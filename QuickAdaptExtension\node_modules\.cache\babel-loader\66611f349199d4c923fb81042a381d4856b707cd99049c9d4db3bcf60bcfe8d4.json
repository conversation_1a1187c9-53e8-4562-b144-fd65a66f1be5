{"ast": null, "code": "import React,{useState}from\"react\";import{<PERSON><PERSON>,<PERSON>,Typo<PERSON>,IconButton,Tooltip}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import DesignServicesIcon from\"@mui/icons-material/DesignServices\";import ViewModuleIcon from\"@mui/icons-material/ViewModule\";import CodeIcon from\"@mui/icons-material/Code\";import PageTrigger from\"./PageTrigger\";import ElementRules from\"./ElementRules\";//import \"./GuideSettings.css\";\n// import Draggable from \"react-draggable\";\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const GuideSetting=()=>{// State to control the visibility of CanvasSettings\nconst[showPageTrigger,setShowPageTrigger]=useState(false);const[showUrlRules,setShowUrlRules]=useState(false);const[showElementUrls,setShowElementUrls]=useState(false);const[isOpen,setIsOpen]=useState(true);const toggleCanvasSettings=()=>{setShowPageTrigger(!showPageTrigger);};const toggleElementsSettings=()=>{setShowUrlRules(!showUrlRules);};const toggleCustomCSS=()=>{setShowElementUrls(!showElementUrls);// Toggle CustomCSS visibility\n};const handleClose=()=>{setIsOpen(false);// Close the popup when close button is clicked\n};if(!isOpen)return null;return/*#__PURE__*/(//<Draggable>\n_jsxs(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:\"Settings\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Typography,{sx:{color:\"var(--primarycolor)\",position:\"relative\",fontSize:\"14px\"},children:[\"settings for current page\",\" \"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[\"    \",/*#__PURE__*/_jsx(Button,{fullWidth:true,sx:{justifyContent:\"flex-start\",backgroundColor:\"#ede8e7\",color:\"#495e58\",textTransform:\"none\",marginBottom:\"8px\",borderRadius:\"12px\",padding:\"8px\",\":hover\":{backgroundColor:\"#d8d4d2\"}},onClick:toggleCanvasSettings// Show/Hide CanvasSettings\n,startIcon:/*#__PURE__*/_jsx(DesignServicesIcon,{}),children:\"On Page Trigger\"}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Coming Soon\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(\"span\",{children:[\" \",/*#__PURE__*/_jsx(Button,{fullWidth:true,disabled:true,sx:{justifyContent:\"flex-start\",backgroundColor:\"#ede8e7\",color:\"#495e58\",textTransform:\"none\",marginBottom:\"8px\",borderRadius:\"12px\",padding:\"8px\",\":hover\":{backgroundColor:\"#d8d4d2\"}},onClick:toggleElementsSettings,startIcon:/*#__PURE__*/_jsx(ViewModuleIcon,{}),children:\"Url Rules\"})]})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Coming Soon\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsxs(\"span\",{children:[\" \",/*#__PURE__*/_jsx(Button,{fullWidth:true,disabled:true,sx:{justifyContent:\"flex-start\",backgroundColor:\"#ede8e7\",color:\"#495e58\",textTransform:\"none\",borderRadius:\"12px\",padding:\"8px\",\":hover\":{backgroundColor:\"#d8d4d2\"}},onClick:toggleCustomCSS,startIcon:/*#__PURE__*/_jsx(CodeIcon,{}),children:\"Element Rules\"})]})})]})]}),showPageTrigger&&/*#__PURE__*/_jsx(Box,{sx:{marginTop:\"16px\",// Add some margin to separate from buttons\nborder:\"1px solid #ddd\",// Optional styling for CanvasSettings container\npadding:\"8px\",position:\"relative\",bottom:\"200px\",zIndex:9999},children:/*#__PURE__*/_jsx(PageTrigger,{})}),showUrlRules&&/*#__PURE__*/_jsx(Box,{sx:{marginTop:\"16px\",// Add some margin to separate from buttons\nborder:\"1px solid #ddd\",// Optional styling for CanvasSettings container\npadding:\"8px\",position:\"relative\",bottom:\"100px\",zIndex:9999}}),showElementUrls&&/*#__PURE__*/_jsx(Box,{sx:{marginTop:\"16px\",// Add some margin to separate from buttons\nborder:\"1px solid #ddd\",// Optional styling for CustomCSS container\npadding:\"8px\",position:\"relative\",bottom:\"100px\",zIndex:9999},children:/*#__PURE__*/_jsx(ElementRules,{})})]})//\t</Draggable>\n);};export default GuideSetting;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "CloseIcon", "DesignServicesIcon", "ViewModuleIcon", "CodeIcon", "PageTrigger", "ElementRules", "jsx", "_jsx", "jsxs", "_jsxs", "GuideSetting", "showPageTrigger", "setShowPageTrigger", "showUrlRules", "setShowUrlRules", "showElementUrls", "setShowElementUrls", "isOpen", "setIsOpen", "toggleCanvasSettings", "toggleElementsSettings", "toggleCustomCSS", "handleClose", "id", "className", "children", "size", "onClick", "sx", "color", "position", "fontSize", "fullWidth", "justifyContent", "backgroundColor", "textTransform", "marginBottom", "borderRadius", "padding", "startIcon", "arrow", "title", "PopperProps", "zIndex", "disabled", "marginTop", "border", "bottom"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/GuideSettings.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, Typography, IconButton, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport DesignServicesIcon from \"@mui/icons-material/DesignServices\";\r\nimport ViewModuleIcon from \"@mui/icons-material/ViewModule\";\r\nimport CodeIcon from \"@mui/icons-material/Code\";\r\nimport PageTrigger from \"./PageTrigger\";\r\nimport ElementRules from \"./ElementRules\";\r\n\r\n//import \"./GuideSettings.css\";\r\n// import Draggable from \"react-draggable\";\r\n\r\nconst GuideSetting = () => {\r\n\t// State to control the visibility of CanvasSettings\r\n\tconst [showPageTrigger, setShowPageTrigger] = useState(false);\r\n\tconst [showUrlRules, setShowUrlRules] = useState(false);\r\n\tconst [showElementUrls, setShowElementUrls] = useState(false);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\tconst toggleCanvasSettings = () => {\r\n\t\tsetShowPageTrigger(!showPageTrigger);\r\n\t};\r\n\r\n\tconst toggleElementsSettings = () => {\r\n\t\tsetShowUrlRules(!showUrlRules);\r\n\t};\r\n\r\n\tconst toggleCustomCSS = () => {\r\n\t\tsetShowElementUrls(!showElementUrls); // Toggle CustomCSS visibility\r\n\t};\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t{/* Header with title and close button */}\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">Settings</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tsettings for current page{\" \"}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-controls\">\t\t\t\t{/* Buttons with icons */}\r\n\t\t\t\t<Button\r\n\t\t\t\t\tfullWidth\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\tbackgroundColor: \"#ede8e7\",\r\n\t\t\t\t\t\tcolor: \"#495e58\",\r\n\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\":hover\": {\r\n\t\t\t\t\t\t\tbackgroundColor: \"#d8d4d2\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tonClick={toggleCanvasSettings} // Show/Hide CanvasSettings\r\n\t\t\t\t\tstartIcon={<DesignServicesIcon />}\r\n\t\t\t\t>\r\n\t\t\t\t\tOn Page Trigger\r\n\t\t\t\t</Button>\r\n\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t{/* Wrapper for the disabled Button */}\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#ede8e7\",\r\n\t\t\t\t\t\t\t\tcolor: \"#495e58\",\r\n\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\":hover\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#d8d4d2\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={toggleElementsSettings}\r\n\t\t\t\t\t\t\tstartIcon={<ViewModuleIcon />}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tUrl Rules\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<span>\r\n\t\t\t\t\t\t{\" \"}\r\n\t\t\t\t\t\t{/* Wrapper for the disabled Button */}\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#ede8e7\",\r\n\t\t\t\t\t\t\t\tcolor: \"#495e58\",\r\n\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\":hover\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#d8d4d2\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={toggleCustomCSS}\r\n\t\t\t\t\t\t\tstartIcon={<CodeIcon />}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tElement Rules\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t</div>\r\n\t\t\t{/* Conditionally render CanvasSettings */}\r\n\t\t\t{showPageTrigger && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tmarginTop: \"16px\", // Add some margin to separate from buttons\r\n\t\t\t\t\t\tborder: \"1px solid #ddd\", // Optional styling for CanvasSettings container\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\tbottom: \"200px\",\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<PageTrigger />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showUrlRules && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tmarginTop: \"16px\", // Add some margin to separate from buttons\r\n\t\t\t\t\t\tborder: \"1px solid #ddd\", // Optional styling for CanvasSettings container\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\tbottom: \"100px\",\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* <Elementssettings /> */}\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showElementUrls && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tmarginTop: \"16px\", // Add some margin to separate from buttons\r\n\t\t\t\t\t\tborder: \"1px solid #ddd\", // Optional styling for CustomCSS container\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\tbottom: \"100px\",\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<ElementRules />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t\t//\t</Draggable>\r\n\t);\r\n};\r\n\r\nexport default GuideSetting;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,GAAG,CAAEC,UAAU,CAAEC,UAAU,CAAEC,OAAO,KAAQ,eAAe,CAC5E,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CAEzC;AACA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CAC1B;AACA,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACuB,MAAM,CAAEC,SAAS,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAE1C,KAAM,CAAAyB,oBAAoB,CAAGA,CAAA,GAAM,CAClCP,kBAAkB,CAAC,CAACD,eAAe,CAAC,CACrC,CAAC,CAED,KAAM,CAAAS,sBAAsB,CAAGA,CAAA,GAAM,CACpCN,eAAe,CAAC,CAACD,YAAY,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAQ,eAAe,CAAGA,CAAA,GAAM,CAC7BL,kBAAkB,CAAC,CAACD,eAAe,CAAC,CAAE;AACvC,CAAC,CACD,KAAM,CAAAO,WAAW,CAAGA,CAAA,GAAM,CACzBJ,SAAS,CAAC,KAAK,CAAC,CAAE;AACnB,CAAC,CAED,GAAI,CAACD,MAAM,CAAE,MAAO,KAAI,CAExB,mBACC;AACAR,KAAA,QACCc,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7BhB,KAAA,QAAKe,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE7BhB,KAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnClB,IAAA,QAAKiB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC3ClB,IAAA,CAACT,UAAU,EACV4B,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBC,OAAO,CAAEL,WAAY,CAAAG,QAAA,cAErBlB,IAAA,CAACP,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNO,IAAA,QAAAkB,QAAA,cACChB,KAAA,CAACZ,UAAU,EACV+B,EAAE,CAAE,CACHC,KAAK,CAAE,qBAAqB,CAC5BC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,MACX,CAAE,CAAAN,QAAA,EACF,2BACyB,CAAC,GAAG,EAClB,CAAC,CACT,CAAC,cACNhB,KAAA,QAAKe,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,MAAI,cACpClB,IAAA,CAACZ,MAAM,EACNqC,SAAS,MACTJ,EAAE,CAAE,CACHK,cAAc,CAAE,YAAY,CAC5BC,eAAe,CAAE,SAAS,CAC1BL,KAAK,CAAE,SAAS,CAChBM,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,CACTJ,eAAe,CAAE,SAClB,CACD,CAAE,CACFP,OAAO,CAAER,oBAAsB;AAAA,CAC/BoB,SAAS,cAAEhC,IAAA,CAACN,kBAAkB,GAAE,CAAE,CAAAwB,QAAA,CAClC,iBAED,CAAQ,CAAC,cACTlB,IAAA,CAACR,OAAO,EAACyC,KAAK,MACbC,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAE,CACZd,EAAE,CAAE,CACHe,MAAM,CAAE,IACT,CACD,CAAE,CAAAlB,QAAA,cAEFhB,KAAA,SAAAgB,QAAA,EACE,GAAG,cAEJlB,IAAA,CAACZ,MAAM,EACNqC,SAAS,MACTY,QAAQ,MACRhB,EAAE,CAAE,CACHK,cAAc,CAAE,YAAY,CAC5BC,eAAe,CAAE,SAAS,CAC1BL,KAAK,CAAE,SAAS,CAChBM,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,CACTJ,eAAe,CAAE,SAClB,CACD,CAAE,CACFP,OAAO,CAAEP,sBAAuB,CAChCmB,SAAS,cAAEhC,IAAA,CAACL,cAAc,GAAE,CAAE,CAAAuB,QAAA,CAC9B,WAED,CAAQ,CAAC,EACJ,CAAC,CACC,CAAC,cACVlB,IAAA,CAACR,OAAO,EAACyC,KAAK,MACbC,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAE,CACZd,EAAE,CAAE,CACHe,MAAM,CAAE,IACT,CACD,CAAE,CAAAlB,QAAA,cAEFhB,KAAA,SAAAgB,QAAA,EACE,GAAG,cAEJlB,IAAA,CAACZ,MAAM,EACNqC,SAAS,MACTY,QAAQ,MACRhB,EAAE,CAAE,CACHK,cAAc,CAAE,YAAY,CAC5BC,eAAe,CAAE,SAAS,CAC1BL,KAAK,CAAE,SAAS,CAChBM,aAAa,CAAE,MAAM,CACrBE,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,CACTJ,eAAe,CAAE,SAClB,CACD,CAAE,CACFP,OAAO,CAAEN,eAAgB,CACzBkB,SAAS,cAAEhC,IAAA,CAACJ,QAAQ,GAAE,CAAE,CAAAsB,QAAA,CACxB,eAED,CAAQ,CAAC,EACJ,CAAC,CACE,CAAC,EACL,CAAC,EAEH,CAAC,CAELd,eAAe,eACfJ,IAAA,CAACX,GAAG,EACHgC,EAAE,CAAE,CACHiB,SAAS,CAAE,MAAM,CAAE;AACnBC,MAAM,CAAE,gBAAgB,CAAE;AAC1BR,OAAO,CAAE,KAAK,CACdR,QAAQ,CAAE,UAAU,CACpBiB,MAAM,CAAE,OAAO,CACfJ,MAAM,CAAE,IACT,CAAE,CAAAlB,QAAA,cAEFlB,IAAA,CAACH,WAAW,GAAE,CAAC,CACX,CACL,CACAS,YAAY,eACZN,IAAA,CAACX,GAAG,EACHgC,EAAE,CAAE,CACHiB,SAAS,CAAE,MAAM,CAAE;AACnBC,MAAM,CAAE,gBAAgB,CAAE;AAC1BR,OAAO,CAAE,KAAK,CACdR,QAAQ,CAAE,UAAU,CACpBiB,MAAM,CAAE,OAAO,CACfJ,MAAM,CAAE,IACT,CAAE,CAGE,CACL,CACA5B,eAAe,eACfR,IAAA,CAACX,GAAG,EACHgC,EAAE,CAAE,CACHiB,SAAS,CAAE,MAAM,CAAE;AACnBC,MAAM,CAAE,gBAAgB,CAAE;AAC1BR,OAAO,CAAE,KAAK,CACdR,QAAQ,CAAE,UAAU,CACpBiB,MAAM,CAAE,OAAO,CACfJ,MAAM,CAAE,IACT,CAAE,CAAAlB,QAAA,cAEFlB,IAAA,CAACF,YAAY,GAAE,CAAC,CACZ,CACL,EACG,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}