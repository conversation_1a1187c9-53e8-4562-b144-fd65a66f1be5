import { useState, useEffect } from 'react';

interface ExtensionInitializationState {
  isInitializing: boolean;
  isFirstLoad: boolean;
  initializationComplete: boolean;
}

export const useExtensionInitialization = () => {
  const [state, setState] = useState<ExtensionInitializationState>({
    isInitializing: true,
    isFirstLoad: true,
    initializationComplete: false
  });

  useEffect(() => {
    // Check if this is the first time the extension is being loaded
    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');
    
    if (!hasBeenInitialized) {
      // First time loading - show popup loader
      setState(prev => ({
        ...prev,
        isFirstLoad: true,
        isInitializing: true
      }));

      // Simulate initialization process
      const initializationSteps = [
        { delay: 500, message: 'Loading extension...' },
        { delay: 1000, message: 'Initializing components...' },
        { delay: 1500, message: 'Setting up environment...' },
        { delay: 2000, message: 'Almost ready...' },
        { delay: 2500, message: 'Complete!' }
      ];

      // Complete initialization after all steps
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          isInitializing: false,
          initializationComplete: true
        }));
        
        // Mark as initialized in session storage
        sessionStorage.setItem('quickadapt-initialized', 'true');
      }, 3000);

    } else {
      // Already initialized in this session - skip popup
      setState({
        isInitializing: false,
        isFirstLoad: false,
        initializationComplete: true
      });
    }
  }, []);

  const resetInitialization = () => {
    sessionStorage.removeItem('quickadapt-initialized');
    setState({
      isInitializing: true,
      isFirstLoad: true,
      initializationComplete: false
    });
  };

  const hideInitializationLoader = () => {
    setState(prev => ({
      ...prev,
      isInitializing: false
    }));
  };

  return {
    ...state,
    resetInitialization,
    hideInitializationLoader
  };
};
