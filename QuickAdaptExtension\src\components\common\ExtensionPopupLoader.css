/* Extension Popup Loader */
.extension-popup-loader {
  position: fixed;
  z-index: 999999;
  width: 280px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Positioning */
.extension-popup-loader.top-right {
  top: 20px;
  right: 20px;
}

.extension-popup-loader.top-left {
  top: 20px;
  left: 20px;
}

.extension-popup-loader.bottom-right {
  bottom: 20px;
  right: 20px;
}

.extension-popup-loader.bottom-left {
  bottom: 20px;
  left: 20px;
}

/* Visibility states */
.extension-popup-loader.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.extension-popup-loader.hidden {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  pointer-events: none;
}

/* Main container */
.popup-loader-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* Header */
.popup-loader-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 8px 16px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
}

.popup-loader-icon {
  display: flex;
  align-items: center;
}

.icon-spinner {
  display: flex;
  gap: 3px;
  align-items: center;
}

.spinner-dot {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.spinner-dot:nth-child(1) { animation-delay: -0.32s; }
.spinner-dot:nth-child(2) { animation-delay: -0.16s; }
.spinner-dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.popup-close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.popup-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Content */
.popup-loader-content {
  padding: 16px;
}

.popup-loader-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

/* Main content spinner */
.popup-main-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16px 0 12px 0;
}

.popup-main-spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.popup-main-spinner .spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.popup-main-spinner .spinner-ring:nth-child(1) {
  border-top-color: #4f46e5;
  animation-delay: -0.45s;
}

.popup-main-spinner .spinner-ring:nth-child(2) {
  border-top-color: #7c3aed;
  animation-delay: -0.3s;
}

.popup-main-spinner .spinner-ring:nth-child(3) {
  border-top-color: #ec4899;
  animation-delay: -0.15s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.popup-loader-message {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.4;
  text-align: center;
}

/* Progress bar */
.popup-progress-container {
  width: 100%;
  height: 4px;
  background: #f3f4f6;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.popup-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.popup-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.popup-loader-status {
  font-size: 11px;
  color: #9ca3af;
  text-align: center;
  font-weight: 500;
}

/* Footer */
.popup-loader-footer {
  padding: 8px 16px;
  background: #f9fafb;
  border-top: 1px solid #f3f4f6;
  display: flex;
  justify-content: center;
}

.extension-badge {
  background: #e5e7eb;
  color: #6b7280;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .extension-popup-loader {
    width: 260px;
  }
  
  .extension-popup-loader.top-right,
  .extension-popup-loader.bottom-right {
    right: 10px;
  }
  
  .extension-popup-loader.top-left,
  .extension-popup-loader.bottom-left {
    left: 10px;
  }
  
  .extension-popup-loader.top-right,
  .extension-popup-loader.top-left {
    top: 10px;
  }
  
  .extension-popup-loader.bottom-right,
  .extension-popup-loader.bottom-left {
    bottom: 10px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .popup-loader-container {
    background: #1f2937;
    border-color: #374151;
  }

  .popup-loader-title {
    color: #f9fafb;
  }

  .popup-loader-message {
    color: #d1d5db;
  }

  .popup-main-spinner .spinner-ring:nth-child(1) {
    border-top-color: #6366f1;
  }

  .popup-main-spinner .spinner-ring:nth-child(2) {
    border-top-color: #8b5cf6;
  }

  .popup-main-spinner .spinner-ring:nth-child(3) {
    border-top-color: #f472b6;
  }

  .popup-progress-container {
    background: #374151;
  }

  .popup-loader-status {
    color: #9ca3af;
  }

  .popup-loader-footer {
    background: #111827;
    border-color: #374151;
  }

  .extension-badge {
    background: #374151;
    color: #d1d5db;
  }
}
