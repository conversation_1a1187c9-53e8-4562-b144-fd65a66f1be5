/* Extension Popup Loader */
.extension-popup-loader {
  position: fixed;
  z-index: 999999;
  width: 120px;
  height: 120px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Positioning */
.extension-popup-loader.top-right {
  top: 20px;
  right: 20px;
}

.extension-popup-loader.top-left {
  top: 20px;
  left: 20px;
}

.extension-popup-loader.bottom-right {
  bottom: 20px;
  right: 20px;
}

.extension-popup-loader.bottom-left {
  bottom: 20px;
  left: 20px;
}

/* Visibility states */
.extension-popup-loader.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.extension-popup-loader.hidden {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  pointer-events: none;
}

/* Main container */
.popup-loader-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Minimal close button */
.popup-close-btn-minimal {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;
}

.popup-close-btn-minimal:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* Lottie Content */
.popup-lottie-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
}



/* Responsive adjustments */
@media (max-width: 480px) {
  .extension-popup-loader {
    width: 100px;
    height: 100px;
  }

  .extension-popup-loader.top-right,
  .extension-popup-loader.bottom-right {
    right: 10px;
  }

  .extension-popup-loader.top-left,
  .extension-popup-loader.bottom-left {
    left: 10px;
  }

  .extension-popup-loader.top-right,
  .extension-popup-loader.top-left {
    top: 10px;
  }

  .extension-popup-loader.bottom-right,
  .extension-popup-loader.bottom-left {
    bottom: 10px;
  }

  .popup-lottie-content {
    padding: 15px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .popup-loader-container {
    background: rgba(31, 41, 55, 0.95);
    border-color: #374151;
  }

  .popup-close-btn-minimal {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }

  .popup-close-btn-minimal:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}
