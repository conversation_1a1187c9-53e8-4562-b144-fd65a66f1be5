{"ast": null, "code": "import React from'react';import'./ExtensionLoader.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExtensionLoader=_ref=>{let{message=\"Loading QuickAdapt Extension...\",size='large',overlay=true}=_ref;const containerClass=`extension-loader-container ${size}`;const spinnerClass=`extension-loader-spinner ${size}`;const content=/*#__PURE__*/_jsx(\"div\",{className:containerClass,children:/*#__PURE__*/_jsxs(\"div\",{className:\"extension-loader-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:spinnerClass,children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner-ring\"}),/*#__PURE__*/_jsx(\"div\",{className:\"spinner-ring\"}),/*#__PURE__*/_jsx(\"div\",{className:\"spinner-ring\"}),/*#__PURE__*/_jsx(\"div\",{className:\"spinner-ring\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"extension-loader-text\",children:[/*#__PURE__*/_jsx(\"h3\",{children:message}),/*#__PURE__*/_jsxs(\"div\",{className:\"loading-dots\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"span\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"span\",{className:\"dot\"})]})]})]})});if(overlay){return/*#__PURE__*/_jsx(\"div\",{className:\"extension-loader-overlay\",children:content});}return content;};export default ExtensionLoader;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "message", "size", "overlay", "containerClass", "spinnerClass", "content", "className", "children"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionLoader.tsx"], "sourcesContent": ["import React from 'react';\nimport './ExtensionLoader.css';\n\ninterface ExtensionLoaderProps {\n  message?: string;\n  size?: 'small' | 'medium' | 'large';\n  overlay?: boolean;\n}\n\nconst ExtensionLoader: React.FC<ExtensionLoaderProps> = ({\n  message = \"Loading QuickAdapt Extension...\",\n  size = 'large',\n  overlay = true\n}) => {\n  const containerClass = `extension-loader-container ${size}`;\n  const spinnerClass = `extension-loader-spinner ${size}`;\n\n  const content = (\n    <div className={containerClass}>\n      <div className=\"extension-loader-content\">\n        {/* Spinner */}\n        <div className={spinnerClass}>\n          <div className=\"spinner-ring\"></div>\n          <div className=\"spinner-ring\"></div>\n          <div className=\"spinner-ring\"></div>\n          <div className=\"spinner-ring\"></div>\n        </div>\n\n        {/* Loading text */}\n        <div className=\"extension-loader-text\">\n          <h3>{message}</h3>\n          <div className=\"loading-dots\">\n            <span className=\"dot\"></span>\n            <span className=\"dot\"></span>\n            <span className=\"dot\"></span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (overlay) {\n    return (\n      <div className=\"extension-loader-overlay\">\n        {content}\n      </div>\n    );\n  }\n\n  return content;\n};\n\nexport default ExtensionLoader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ/B,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAIlD,IAJmD,CACvDC,OAAO,CAAG,iCAAiC,CAC3CC,IAAI,CAAG,OAAO,CACdC,OAAO,CAAG,IACZ,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,cAAc,CAAG,8BAA8BF,IAAI,EAAE,CAC3D,KAAM,CAAAG,YAAY,CAAG,4BAA4BH,IAAI,EAAE,CAEvD,KAAM,CAAAI,OAAO,cACXV,IAAA,QAAKW,SAAS,CAAEH,cAAe,CAAAI,QAAA,cAC7BV,KAAA,QAAKS,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eAEvCV,KAAA,QAAKS,SAAS,CAAEF,YAAa,CAAAG,QAAA,eAC3BZ,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAM,CAAC,cACpCX,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAM,CAAC,cACpCX,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAM,CAAC,cACpCX,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAM,CAAC,EACjC,CAAC,cAGNT,KAAA,QAAKS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCZ,IAAA,OAAAY,QAAA,CAAKP,OAAO,CAAK,CAAC,cAClBH,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,SAAMW,SAAS,CAAC,KAAK,CAAO,CAAC,cAC7BX,IAAA,SAAMW,SAAS,CAAC,KAAK,CAAO,CAAC,cAC7BX,IAAA,SAAMW,SAAS,CAAC,KAAK,CAAO,CAAC,EAC1B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAED,GAAIJ,OAAO,CAAE,CACX,mBACEP,IAAA,QAAKW,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtCF,OAAO,CACL,CAAC,CAEV,CAEA,MAAO,CAAAA,OAAO,CAChB,CAAC,CAED,cAAe,CAAAP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}