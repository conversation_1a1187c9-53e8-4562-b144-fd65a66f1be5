{"ast": null, "code": "import React,{useEffect,useState}from'react';import\"./App.scss\";import Drawer from\"./components/drawer/Drawer\";import{AuthProvider}from\"./components/auth/AuthProvider\";import{AccountProvider}from\"./components/login/AccountContext\";import{SnackbarProvider}from\"./components/guideSetting/guideList/SnackbarContext\";import{TranslationProvider}from\"./contexts/TranslationContext\";import jwtDecode from\"jwt-decode\";import useInfoStore from\"./store/UserInfoStore\";import{initializeI18n}from\"./multilinguial/i18n\";import ExtensionLoader from\"./components/common/ExtensionLoader\";import ExtensionPopupLoader from\"./components/common/ExtensionPopupLoader\";import{useExtensionInitialization}from\"./hooks/useExtensionInitialization\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[isI18nReady,setIsI18nReady]=useState(false);const[isLoginPageReady,setIsLoginPageReady]=useState(false);const accessToken=useInfoStore(state=>state.accessToken);const{clearAll,clearAccessToken}=useInfoStore.getState();const{isInitializing:isExtensionInitializing,isFirstLoad,hideInitializationLoader}=useExtensionInitialization();// Initialize i18n once when app starts\nuseEffect(()=>{const setupI18n=async()=>{try{await initializeI18n();console.log('✅ i18n ready for use');setIsI18nReady(true);}catch(error){console.error('❌ Failed to initialize i18n:',error);// Set ready anyway to prevent infinite loading\nsetIsI18nReady(true);}};setupI18n();},[]);// Check token validity and login page readiness\nuseEffect(()=>{if(accessToken){try{const decodedToken=jwtDecode(accessToken);const currentTime=Math.floor(Date.now()/1000);if(decodedToken.exp<currentTime){console.log('🔐 Token expired, clearing session');clearAll();clearAccessToken();}}catch(error){console.error('❌ Invalid token, clearing session:',error);clearAll();clearAccessToken();}}// Set login page ready state - if no token, login page will be shown\n// If i18n is ready and we don't have a valid token, login page is ready\nif(isI18nReady&&!accessToken){setIsLoginPageReady(true);// Hide the initialization loader when login page is ready\nhideInitializationLoader();}},[accessToken,clearAll,clearAccessToken,isI18nReady,hideInitializationLoader]);// Show loading until i18n is ready\nif(!isI18nReady){return/*#__PURE__*/_jsx(ExtensionLoader,{message:\"Initializing QuickAdapt Extension...\"});}return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[isFirstLoad&&isExtensionInitializing&&!isLoginPageReady&&/*#__PURE__*/_jsx(ExtensionPopupLoader,{message:\"Extension is starting up...\",duration:3000,position:\"top-right\"}),/*#__PURE__*/_jsx(TranslationProvider,{children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AccountProvider,{children:/*#__PURE__*/_jsx(SnackbarProvider,{children:/*#__PURE__*/_jsx(Drawer,{})})})})})]});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Drawer", "<PERSON>th<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>", "SnackbarProvider", "TranslationProvider", "jwtDecode", "useInfoStore", "initializeI18n", "<PERSON><PERSON><PERSON><PERSON>", "ExtensionPopup<PERSON><PERSON>der", "useExtensionInitialization", "jsx", "_jsx", "jsxs", "_jsxs", "App", "isI18nReady", "setIsI18nReady", "isLoginPageReady", "setIsLoginPageReady", "accessToken", "state", "clearAll", "clearAccessToken", "getState", "isInitializing", "isExtensionInitializing", "isFirstLoad", "hideInitializationLoader", "setupI18n", "console", "log", "error", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "message", "className", "children", "duration", "position"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport \"./App.scss\";\r\nimport GuidePopup from \"./components/guideSetting/GuidePopUp\";\r\nimport Drawer from \"./components/drawer/Drawer\";\r\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\r\nimport { AccountProvider } from \"./components/login/AccountContext\";\r\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\r\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\r\nimport Rte from \"./components/guideSetting/RTE\";\r\nimport jwtDecode from \"jwt-decode\";\r\nimport useInfoStore from \"./store/UserInfoStore\";\r\nimport { initializeI18n } from \"./multilinguial/i18n\";\r\nimport ExtensionLoader from \"./components/common/ExtensionLoader\";\r\nimport ExtensionPopupLoader from \"./components/common/ExtensionPopupLoader\";\r\nimport { useExtensionInitialization } from \"./hooks/useExtensionInitialization\";\r\n\r\nfunction App() {\r\n\tconst [isI18nReady, setIsI18nReady] = useState(false);\r\n\tconst [isLoginPageReady, setIsLoginPageReady] = useState(false);\r\n\tconst accessToken = useInfoStore((state) => state.accessToken);\r\n\tconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\n\tconst { isInitializing: isExtensionInitializing, isFirstLoad, hideInitializationLoader } = useExtensionInitialization();\r\n\r\n\t// Initialize i18n once when app starts\r\n\tuseEffect(() => {\r\n\t\tconst setupI18n = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait initializeI18n();\r\n\t\t\t\tconsole.log('✅ i18n ready for use');\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Failed to initialize i18n:', error);\r\n\t\t\t\t// Set ready anyway to prevent infinite loading\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tsetupI18n();\r\n\t}, []);\r\n\r\n\t// Check token validity and login page readiness\r\n\tuseEffect(() => {\r\n\t\tif (accessToken) {\r\n\t\t\ttry {\r\n\t\t\t\tconst decodedToken: any = jwtDecode(accessToken);\r\n\t\t\t\tconst currentTime = Math.floor(Date.now() / 1000);\r\n\t\t\t\tif (decodedToken.exp < currentTime) {\r\n\t\t\t\t\tconsole.log('🔐 Token expired, clearing session');\r\n\t\t\t\t\tclearAll();\r\n\t\t\t\t\tclearAccessToken();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Invalid token, clearing session:', error);\r\n\t\t\t\tclearAll();\r\n\t\t\t\tclearAccessToken();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Set login page ready state - if no token, login page will be shown\r\n\t\t// If i18n is ready and we don't have a valid token, login page is ready\r\n\t\tif (isI18nReady && !accessToken) {\r\n\t\t\tsetIsLoginPageReady(true);\r\n\t\t\t// Hide the initialization loader when login page is ready\r\n\t\t\thideInitializationLoader();\r\n\t\t}\r\n\t}, [accessToken, clearAll, clearAccessToken, isI18nReady, hideInitializationLoader]);\r\n\r\n\t// Show loading until i18n is ready\r\n\tif (!isI18nReady) {\r\n\t\treturn <ExtensionLoader message=\"Initializing QuickAdapt Extension...\" />;\r\n\t}\r\n\r\n\treturn (\r\n\t\t<div className=\"App\">\r\n\t\t\t{/* Show popup loader on first extension load, but hide when login page is ready */}\r\n\t\t\t{isFirstLoad && isExtensionInitializing && !isLoginPageReady && (\r\n\t\t\t\t<ExtensionPopupLoader\r\n\t\t\t\t\tmessage=\"Extension is starting up...\"\r\n\t\t\t\t\tduration={3000}\r\n\t\t\t\t\tposition=\"top-right\"\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t<TranslationProvider>\r\n\t\t\t\t<AuthProvider>\r\n\t\t\t\t\t<AccountProvider>\r\n\t\t\t\t\t\t<SnackbarProvider>\r\n\t\t\t\t\t\t\t<Drawer />\r\n\t\t\t\t\t\t</SnackbarProvider>\r\n\t\t\t\t\t</AccountProvider>\r\n\t\t\t\t</AuthProvider>\r\n\t\t\t</TranslationProvider>\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,YAAY,CAEnB,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,OAASC,YAAY,KAAQ,gCAAgC,CAC7D,OAASC,eAAe,KAAQ,mCAAmC,CACnE,OAASC,gBAAgB,KAAQ,qDAAqD,CACtF,OAASC,mBAAmB,KAAQ,+BAA+B,CAEnE,MAAO,CAAAC,SAAS,KAAM,YAAY,CAClC,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,OAASC,cAAc,KAAQ,sBAAsB,CACrD,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CACjE,MAAO,CAAAC,oBAAoB,KAAM,0CAA0C,CAC3E,OAASC,0BAA0B,KAAQ,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhF,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACd,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAAqB,WAAW,CAAGd,YAAY,CAAEe,KAAK,EAAKA,KAAK,CAACD,WAAW,CAAC,CAC9D,KAAM,CAAEE,QAAQ,CAAEC,gBAAiB,CAAC,CAAGjB,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,uBAAuB,CAAEC,WAAW,CAAEC,wBAAyB,CAAC,CAAGlB,0BAA0B,CAAC,CAAC,CAEvH;AACAZ,SAAS,CAAC,IAAM,CACf,KAAM,CAAA+B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACH,KAAM,CAAAtB,cAAc,CAAC,CAAC,CACtBuB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnCd,cAAc,CAAC,IAAI,CAAC,CACrB,CAAE,MAAOe,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD;AACAf,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAEDY,SAAS,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA/B,SAAS,CAAC,IAAM,CACf,GAAIsB,WAAW,CAAE,CAChB,GAAI,CACH,KAAM,CAAAa,YAAiB,CAAG5B,SAAS,CAACe,WAAW,CAAC,CAChD,KAAM,CAAAc,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CACjD,GAAIL,YAAY,CAACM,GAAG,CAAGL,WAAW,CAAE,CACnCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDT,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CAAE,MAAOS,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1DV,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CAEA;AACA;AACA,GAAIP,WAAW,EAAI,CAACI,WAAW,CAAE,CAChCD,mBAAmB,CAAC,IAAI,CAAC,CACzB;AACAS,wBAAwB,CAAC,CAAC,CAC3B,CACD,CAAC,CAAE,CAACR,WAAW,CAAEE,QAAQ,CAAEC,gBAAgB,CAAEP,WAAW,CAAEY,wBAAwB,CAAC,CAAC,CAEpF;AACA,GAAI,CAACZ,WAAW,CAAE,CACjB,mBAAOJ,IAAA,CAACJ,eAAe,EAACgC,OAAO,CAAC,sCAAsC,CAAE,CAAC,CAC1E,CAEA,mBACC1B,KAAA,QAAK2B,SAAS,CAAC,KAAK,CAAAC,QAAA,EAElBf,WAAW,EAAID,uBAAuB,EAAI,CAACR,gBAAgB,eAC3DN,IAAA,CAACH,oBAAoB,EACpB+B,OAAO,CAAC,6BAA6B,CACrCG,QAAQ,CAAE,IAAK,CACfC,QAAQ,CAAC,WAAW,CACpB,CACD,cAEDhC,IAAA,CAACR,mBAAmB,EAAAsC,QAAA,cACnB9B,IAAA,CAACX,YAAY,EAAAyC,QAAA,cACZ9B,IAAA,CAACV,eAAe,EAAAwC,QAAA,cACf9B,IAAA,CAACT,gBAAgB,EAAAuC,QAAA,cAChB9B,IAAA,CAACZ,MAAM,GAAE,CAAC,CACO,CAAC,CACH,CAAC,CACL,CAAC,CACK,CAAC,EAClB,CAAC,CAER,CAEA,cAAe,CAAAe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}