{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport \"./App.scss\";\nimport Drawer from \"./components/drawer/Drawer\";\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\nimport { AccountProvider } from \"./components/login/AccountContext\";\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\nimport jwtDecode from \"jwt-decode\";\nimport useInfoStore from \"./store/UserInfoStore\";\nimport { initializeI18n } from \"./multilinguial/i18n\";\nimport ExtensionPopupLoader from \"./components/common/ExtensionPopupLoader\";\nimport { useExtensionInitialization } from \"./hooks/useExtensionInitialization\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isI18nReady, setIsI18nReady] = useState(false);\n  const accessToken = useInfoStore(state => state.accessToken);\n  const {\n    clearAll,\n    clearAccessToken\n  } = useInfoStore.getState();\n  const {\n    isInitializing: isExtensionInitializing,\n    isFirstLoad\n  } = useExtensionInitialization();\n\n  // Initialize i18n once when app starts\n  useEffect(() => {\n    const setupI18n = async () => {\n      try {\n        await initializeI18n();\n        console.log('✅ i18n ready for use');\n        setIsI18nReady(true);\n      } catch (error) {\n        console.error('❌ Failed to initialize i18n:', error);\n        // Set ready anyway to prevent infinite loading\n        setIsI18nReady(true);\n      }\n    };\n    setupI18n();\n  }, []);\n\n  // Check token validity\n  useEffect(() => {\n    if (accessToken) {\n      try {\n        const decodedToken = jwtDecode(accessToken);\n        const currentTime = Math.floor(Date.now() / 1000);\n        if (decodedToken.exp < currentTime) {\n          console.log('🔐 Token expired, clearing session');\n          clearAll();\n          clearAccessToken();\n        }\n      } catch (error) {\n        console.error('❌ Invalid token, clearing session:', error);\n        clearAll();\n        clearAccessToken();\n      }\n    }\n  }, [accessToken, clearAll, clearAccessToken]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [isFirstLoad && isExtensionInitializing && /*#__PURE__*/_jsxDEV(ExtensionPopupLoader, {\n      message: \"Extension is starting up...\",\n      duration: 3000,\n      position: \"top-right\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(TranslationProvider, {\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(AccountProvider, {\n          children: /*#__PURE__*/_jsxDEV(SnackbarProvider, {\n            children: /*#__PURE__*/_jsxDEV(Drawer, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 3\n  }, this);\n}\n_s(App, \"4KTTBADKVib/r4VUhqx/25OFakg=\", false, function () {\n  return [useInfoStore, useExtensionInitialization];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Drawer", "<PERSON>th<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>", "SnackbarProvider", "TranslationProvider", "jwtDecode", "useInfoStore", "initializeI18n", "ExtensionPopup<PERSON><PERSON>der", "useExtensionInitialization", "jsxDEV", "_jsxDEV", "App", "_s", "isI18nReady", "setIsI18nReady", "accessToken", "state", "clearAll", "clearAccessToken", "getState", "isInitializing", "isExtensionInitializing", "isFirstLoad", "setupI18n", "console", "log", "error", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "className", "children", "message", "duration", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport \"./App.scss\";\r\nimport GuidePopup from \"./components/guideSetting/GuidePopUp\";\r\nimport Drawer from \"./components/drawer/Drawer\";\r\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\r\nimport { AccountProvider } from \"./components/login/AccountContext\";\r\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\r\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\r\nimport Rte from \"./components/guideSetting/RTE\";\r\nimport jwtDecode from \"jwt-decode\";\r\nimport useInfoStore from \"./store/UserInfoStore\";\r\nimport { initializeI18n } from \"./multilinguial/i18n\";\r\nimport ExtensionPopupLoader from \"./components/common/ExtensionPopupLoader\";\r\nimport { useExtensionInitialization } from \"./hooks/useExtensionInitialization\";\r\n\r\nfunction App() {\r\n\tconst [isI18nReady, setIsI18nReady] = useState(false);\r\n\tconst accessToken = useInfoStore((state) => state.accessToken);\r\n\tconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\n\tconst { isInitializing: isExtensionInitializing, isFirstLoad } = useExtensionInitialization();\r\n\r\n\t// Initialize i18n once when app starts\r\n\tuseEffect(() => {\r\n\t\tconst setupI18n = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait initializeI18n();\r\n\t\t\t\tconsole.log('✅ i18n ready for use');\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Failed to initialize i18n:', error);\r\n\t\t\t\t// Set ready anyway to prevent infinite loading\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tsetupI18n();\r\n\t}, []);\r\n\r\n\t// Check token validity\r\n\tuseEffect(() => {\r\n\t\tif (accessToken) {\r\n\t\t\ttry {\r\n\t\t\t\tconst decodedToken: any = jwtDecode(accessToken);\r\n\t\t\t\tconst currentTime = Math.floor(Date.now() / 1000);\r\n\t\t\t\tif (decodedToken.exp < currentTime) {\r\n\t\t\t\t\tconsole.log('🔐 Token expired, clearing session');\r\n\t\t\t\t\tclearAll();\r\n\t\t\t\t\tclearAccessToken();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Invalid token, clearing session:', error);\r\n\t\t\t\tclearAll();\r\n\t\t\t\tclearAccessToken();\r\n\t\t\t}\r\n\t\t}\r\n\t}, [accessToken, clearAll, clearAccessToken]);\r\n\r\n\treturn (\r\n\t\t<div className=\"App\">\r\n\t\t\t{/* Show popup loader on first extension load before extension page loads */}\r\n\t\t\t{isFirstLoad && isExtensionInitializing && (\r\n\t\t\t\t<ExtensionPopupLoader\r\n\t\t\t\t\tmessage=\"Extension is starting up...\"\r\n\t\t\t\t\tduration={3000}\r\n\t\t\t\t\tposition=\"top-right\"\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t<TranslationProvider>\r\n\t\t\t\t<AuthProvider>\r\n\t\t\t\t\t<AccountProvider>\r\n\t\t\t\t\t\t<SnackbarProvider>\r\n\t\t\t\t\t\t\t<Drawer />\r\n\t\t\t\t\t\t</SnackbarProvider>\r\n\t\t\t\t\t</AccountProvider>\r\n\t\t\t\t</AuthProvider>\r\n\t\t\t</TranslationProvider>\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,YAAY;AAEnB,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,gBAAgB,QAAQ,qDAAqD;AACtF,SAASC,mBAAmB,QAAQ,+BAA+B;AAEnE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,SAASC,0BAA0B,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMiB,WAAW,GAAGV,YAAY,CAAEW,KAAK,IAAKA,KAAK,CAACD,WAAW,CAAC;EAC9D,MAAM;IAAEE,QAAQ;IAAEC;EAAiB,CAAC,GAAGb,YAAY,CAACc,QAAQ,CAAC,CAAC;EAC9D,MAAM;IAAEC,cAAc,EAAEC,uBAAuB;IAAEC;EAAY,CAAC,GAAGd,0BAA0B,CAAC,CAAC;;EAE7F;EACAX,SAAS,CAAC,MAAM;IACf,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACH,MAAMjB,cAAc,CAAC,CAAC;QACtBkB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnCX,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC,OAAOY,KAAK,EAAE;QACfF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACAZ,cAAc,CAAC,IAAI,CAAC;MACrB;IACD,CAAC;IAEDS,SAAS,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1B,SAAS,CAAC,MAAM;IACf,IAAIkB,WAAW,EAAE;MAChB,IAAI;QACH,MAAMY,YAAiB,GAAGvB,SAAS,CAACW,WAAW,CAAC;QAChD,MAAMa,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACjD,IAAIL,YAAY,CAACM,GAAG,GAAGL,WAAW,EAAE;UACnCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjDR,QAAQ,CAAC,CAAC;UACVC,gBAAgB,CAAC,CAAC;QACnB;MACD,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACfF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1DT,QAAQ,CAAC,CAAC;QACVC,gBAAgB,CAAC,CAAC;MACnB;IACD;EACD,CAAC,EAAE,CAACH,WAAW,EAAEE,QAAQ,EAAEC,gBAAgB,CAAC,CAAC;EAE7C,oBACCR,OAAA;IAAKwB,SAAS,EAAC,KAAK;IAAAC,QAAA,GAElBb,WAAW,IAAID,uBAAuB,iBACtCX,OAAA,CAACH,oBAAoB;MACpB6B,OAAO,EAAC,6BAA6B;MACrCC,QAAQ,EAAE,IAAK;MACfC,QAAQ,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACD,eAEDhC,OAAA,CAACP,mBAAmB;MAAAgC,QAAA,eACnBzB,OAAA,CAACV,YAAY;QAAAmC,QAAA,eACZzB,OAAA,CAACT,eAAe;UAAAkC,QAAA,eACfzB,OAAA,CAACR,gBAAgB;YAAAiC,QAAA,eAChBzB,OAAA,CAACX,MAAM;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAER;AAAC9B,EAAA,CAhEQD,GAAG;EAAA,QAESN,YAAY,EAEiCG,0BAA0B;AAAA;AAAAmC,EAAA,GAJnFhC,GAAG;AAkEZ,eAAeA,GAAG;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}