{"ast": null, "code": "import React,{useEffect,useState}from'react';import\"./App.scss\";import Drawer from\"./components/drawer/Drawer\";import{AuthProvider}from\"./components/auth/AuthProvider\";import{AccountProvider}from\"./components/login/AccountContext\";import{SnackbarProvider}from\"./components/guideSetting/guideList/SnackbarContext\";import{TranslationProvider}from\"./contexts/TranslationContext\";import jwtDecode from\"jwt-decode\";import useInfoStore from\"./store/UserInfoStore\";import{initializeI18n}from\"./multilinguial/i18n\";import ExtensionPopupLoader from\"./components/common/ExtensionPopupLoader\";import{useExtensionInitialization}from\"./hooks/useExtensionInitialization\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[isI18nReady,setIsI18nReady]=useState(false);const accessToken=useInfoStore(state=>state.accessToken);const{clearAll,clearAccessToken}=useInfoStore.getState();const{isInitializing:isExtensionInitializing,isFirstLoad}=useExtensionInitialization();// Initialize i18n once when app starts\nuseEffect(()=>{const setupI18n=async()=>{try{await initializeI18n();console.log('✅ i18n ready for use');setIsI18nReady(true);}catch(error){console.error('❌ Failed to initialize i18n:',error);// Set ready anyway to prevent infinite loading\nsetIsI18nReady(true);}};setupI18n();},[]);// Check token validity\nuseEffect(()=>{if(accessToken){try{const decodedToken=jwtDecode(accessToken);const currentTime=Math.floor(Date.now()/1000);if(decodedToken.exp<currentTime){console.log('🔐 Token expired, clearing session');clearAll();clearAccessToken();}}catch(error){console.error('❌ Invalid token, clearing session:',error);clearAll();clearAccessToken();}}},[accessToken,clearAll,clearAccessToken]);// Show popup loader until i18n is ready or during extension initialization\nconst shouldShowPopupLoader=!isI18nReady||isFirstLoad&&isExtensionInitializing;return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[shouldShowPopupLoader&&/*#__PURE__*/_jsx(ExtensionPopupLoader,{message:!isI18nReady?\"Initializing language system...\":\"Extension is starting up...\",duration:!isI18nReady?5000:3000,position:\"top-right\"}),isI18nReady&&/*#__PURE__*/_jsx(TranslationProvider,{children:/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AccountProvider,{children:/*#__PURE__*/_jsx(SnackbarProvider,{children:/*#__PURE__*/_jsx(Drawer,{})})})})})]});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Drawer", "<PERSON>th<PERSON><PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>", "SnackbarProvider", "TranslationProvider", "jwtDecode", "useInfoStore", "initializeI18n", "ExtensionPopup<PERSON><PERSON>der", "useExtensionInitialization", "jsx", "_jsx", "jsxs", "_jsxs", "App", "isI18nReady", "setIsI18nReady", "accessToken", "state", "clearAll", "clearAccessToken", "getState", "isInitializing", "isExtensionInitializing", "isFirstLoad", "setupI18n", "console", "log", "error", "decodedToken", "currentTime", "Math", "floor", "Date", "now", "exp", "shouldShowPopupLoader", "className", "children", "message", "duration", "position"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport \"./App.scss\";\r\nimport GuidePopup from \"./components/guideSetting/GuidePopUp\";\r\nimport Drawer from \"./components/drawer/Drawer\";\r\nimport { AuthProvider } from \"./components/auth/AuthProvider\";\r\nimport { AccountProvider } from \"./components/login/AccountContext\";\r\nimport { SnackbarProvider } from \"./components/guideSetting/guideList/SnackbarContext\";\r\nimport { TranslationProvider } from \"./contexts/TranslationContext\";\r\nimport Rte from \"./components/guideSetting/RTE\";\r\nimport jwtDecode from \"jwt-decode\";\r\nimport useInfoStore from \"./store/UserInfoStore\";\r\nimport { initializeI18n } from \"./multilinguial/i18n\";\r\nimport ExtensionPopupLoader from \"./components/common/ExtensionPopupLoader\";\r\nimport { useExtensionInitialization } from \"./hooks/useExtensionInitialization\";\r\n\r\nfunction App() {\r\n\tconst [isI18nReady, setIsI18nReady] = useState(false);\r\n\tconst accessToken = useInfoStore((state) => state.accessToken);\r\n\tconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\n\tconst { isInitializing: isExtensionInitializing, isFirstLoad } = useExtensionInitialization();\r\n\r\n\t// Initialize i18n once when app starts\r\n\tuseEffect(() => {\r\n\t\tconst setupI18n = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tawait initializeI18n();\r\n\t\t\t\tconsole.log('✅ i18n ready for use');\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Failed to initialize i18n:', error);\r\n\t\t\t\t// Set ready anyway to prevent infinite loading\r\n\t\t\t\tsetIsI18nReady(true);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tsetupI18n();\r\n\t}, []);\r\n\r\n\t// Check token validity\r\n\tuseEffect(() => {\r\n\t\tif (accessToken) {\r\n\t\t\ttry {\r\n\t\t\t\tconst decodedToken: any = jwtDecode(accessToken);\r\n\t\t\t\tconst currentTime = Math.floor(Date.now() / 1000);\r\n\t\t\t\tif (decodedToken.exp < currentTime) {\r\n\t\t\t\t\tconsole.log('🔐 Token expired, clearing session');\r\n\t\t\t\t\tclearAll();\r\n\t\t\t\t\tclearAccessToken();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('❌ Invalid token, clearing session:', error);\r\n\t\t\t\tclearAll();\r\n\t\t\t\tclearAccessToken();\r\n\t\t\t}\r\n\t\t}\r\n\t}, [accessToken, clearAll, clearAccessToken]);\r\n\r\n\t// Show popup loader until i18n is ready or during extension initialization\r\n\tconst shouldShowPopupLoader = !isI18nReady || (isFirstLoad && isExtensionInitializing);\r\n\r\n\treturn (\r\n\t\t<div className=\"App\">\r\n\t\t\t{/* Show popup loader before extension page loads */}\r\n\t\t\t{shouldShowPopupLoader && (\r\n\t\t\t\t<ExtensionPopupLoader\r\n\t\t\t\t\tmessage={!isI18nReady ? \"Initializing language system...\" : \"Extension is starting up...\"}\r\n\t\t\t\t\tduration={!isI18nReady ? 5000 : 3000}\r\n\t\t\t\t\tposition=\"top-right\"\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\r\n\t\t\t{/* Only render main components when i18n is ready */}\r\n\t\t\t{isI18nReady && (\r\n\t\t\t\t<TranslationProvider>\r\n\t\t\t\t\t<AuthProvider>\r\n\t\t\t\t\t\t<AccountProvider>\r\n\t\t\t\t\t\t\t<SnackbarProvider>\r\n\t\t\t\t\t\t\t\t<Drawer />\r\n\t\t\t\t\t\t\t</SnackbarProvider>\r\n\t\t\t\t\t\t</AccountProvider>\r\n\t\t\t\t\t</AuthProvider>\r\n\t\t\t\t</TranslationProvider>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,YAAY,CAEnB,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,OAASC,YAAY,KAAQ,gCAAgC,CAC7D,OAASC,eAAe,KAAQ,mCAAmC,CACnE,OAASC,gBAAgB,KAAQ,qDAAqD,CACtF,OAASC,mBAAmB,KAAQ,+BAA+B,CAEnE,MAAO,CAAAC,SAAS,KAAM,YAAY,CAClC,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,OAASC,cAAc,KAAQ,sBAAsB,CACrD,MAAO,CAAAC,oBAAoB,KAAM,0CAA0C,CAC3E,OAASC,0BAA0B,KAAQ,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhF,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACd,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAAkB,WAAW,CAAGX,YAAY,CAAEY,KAAK,EAAKA,KAAK,CAACD,WAAW,CAAC,CAC9D,KAAM,CAAEE,QAAQ,CAAEC,gBAAiB,CAAC,CAAGd,YAAY,CAACe,QAAQ,CAAC,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,uBAAuB,CAAEC,WAAY,CAAC,CAAGf,0BAA0B,CAAC,CAAC,CAE7F;AACAX,SAAS,CAAC,IAAM,CACf,KAAM,CAAA2B,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACH,KAAM,CAAAlB,cAAc,CAAC,CAAC,CACtBmB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnCX,cAAc,CAAC,IAAI,CAAC,CACrB,CAAE,MAAOY,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD;AACAZ,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAEDS,SAAS,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3B,SAAS,CAAC,IAAM,CACf,GAAImB,WAAW,CAAE,CAChB,GAAI,CACH,KAAM,CAAAY,YAAiB,CAAGxB,SAAS,CAACY,WAAW,CAAC,CAChD,KAAM,CAAAa,WAAW,CAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAC,CACjD,GAAIL,YAAY,CAACM,GAAG,CAAGL,WAAW,CAAE,CACnCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjDR,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CAAE,MAAOQ,KAAK,CAAE,CACfF,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1DT,QAAQ,CAAC,CAAC,CACVC,gBAAgB,CAAC,CAAC,CACnB,CACD,CACD,CAAC,CAAE,CAACH,WAAW,CAAEE,QAAQ,CAAEC,gBAAgB,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAgB,qBAAqB,CAAG,CAACrB,WAAW,EAAKS,WAAW,EAAID,uBAAwB,CAEtF,mBACCV,KAAA,QAAKwB,SAAS,CAAC,KAAK,CAAAC,QAAA,EAElBF,qBAAqB,eACrBzB,IAAA,CAACH,oBAAoB,EACpB+B,OAAO,CAAE,CAACxB,WAAW,CAAG,iCAAiC,CAAG,6BAA8B,CAC1FyB,QAAQ,CAAE,CAACzB,WAAW,CAAG,IAAI,CAAG,IAAK,CACrC0B,QAAQ,CAAC,WAAW,CACpB,CACD,CAGA1B,WAAW,eACXJ,IAAA,CAACP,mBAAmB,EAAAkC,QAAA,cACnB3B,IAAA,CAACV,YAAY,EAAAqC,QAAA,cACZ3B,IAAA,CAACT,eAAe,EAAAoC,QAAA,cACf3B,IAAA,CAACR,gBAAgB,EAAAmC,QAAA,cAChB3B,IAAA,CAACX,MAAM,GAAE,CAAC,CACO,CAAC,CACH,CAAC,CACL,CAAC,CACK,CACrB,EACG,CAAC,CAER,CAEA,cAAe,CAAAc,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}