{"ast": null, "code": "import React,{useState}from\"react\";import{Box,Typography,TextField,Button,IconButton,Tooltip}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";// import Draggable from \"react-draggable\";\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PageTrigger=()=>{const[isOpen,setIsOpen]=useState(true);const[timedDelay,setTimedDelay]=useState(\"\");const[onScroll,setOnScroll]=useState(\"\");const handleClose=()=>{setIsOpen(false);};const handleTimedDelayChange=e=>{const value=e.target.value;if(/^\\d*$/.test(value)){// Regex to allow only digits\nsetTimedDelay(value);}};const handleOnScrollChange=e=>{const value=e.target.value;if(/^\\d*$/.test(value)){// Regex to allow only digits\nsetOnScroll(value);}};if(!isOpen)return null;return/*#__PURE__*/(//<Draggable>\n_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:\"Page Trigger\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":\"close\",onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:\"Timed Delay\"}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-input-box\",children:/*#__PURE__*/_jsx(TextField,{value:timedDelay,onChange:handleTimedDelayChange,variant:\"outlined\",size:\"small\",className:\"qadpt-control-input\",inputProps:{style:{textAlign:\"center\"},endAdornment:\"px\",type:\"number\"}})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:\"On Scroll\"}),/*#__PURE__*/_jsx(Box,{className:\"qadpt-input-box\",children:/*#__PURE__*/_jsx(TextField,{value:onScroll,onChange:handleOnScrollChange,variant:\"outlined\",size:\"small\",className:\"qadpt-control-input\",inputProps:{style:{textAlign:\"center\"},endAdornment:\"px\",type:\"number\"}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{backgroundColor:\"var(--back-light-color)\",borderRadius:\"10px\",padding:\"12px\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"14px\",marginBottom:\"8px\",color:\"rgba(0, 0, 0, 0.38)\"},children:\"Click Element\"}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Coming Soon\",PopperProps:{sx:{zIndex:9999}},children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(Button,{disabled:true,variant:\"outlined\",sx:{width:\"100%\",textTransform:\"none\",borderRadius:\"8px\",borderColor:\"var(--border-color)\",color:\"var(--primarycolor)\"},children:\"Choose element\"})})})]})]})})//</Draggable>\n);};export default PageTrigger;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "CloseIcon", "jsx", "_jsx", "jsxs", "_jsxs", "PageTrigger", "isOpen", "setIsOpen", "timedDelay", "setTimedDelay", "onScroll", "setOnScroll", "handleClose", "handleTimedDelayChange", "e", "value", "target", "test", "handleOnScrollChange", "id", "className", "children", "size", "onClick", "onChange", "variant", "inputProps", "style", "textAlign", "endAdornment", "type", "sx", "backgroundColor", "borderRadius", "padding", "fontSize", "marginBottom", "color", "arrow", "title", "PopperProps", "zIndex", "disabled", "width", "textTransform", "borderColor"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PageTrigger.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Typography, TextField, Button, IconButton, Tooltip } from \"@mui/material\";\r\nimport ArrowBackIosIcon from \"@mui/icons-material/ArrowBackIos\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n// import Draggable from \"react-draggable\";\r\n\r\nconst PageTrigger = () => {\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [timedDelay, setTimedDelay] = useState(\"\");\r\n\tconst [onScroll, setOnScroll] = useState(\"\");\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t};\r\n\r\n\tconst handleTimedDelayChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tif (/^\\d*$/.test(value)) {\r\n\t\t\t// Regex to allow only digits\r\n\t\t\tsetTimedDelay(value);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleOnScrollChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tif (/^\\d*$/.test(value)) {\r\n\t\t\t// Regex to allow only digits\r\n\t\t\tsetOnScroll(value);\r\n\t\t}\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">Page Trigger</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t{/* Timed Delay Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Timed Delay</Typography>\r\n\t\t\t\t\t\t<Box className=\"qadpt-input-box\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={timedDelay}\r\n\t\t\t\t\t\t\t\tonChange={handleTimedDelayChange}\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tinputProps={{\r\n\t\t\t\t\t\t\t\t\tstyle: { textAlign: \"center\" },\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\ttype: \"number\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* On Scroll Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">On Scroll</Typography>\r\n\t\t\t\t\t\t<Box className=\"qadpt-input-box\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={onScroll}\r\n\t\t\t\t\t\t\t\tonChange={handleOnScrollChange}\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tinputProps={{\r\n\t\t\t\t\t\t\t\t\tstyle: { textAlign: \"center\" },\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\ttype: \"number\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", marginBottom: \"8px\", color: \"rgba(0, 0, 0, 0.38)\" }}>\r\n\t\t\t\t\t\tClick Element\r\n\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\tPopperProps={{ sx: { zIndex: 9999 } }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tChoose element\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default PageTrigger;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAEC,MAAM,CAAEC,UAAU,CAAEC,OAAO,KAAQ,eAAe,CAEvF,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEA,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACe,UAAU,CAAEC,aAAa,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACiB,QAAQ,CAAEC,WAAW,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAE5C,KAAM,CAAAmB,WAAW,CAAGA,CAAA,GAAM,CACzBL,SAAS,CAAC,KAAK,CAAC,CACjB,CAAC,CAED,KAAM,CAAAM,sBAAsB,CAAIC,CAAM,EAAK,CAC1C,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B,GAAI,OAAO,CAACE,IAAI,CAACF,KAAK,CAAC,CAAE,CACxB;AACAN,aAAa,CAACM,KAAK,CAAC,CACrB,CACD,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAIJ,CAAM,EAAK,CACxC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B,GAAI,OAAO,CAACE,IAAI,CAACF,KAAK,CAAC,CAAE,CACxB;AACAJ,WAAW,CAACI,KAAK,CAAC,CACnB,CACD,CAAC,CAED,GAAI,CAACT,MAAM,CAAE,MAAO,KAAI,CAExB,mBACC;AACAJ,IAAA,QACCiB,EAAE,CAAC,mBAAmB,CACtBC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7BjB,KAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7BjB,KAAA,QAAKgB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnCnB,IAAA,QAAKkB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,cAAY,CAAK,CAAC,cAC/CnB,IAAA,CAACJ,UAAU,EACVwB,IAAI,CAAC,OAAO,CACZ,aAAW,OAAO,CAClBC,OAAO,CAAEX,WAAY,CAAAS,QAAA,cAErBnB,IAAA,CAACF,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cAENI,KAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE9BjB,KAAA,CAACV,GAAG,EAAC0B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCnB,IAAA,CAACP,UAAU,EAACyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,aAAW,CAAY,CAAC,cACpEnB,IAAA,CAACR,GAAG,EAAC0B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC/BnB,IAAA,CAACN,SAAS,EACTmB,KAAK,CAAEP,UAAW,CAClBgB,QAAQ,CAAEX,sBAAuB,CACjCY,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZF,SAAS,CAAC,qBAAqB,CAC/BM,UAAU,CAAE,CACXC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAC,CAC9BC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,QACP,CAAE,CACF,CAAC,CACE,CAAC,EACF,CAAC,cAGN1B,KAAA,CAACV,GAAG,EAAC0B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjCnB,IAAA,CAACP,UAAU,EAACyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,WAAS,CAAY,CAAC,cAClEnB,IAAA,CAACR,GAAG,EAAC0B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC/BnB,IAAA,CAACN,SAAS,EACTmB,KAAK,CAAEL,QAAS,CAChBc,QAAQ,CAAEN,oBAAqB,CAC/BO,OAAO,CAAC,UAAU,CAClBH,IAAI,CAAC,OAAO,CACZF,SAAS,CAAC,qBAAqB,CAC/BM,UAAU,CAAE,CACXC,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAC,CAC9BC,YAAY,CAAE,IAAI,CAClBC,IAAI,CAAE,QACP,CAAE,CACF,CAAC,CACE,CAAC,EACF,CAAC,EACF,CAAC,cAEN1B,KAAA,CAACV,GAAG,EACHqC,EAAE,CAAE,CACHC,eAAe,CAAE,yBAAyB,CAC1CC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,MACV,CAAE,CAAAb,QAAA,eAEFnB,IAAA,CAACP,UAAU,EAACoC,EAAE,CAAE,CAAEI,QAAQ,CAAE,MAAM,CAAEC,YAAY,CAAE,KAAK,CAAEC,KAAK,CAAE,qBAAsB,CAAE,CAAAhB,QAAA,CAAC,eAEzF,CAAY,CAAC,cAEbnB,IAAA,CAACH,OAAO,EAACuC,KAAK,MACbC,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAE,CAAET,EAAE,CAAE,CAAEU,MAAM,CAAE,IAAK,CAAE,CAAE,CAAApB,QAAA,cAEtCnB,IAAA,SAAAmB,QAAA,cACCnB,IAAA,CAACL,MAAM,EACN6C,QAAQ,MACRjB,OAAO,CAAC,UAAU,CAClBM,EAAE,CAAE,CACHY,KAAK,CAAE,MAAM,CACbC,aAAa,CAAE,MAAM,CACrBX,YAAY,CAAE,KAAK,CACnBY,WAAW,CAAE,qBAAqB,CAClCR,KAAK,CAAE,qBACR,CAAE,CAAAhB,QAAA,CACF,gBAED,CAAQ,CAAC,CACJ,CAAC,CACC,CAAC,EACN,CAAC,EACF,CAAC,CACF,CACL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAhB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}