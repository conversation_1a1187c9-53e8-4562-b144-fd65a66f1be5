import React, { useState } from 'react';
import ExtensionLoader from './ExtensionLoader';
import InlineLoader from './InlineLoader';
import ExtensionPopupLoader from './ExtensionPopupLoader';
import { Button } from '@mui/material';
import './LoaderDemo.css';

const LoaderDemo: React.FC = () => {
  const [showFullLoader, setShowFullLoader] = useState(false);
  const [showMediumLoader, setShowMediumLoader] = useState(false);
  const [showSmallLoader, setShowSmallLoader] = useState(false);
  const [showInlineLoader, setShowInlineLoader] = useState(false);
  const [showPopupLoader, setShowPopupLoader] = useState(false);

  const simulateLoading = (
    setLoader: React.Dispatch<React.SetStateAction<boolean>>, 
    duration: number = 3000
  ) => {
    setLoader(true);
    setTimeout(() => setLoader(false), duration);
  };

  return (
    <div className="loader-demo">
      <h2>QuickAdapt Extension Loaders Demo</h2>
      
      <div className="demo-section">
        <h3>Full Screen Loaders</h3>
        <div className="demo-buttons">
          <Button 
            variant="contained" 
            onClick={() => simulateLoading(setShowFullLoader)}
            disabled={showFullLoader}
          >
            {showFullLoader ? 'Loading...' : 'Show Large Loader'}
          </Button>
          
          <Button 
            variant="contained" 
            onClick={() => simulateLoading(setShowMediumLoader)}
            disabled={showMediumLoader}
          >
            {showMediumLoader ? 'Loading...' : 'Show Medium Loader'}
          </Button>
          
          <Button 
            variant="contained" 
            onClick={() => simulateLoading(setShowSmallLoader)}
            disabled={showSmallLoader}
          >
            {showSmallLoader ? 'Loading...' : 'Show Small Loader'}
          </Button>
        </div>
      </div>

      <div className="demo-section">
        <h3>Inline Loaders</h3>
        <div className="inline-demo">
          <div className="inline-example">
            <span>Processing data </span>
            <InlineLoader size="small" message="Please wait..." />
          </div>
          
          <div className="inline-example">
            <span>Saving changes </span>
            <InlineLoader size="medium" color="#10b981" />
          </div>
          
          <div className="inline-example">
            <span>Uploading file </span>
            <InlineLoader size="large" color="#f59e0b" message="Uploading..." />
          </div>
          
          <Button 
            variant="outlined" 
            onClick={() => simulateLoading(setShowInlineLoader, 2000)}
            disabled={showInlineLoader}
            style={{ marginTop: '16px' }}
          >
            {showInlineLoader ? (
              <>
                <InlineLoader size="small" color="#1976d2" />
                <span style={{ marginLeft: '8px' }}>Processing...</span>
              </>
            ) : (
              'Toggle Inline Loader'
            )}
          </Button>
        </div>
      </div>

      <div className="demo-section">
        <h3>Popup Loader (Top-Right Corner)</h3>
        <div className="demo-buttons">
          <Button
            variant="contained"
            color="secondary"
            onClick={() => simulateLoading(setShowPopupLoader, 4000)}
            disabled={showPopupLoader}
          >
            {showPopupLoader ? 'Showing Popup...' : 'Show Extension Popup Loader'}
          </Button>

          <Button
            variant="outlined"
            color="primary"
            onClick={() => {
              // Reset session storage to simulate first-time load
              sessionStorage.removeItem('quickadapt-initialized');
              sessionStorage.removeItem('quickadapt-popup-shown');
              window.location.reload();
            }}
          >
            Reset & Reload (Simulate First Load)
          </Button>
        </div>
        <p style={{ fontSize: '14px', color: '#666', marginTop: '8px' }}>
          This simulates the popup that appears when the extension is first enabled in the browser.
          Use "Reset & Reload" to test the actual first-time experience.
        </p>
      </div>

      <div className="demo-section">
        <h3>Usage Examples</h3>
        <div className="usage-examples">
          <div className="usage-card">
            <h4>Extension Initialization</h4>
            <p>Use the large full-screen loader when the extension is starting up</p>
            <code>{`<ExtensionLoader message="Initializing QuickAdapt Extension..." />`}</code>
          </div>
          
          <div className="usage-card">
            <h4>Login Process</h4>
            <p>Use medium loader for authentication flows</p>
            <code>{`<ExtensionLoader size="medium" message="Signing you in..." />`}</code>
          </div>
          
          <div className="usage-card">
            <h4>Button Actions</h4>
            <p>Use inline loaders for button states and quick actions</p>
            <code>{`<InlineLoader size="small" message="Saving..." />`}</code>
          </div>

          <div className="usage-card">
            <h4>Extension Startup</h4>
            <p>Use popup loader when extension is first enabled in browser</p>
            <code>{`<ExtensionPopupLoader message="Extension starting..." position="top-right" />`}</code>
          </div>
        </div>
      </div>

      {/* Render active loaders */}
      {showFullLoader && (
        <ExtensionLoader 
          message="Loading large content..." 
          size="large" 
        />
      )}
      
      {showMediumLoader && (
        <ExtensionLoader 
          message="Processing your request..." 
          size="medium" 
        />
      )}
      
      {showSmallLoader && (
        <ExtensionLoader
          message="Quick action..."
          size="small"
        />
      )}

      {showPopupLoader && (
        <ExtensionPopupLoader
        //  message="QuickAdapt Extension is loading..."
          duration={4000}
          position="top-right"
          onComplete={() => console.log('Popup loader completed!')}
        />
      )}
    </div>
  );
};

export default LoaderDemo;
