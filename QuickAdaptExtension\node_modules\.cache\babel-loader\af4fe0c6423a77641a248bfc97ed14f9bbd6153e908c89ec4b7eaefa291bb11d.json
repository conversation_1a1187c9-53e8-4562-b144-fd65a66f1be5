{"ast": null, "code": "import React from'react';import{Icon<PERSON>utton,Tooltip,Box}from'@mui/material';import{useTranslation}from'react-i18next';import useDrawerStore from'../../store/drawerStore';import{redoicon,undoicon}from'../../assets/icons/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";/**\r\n * UndoRedoButtons component provides undo and redo functionality\r\n * for the guide editor.\r\n */const UndoRedoButtons=_ref=>{let{size='medium',color='primary',disabled=false,className}=_ref;const{t:translate}=useTranslation();// Get the undo/redo functions and state from the drawer store\nconst{undo,redo,canUndo,canRedo}=useDrawerStore();// Check if undo/redo are available\nconst canUndoValue=canUndo();const canRedoValue=canRedo();return/*#__PURE__*/_jsxs(Box,{className:\"undo-redobtn\",sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"coming soon\"),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{className:\"qadpt-banner-button qadpt-icon\",onClick:undo,disabled:true,sx:{opacity:0.5},size:size,color:\"default\",\"aria-label\":translate(\"Undo\"),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:undoicon},style:{width:\"24px\",placeContent:\"center\"}})})})})}),/*#__PURE__*/_jsx(Box,{sx:{marginLeft:1},children:/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate(\"coming soon\"),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{className:\"qadpt-banner-button qadpt-icon\",onClick:redo,disabled:true,sx:{opacity:0.5},size:size,color:\"default\",\"aria-label\":translate(\"Redo\"),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:redoicon},style:{width:\"24px\",placeContent:\"center\"}})})})})})]});};export default UndoRedoButtons;", "map": {"version": 3, "names": ["React", "IconButton", "<PERSON><PERSON><PERSON>", "Box", "useTranslation", "useDrawerStore", "redoicon", "undoicon", "jsx", "_jsx", "jsxs", "_jsxs", "UndoRedoButtons", "_ref", "size", "color", "disabled", "className", "t", "translate", "undo", "redo", "canUndo", "canRedo", "canUndoValue", "canRedoValue", "sx", "display", "alignItems", "children", "arrow", "title", "onClick", "opacity", "dangerouslySetInnerHTML", "__html", "style", "width", "place<PERSON><PERSON>nt", "marginLeft"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/UndoRedoButtons.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { IconButton, Tooltip, Box } from '@mui/material';\r\nimport UndoIcon from '@mui/icons-material/Undo';\r\nimport RedoIcon from '@mui/icons-material/Redo';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport { redoicon, undoicon } from '../../assets/icons/icons';\r\n\r\ninterface UndoRedoButtonsProps {\r\n  size?: 'small' | 'medium' | 'large';\r\n  color?: string;\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * UndoRedoButtons component provides undo and redo functionality\r\n * for the guide editor.\r\n */\r\nconst UndoRedoButtons: React.FC<UndoRedoButtonsProps> = ({\r\n  size = 'medium',\r\n  color = 'primary',\r\n  disabled = false,\r\n  className,\r\n}) => {\r\n  const { t: translate } = useTranslation();\r\n  // Get the undo/redo functions and state from the drawer store\r\n  const { undo, redo, canUndo, canRedo } = useDrawerStore();\r\n\r\n  // Check if undo/redo are available\r\n  const canUndoValue = canUndo();\r\n  const canRedoValue = canRedo();\r\n\r\n  return (\r\n    <Box className=\"undo-redobtn\" sx={{ display: 'flex', alignItems: 'center' }}>\r\n      <Box>\r\n        <Tooltip arrow title={translate(\"coming soon\")}>\r\n          <span>\r\n            {/* <IconButton\r\n              onClick={undo}\r\n              disabled={disabled || !canUndoValue}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Undo\")}\r\n            >\r\n              <UndoIcon fontSize={size} />\r\n            </IconButton> */}\r\n            <IconButton\r\n              className=\"qadpt-banner-button qadpt-icon\"\r\n              onClick={undo}\r\n              disabled={true}\r\n              sx={{ opacity: 0.5 }}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Undo\")}\r\n            >\r\n              <span dangerouslySetInnerHTML={{ __html: undoicon }} style={{ width: \"24px\", placeContent: \"center\" }}></span>\r\n            </IconButton>\r\n          </span>\r\n        </Tooltip>\r\n      </Box>\r\n      <Box sx={{ marginLeft: 1 }}>\r\n        <Tooltip arrow title={translate(\"coming soon\")}>\r\n          <span>\r\n            {/* <IconButton\r\n              onClick={redo}\r\n              disabled={disabled || !canRedoValue}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Redo\")}\r\n            >\r\n              <RedoIcon fontSize={size} />\r\n            </IconButton> */}\r\n            <IconButton\r\n              className=\"qadpt-banner-button qadpt-icon\"\r\n              onClick={redo}\r\n              disabled={true}\r\n              sx={{ opacity: 0.5 }}\r\n              size={size}\r\n              color=\"default\"\r\n              aria-label={translate(\"Redo\")}\r\n            >\r\n              <span dangerouslySetInnerHTML={{ __html: redoicon }} style={{ width: \"24px\", placeContent: \"center\" }}></span>\r\n            </IconButton>\r\n          </span>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default UndoRedoButtons;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,UAAU,CAAEC,OAAO,CAAEC,GAAG,KAAQ,eAAe,CAGxD,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,OAASC,QAAQ,CAAEC,QAAQ,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS9D;AACA;AACA;AACA,GACA,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAKlD,IALmD,CACvDC,IAAI,CAAG,QAAQ,CACfC,KAAK,CAAG,SAAS,CACjBC,QAAQ,CAAG,KAAK,CAChBC,SACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAEK,CAAC,CAAEC,SAAU,CAAC,CAAGf,cAAc,CAAC,CAAC,CACzC;AACA,KAAM,CAAEgB,IAAI,CAAEC,IAAI,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAGlB,cAAc,CAAC,CAAC,CAEzD;AACA,KAAM,CAAAmB,YAAY,CAAGF,OAAO,CAAC,CAAC,CAC9B,KAAM,CAAAG,YAAY,CAAGF,OAAO,CAAC,CAAC,CAE9B,mBACEZ,KAAA,CAACR,GAAG,EAACc,SAAS,CAAC,cAAc,CAACS,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAC,QAAA,eAC1EpB,IAAA,CAACN,GAAG,EAAA0B,QAAA,cACFpB,IAAA,CAACP,OAAO,EAAC4B,KAAK,MAACC,KAAK,CAAEZ,SAAS,CAAC,aAAa,CAAE,CAAAU,QAAA,cAC7CpB,IAAA,SAAAoB,QAAA,cAUEpB,IAAA,CAACR,UAAU,EACTgB,SAAS,CAAC,gCAAgC,CAC1Ce,OAAO,CAAEZ,IAAK,CACdJ,QAAQ,CAAE,IAAK,CACfU,EAAE,CAAE,CAAEO,OAAO,CAAE,GAAI,CAAE,CACrBnB,IAAI,CAAEA,IAAK,CACXC,KAAK,CAAC,SAAS,CACf,aAAYI,SAAS,CAAC,MAAM,CAAE,CAAAU,QAAA,cAE9BpB,IAAA,SAAMyB,uBAAuB,CAAE,CAAEC,MAAM,CAAE5B,QAAS,CAAE,CAAC6B,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,YAAY,CAAE,QAAS,CAAE,CAAO,CAAC,CACpG,CAAC,CACT,CAAC,CACA,CAAC,CACP,CAAC,cACN7B,IAAA,CAACN,GAAG,EAACuB,EAAE,CAAE,CAAEa,UAAU,CAAE,CAAE,CAAE,CAAAV,QAAA,cACzBpB,IAAA,CAACP,OAAO,EAAC4B,KAAK,MAACC,KAAK,CAAEZ,SAAS,CAAC,aAAa,CAAE,CAAAU,QAAA,cAC7CpB,IAAA,SAAAoB,QAAA,cAUEpB,IAAA,CAACR,UAAU,EACTgB,SAAS,CAAC,gCAAgC,CAC1Ce,OAAO,CAAEX,IAAK,CACdL,QAAQ,CAAE,IAAK,CACfU,EAAE,CAAE,CAAEO,OAAO,CAAE,GAAI,CAAE,CACrBnB,IAAI,CAAEA,IAAK,CACXC,KAAK,CAAC,SAAS,CACf,aAAYI,SAAS,CAAC,MAAM,CAAE,CAAAU,QAAA,cAE9BpB,IAAA,SAAMyB,uBAAuB,CAAE,CAAEC,MAAM,CAAE7B,QAAS,CAAE,CAAC8B,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEC,YAAY,CAAE,QAAS,CAAE,CAAO,CAAC,CACpG,CAAC,CACT,CAAC,CACA,CAAC,CACP,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}