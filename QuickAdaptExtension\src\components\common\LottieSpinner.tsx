import React from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import './LottieSpinner.css';

interface LottieSpinnerProps {
  size?: number;
  className?: string;
  backgroundColor?: string;
}

const LottieSpinner: React.FC<LottieSpinnerProps> = ({
  size = 60,
  className = '',
  backgroundColor = 'transparent'
}) => {
  return (
    <div
      className={`lottie-spinner-container ${className}`}
      style={{
        width: size,
        height: size,
        backgroundColor: backgroundColor,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <DotLottieReact
        src="https://lottie.host/1af42d2f-54f8-4b9f-b8fc-433ef9ab57cc/ZHCCOvYhBx.lottie"
        loop
        autoplay
        className="lottie-spinner"
      />
    </div>
  );
};

export default LottieSpinner;
