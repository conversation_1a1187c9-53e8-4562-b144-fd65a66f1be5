{"name": "userguide", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/poppins": "^5.1.0", "@lottiefiles/dotlottie-react": "^0.14.3", "@mui/icons-material": "^6.1.0", "@mui/material": "^6.1.0", "@mui/styles": "^6.1.0", "@mui/x-data-grid": "^7.20.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.108", "axios": "^1.7.2", "css-loader": "^7.1.2", "dotenv-webpack": "^8.1.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.0", "env-cmd": "^10.1.0", "html-react-parser": "^5.1.18", "i18next": "^25.2.1", "i18next-http-backend": "^3.0.2", "immer": "^10.1.1", "jodit": "^4.6.2", "jodit-react": "^5.2.19", "jsencrypt": "^3.3.2", "jwt-decode": "^3.1.2", "moment-timezone": "^0.5.46", "mui-rte": "^2.0.1", "oidc-client": "^1.10.1", "oidc-client-ts": "^3.0.1", "react-i18next": "^15.5.3", "react-perfect-scrollbar": "^1.5.8", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "sass": "^1.79.3", "sass-loader": "^16.0.2", "style-loader": "^4.0.0", "typescript": "^5.6.3", "web-vitals": "^2.1.4", "webpack-config": "^7.5.0", "zustand": "^5.0.0"}, "scripts": {"start": "env-cmd -f .env.dev react-scripts start", "start:dev-integration": "env-cmd -f .env.development.integration react-scripts start", "start:qa-integration": "env-cmd -f .env.qa.integration react-scripts start", "start:production": "env-cmd -f .env.production react-scripts start", "build:cloud": "env-cmd -f .env.cloud react-scripts build", "build:development": "env-cmd -f .env.development react-scripts build", "build:production": "env-cmd -f .env.live react-scripts build", "build:qa": "env-cmd -f .env.qa react-scripts build", "build:uat": "env-cmd -f .env.uat react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "i18n:extract": "i18next --config i18next-parser.config.js \"src/**/*.{js,ts,jsx,tsx}\" --output \"src/multilinguial/final.json\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@types/chrome": "^0.0.320", "@types/react": "^18.3.11", "@types/react-color": "^3.0.12", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "babel-loader": "^9.2.1", "copy-webpack-plugin": "^12.0.2", "eslint": "^9.10.0", "eslint-config-react-app": "^7.0.1", "i18next-parser": "^9.3.0", "react": "^18.3.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}}