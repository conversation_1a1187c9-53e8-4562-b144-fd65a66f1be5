/* Lottie Spinner Container */
.lottie-spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Lottie Animation */
.lottie-spinner {
  width: 85%;
  height: 85%;
  object-fit: contain;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
}

/* Ensure smooth rendering */
.lottie-spinner-container * {
  pointer-events: none;
  user-select: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .lottie-spinner-container {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .lottie-spinner {
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.4));
  }
}
