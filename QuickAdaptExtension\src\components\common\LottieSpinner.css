/* Lottie Spinner Container */
.lottie-spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Lottie Animation */
.lottie-spinner {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Ensure smooth rendering */
.lottie-spinner-container * {
  pointer-events: none;
  user-select: none;
}

/* Optional: Add a subtle background for better visibility */
.lottie-spinner-container.with-background {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  padding: 8px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .lottie-spinner-container.with-background {
    background: rgba(0, 0, 0, 0.1);
  }
}
