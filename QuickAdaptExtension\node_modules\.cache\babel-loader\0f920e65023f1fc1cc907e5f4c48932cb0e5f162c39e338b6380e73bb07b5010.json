{"ast": null, "code": "import React,{useMemo,useState}from\"react\";import{Box,Button,Popover,IconButton,Tooltip}from\"@mui/material\";import{ChromePicker}from\"react-color\";import{deleteicon,copyicon,settingsicon}from\"../../../assets/icons/icons\";import useDrawerStore from\"../../../store/drawerStore\";import AddIcon from\"@mui/icons-material/Add\";import ButtonSetting from\"./ButtonSetting\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ButtonSection=_ref=>{var _toolTipGuideMetaData7,_toolTipGuideMetaData8,_toolTipGuideMetaData9,_toolTipGuideMetaData10;let{items:buttonsContainer,updatedGuideData,isCloneDisabled}=_ref;const{t:translate}=useTranslation();const{// buttonsContainer,\ntooltipBtnSettingAnchorEl:settingAnchorEl,cloneTooltipButtonContainer:cloneButtonContainer,updateButtonInTooltip:updateButton,addNewButtonInTooltip:addNewButton,deleteButtonInTooltip:deleteButton,updateTooltipButtonInteraction,updateTooltipButtonAction,deleteTooltipButtonContainer:deleteButtonContainer,updateTooltipBtnContainer:updateContainer,setTooltipBtnSettingAnchorEl:setSettingAnchorEl,currentStep,setbtnidss,getCurrentButtonInfo,toolTipGuideMetaData,highlightedButton,setElementClick,SetElementButtonClick}=useDrawerStore(state=>state);const[anchorEl,setAnchorEl]=useState(null);const[colorPickerAnchorEl,setColorPickerAnchorEl]=useState(null);const[isDeleteIcon,setIsDeleteIcon]=useState(\"\");const[currentContainerId,setCurrentContainerId]=useState(\"\");const[currentButtonId,setCurrentButtonId]=useState(\"\");// Default button color\nlet clickTimeout;const handleClick=(event,containerId,buttonId)=>{const target=event.currentTarget;setAnchorEl(target);setSettingAnchorEl({containerId,buttonId,value:null});// Set current container and button IDs for reference\nsetCurrentContainerId(containerId);setCurrentButtonId(buttonId);};const handleClose=()=>{setAnchorEl(null);};const handleBackgroundColorClick=event=>{setColorPickerAnchorEl(event.currentTarget);};const handleColorChange=color=>{// Update the backgroundColor in the container's style\nupdateContainer(settingAnchorEl.containerId,\"style\",{backgroundColor:color.hex});// Also update the BackgroundColor property at the ButtonSection level\nupdateContainer(settingAnchorEl.containerId,\"BackgroundColor\",color.hex);};const handleCloseColorPicker=()=>{setColorPickerAnchorEl(null);};const open=Boolean(anchorEl);// const open = Boolean(anchorEl && !isEditingButton);\n// const id = open ? \"button-popover\" : undefined;\nconst colorPickerOpen=Boolean(colorPickerAnchorEl);const handleEditButtonName=(containerId,buttonId,isEditing,value)=>{// clearTimeout(clickTimeout);\nupdateButton(containerId,buttonId,isEditing,value);};const handleChangeButton=(containerId,buttonId,value)=>{updateButton(containerId,buttonId,\"type\",value);setAnchorEl(null);};const handleAddIconClick=containerId=>{addNewButton(containerId);};const shouldShowAddBtn=buttonsContainer.buttons.length;const buttonInfo=useMemo(()=>{let result=null;if(settingAnchorEl.buttonId){result=buttonsContainer.buttons.find(item=>item.id===settingAnchorEl.buttonId);}return result;},[settingAnchorEl.buttonId,buttonsContainer.buttons]);// console.log({ buttonInfo });\n// setButtonId(currentButtonId);\n// setCuntainerId(currentButtonId);\nconst handleDelteContainer=()=>{deleteButtonContainer(settingAnchorEl.containerId);setElementClick(\"element\");const updatedData={...updatedGuideData};if(updatedData.GuideStep&&updatedData.GuideStep[currentStep]){const stepData={...updatedData.GuideStep[currentStep-1]};// Ensure GotoNext exists before modifying ButtonId\nif(stepData.Design&&stepData.Design.GotoNext&&stepData.Design.GotoNext.ButtonId!==undefined){stepData.Design={...stepData.Design,GotoNext:{...stepData.Design.GotoNext,ButtonId:\"\",NextStep:\"element\"}};}setbtnidss(\"\");// Update the GuideStep array with modified step data\nupdatedData.GuideStep=[...updatedData.GuideStep];updatedData.GuideStep[currentStep-1]=stepData;updatedGuideData=updatedData;}setbtnidss(\"\");setAnchorEl(null);};const handleSettingIconClick=event=>{//current container and button IDs\nconst containerId=settingAnchorEl.containerId||currentContainerId;const buttonId=settingAnchorEl.buttonId||currentButtonId;setSettingAnchorEl({containerId,buttonId,// @ts-ignore\nvalue:event.currentTarget});handleClose();};const handleCloseSettingPopup=(_containerId,_buttonId)=>{// updateButtonAction(containerId, buttonId, {\n// \tvalue: selectedActions,\n// \ttargetURL: targetURL,\n// \ttab: selectedTab,\n// \tinteraction: null,\n// });\n// updateButtonInteraction(containerId, buttonId, selectedInteraction);\nsetSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});setAnchorEl(null);};// Clear the design button settings if the deleted button was previously selected\nconst deletebuttonidindesign=(buttonid,deletebuttonid)=>{if(buttonid===deletebuttonid){var _updatedGuideData,_updatedGuideData$Gui;const targetStep={...((_updatedGuideData=updatedGuideData)===null||_updatedGuideData===void 0?void 0:(_updatedGuideData$Gui=_updatedGuideData.GuideStep)===null||_updatedGuideData$Gui===void 0?void 0:_updatedGuideData$Gui[currentStep-1])};if(targetStep&&targetStep.Design){targetStep.Design.GotoNext={ButtonId:\"\",ButtonName:\"\",ElementPath:\"\",NextStep:\"element\"};}updatedGuideData.GuideStep[currentStep-1]=targetStep;}};const handleApplyChanges=(tempColors,selectedActions,targetURL,selectedInteraction,currentButtonName,selectedTab)=>{// Get the container and button IDs\nconst{containerId,buttonId}=settingAnchorEl;// Update the button style - make sure we're passing the correct structure\nupdateButton(containerId,buttonId,\"style\",{backgroundColor:tempColors.backgroundColor,borderColor:tempColors.borderColor,color:tempColors.color});updateTooltipButtonAction(containerId,buttonId,{value:selectedActions,targetURL:targetURL,tab:selectedTab,interaction:null});updateTooltipButtonInteraction(containerId,buttonId,selectedInteraction);updateButton(containerId,buttonId,\"name\",currentButtonName);// Clear selection\nsetSettingAnchorEl({containerId:\"\",buttonId:\"\",value:null});setAnchorEl(null);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{component:\"div\",id:buttonsContainer.id,sx:{height:\"60px\",width:\"100%\",display:\"flex\",alignItems:\"center\",gap:\"5px\",padding:\"0px\",boxSizing:\"border-box\",backgroundColor:buttonsContainer.style.backgroundColor,justifyContent:\"center\"}// onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\n// onMouseLeave={(e) => setCurrentContainerId(\"\")}\n,children:[buttonsContainer.buttons.map(item=>{return/*#__PURE__*/_jsxs(Box,{sx:{position:\"relative\",display:\"flex\",// flex: 1,\njustifyContent:`center`// \"&:hover .edit-icon\": { display: \"inline-flex\" },\n},onMouseLeave:()=>{setIsDeleteIcon(\"\");},children:[/*#__PURE__*/_jsx(Button// contentEditable={item.isEditing}\n,{onMouseOver:e=>{if(item.isEditing===false&&e.currentTarget.id===item.id){setIsDeleteIcon(item.id);}},id:item.id,variant:\"contained\",sx:{borderRadius:\"8px\",transition:\"none\",boxShadow:\"none !important\",border:item.style.borderColor,//border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\ncolor:`${item.style.color}`,textTransform:\"none\",padding:\"4px 8px !important\",lineHeight:\"var(--button-lineheight)\",fontSize:\"14px !important\",backgroundColor:item.style.backgroundColor,width:\"fit-content\",//boxShadow: \"none !important\", // Remove box shadow in normal state\n\"&:hover\":{backgroundColor:item.style.backgroundColor,// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},onClick:e=>handleClick(e,buttonsContainer.id,item.id)// Open popover when clicking the button\n,children:translate(item.name,{defaultValue:item.name})}),buttonsContainer.buttons.length>1&&isDeleteIcon===item.id?/*#__PURE__*/_jsx(IconButton,{size:\"small\",className:\"del-icon\",sx:{position:\"absolute\",top:\"-10px\",right:\"-10px\",backgroundColor:\"#fff\",//boxShadow: \"none !important\", // Remove box shadow in normal state\n// display: \"none\", // Initially hidden\nboxShadow:\"rgba(0, 0, 0, 0.4) 0px 2px 6px\",zIndex:\"1\",padding:\"3px !important\",\"&:hover\":{backgroundColor:\"#fff\",boxShadow:\"none !important\"},span:{height:\"14px\"},svg:{width:\"14px\",height:\"14px\",path:{fill:\"#ff0000\"}}},onClick:e=>{var _toolTipGuideMetaData,_toolTipGuideMetaData2,_toolTipGuideMetaData3,_toolTipGuideMetaData4,_toolTipGuideMetaData5,_toolTipGuideMetaData6;e.stopPropagation();deletebuttonidindesign(item.id,(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData2=_toolTipGuideMetaData.design)===null||_toolTipGuideMetaData2===void 0?void 0:(_toolTipGuideMetaData3=_toolTipGuideMetaData2.gotoNext)===null||_toolTipGuideMetaData3===void 0?void 0:_toolTipGuideMetaData3.ButtonId);deleteButton(item.id,buttonsContainer.id,(_toolTipGuideMetaData4=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData4===void 0?void 0:(_toolTipGuideMetaData5=_toolTipGuideMetaData4.design)===null||_toolTipGuideMetaData5===void 0?void 0:(_toolTipGuideMetaData6=_toolTipGuideMetaData5.gotoNext)===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.ButtonId);setElementClick(\"element\");// setButtonClick(false);\nSetElementButtonClick(false);},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon}})}):null]});}),shouldShowAddBtn<4?/*#__PURE__*/_jsx(IconButton,{sx:{backgroundColor:\"#5F9EA0\",cursor:\"pointer\",zIndex:1000,padding:\"6px !important\",boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:\"#70afaf\",boxShadow:\"none !important\"// Remove box shadow in hover state\n}}// sx={sideAddButtonStyle}\n,onClick:()=>handleAddIconClick(buttonsContainer.id),children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\",sx:{color:\"#fff\"}})}):null,/*#__PURE__*/_jsx(Popover,{id:\"button-toolbar\",open:open// anchorEl={anchorEl}\n,onClose:handleClose,anchorReference:\"anchorPosition\",anchorPosition:{top:(anchorEl===null||anchorEl===void 0?void 0:anchorEl.getBoundingClientRect().top)||0,left:(anchorEl===null||anchorEl===void 0?void 0:anchorEl.getBoundingClientRect().left)||0},anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"},slotProps:{root:{// instead of writing sx on popover write here it also target to root and more clear\nsx:{zIndex:theme=>theme.zIndex.tooltip+1000}}},children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:\"6px\",padding:\"4px\"},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSettingIconClick,sx:{boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{boxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:settingsicon},style:{width:\"20px\",height:\"20px\"}})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Background Color\",style:{zIndex:99999},children:/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:buttonsContainer.style.backgroundColor===\"#5f9ea0\"?buttonsContainer.style.backgroundColor:\"#e0dbdb\",width:\"20px\",height:\"20px\",borderRadius:\"50%\",//border: `1px solid red`,\nmarginTop:\"-3px\"},component:\"div\",role:\"button\",onClick:handleBackgroundColorClick})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{cloneButtonContainer(settingAnchorEl.containerId);setAnchorEl(null);},disabled:isCloneDisabled,title:isCloneDisabled?\"Maximum limit of 3 Button sections reached\":\"Clone Section\",sx:{boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{boxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:isCloneDisabled?0.5:1,width:\"20px\",height:\"20px\"}})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\"// disabled={buttonsContainer.buttons.length === 1}\n,onClick:handleDelteContainer,disabled:((_toolTipGuideMetaData7=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData7===void 0?void 0:(_toolTipGuideMetaData8=_toolTipGuideMetaData7.containers)===null||_toolTipGuideMetaData8===void 0?void 0:_toolTipGuideMetaData8.length)===1,sx:{boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{boxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{opacity:((_toolTipGuideMetaData9=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData9===void 0?void 0:(_toolTipGuideMetaData10=_toolTipGuideMetaData9.containers)===null||_toolTipGuideMetaData10===void 0?void 0:_toolTipGuideMetaData10.length)===1?0.5:1,pointerEvents:\"none\",width:\"20px\",height:\"24px\"}})})]})}),/*#__PURE__*/_jsx(ButtonSetting,{handleCloseSettingPopup:handleCloseSettingPopup,settingAnchorEl:settingAnchorEl,buttonInfo:buttonInfo,handleApplyChanges:handleApplyChanges,updatedGuideData:updatedGuideData})]}),/*#__PURE__*/_jsx(Popover,{open:colorPickerOpen,anchorEl:colorPickerAnchorEl,onClose:handleCloseColorPicker,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},id:\"color-picker\",slotProps:{root:{// instead of writing sx on popover write here it also target to root and more clear\nsx:{zIndex:theme=>theme.zIndex.tooltip+1000}}},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(ChromePicker,{color:buttonsContainer.style.backgroundColor,onChange:handleColorChange}),/*#__PURE__*/_jsx(\"style\",{children:`\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `})]})})]});};export default ButtonSection;", "map": {"version": 3, "names": ["React", "useMemo", "useState", "Box", "<PERSON><PERSON>", "Popover", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "ButtonSetting", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ButtonSection", "_ref", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "items", "buttonsContainer", "updatedGuideData", "isCloneDisabled", "t", "translate", "tooltipBtnSettingAnchorEl", "settingAnchorEl", "cloneTooltipButtonContainer", "cloneButtonContainer", "updateButtonInTooltip", "updateButton", "addNewButtonInTooltip", "addNewButton", "deleteButtonInTooltip", "deleteButton", "updateTooltipButtonInteraction", "updateTooltipButtonAction", "deleteTooltipButtonContainer", "deleteButtonContainer", "updateTooltipBtnContainer", "updateContainer", "setTooltipBtnSettingAnchorEl", "setSettingAnchorEl", "currentStep", "setbtnidss", "getCurrentButtonInfo", "toolTipGuideMetaData", "highlighted<PERSON><PERSON><PERSON>", "setElementClick", "SetElementButtonClick", "state", "anchorEl", "setAnchorEl", "colorPickerAnchorEl", "setColorPickerAnchorEl", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "clickTimeout", "handleClick", "event", "containerId", "buttonId", "target", "currentTarget", "value", "handleClose", "handleBackgroundColorClick", "handleColorChange", "color", "backgroundColor", "hex", "handleCloseColorPicker", "open", "Boolean", "colorPickerOpen", "handleEditButtonName", "isEditing", "handleChangeButton", "handleAddIconClick", "shouldShowAddBtn", "buttons", "length", "buttonInfo", "result", "find", "item", "id", "handleDelteContainer", "updatedData", "GuideStep", "stepData", "Design", "GotoNext", "ButtonId", "undefined", "NextStep", "handleSettingIconClick", "handleCloseSettingPopup", "_containerId", "_buttonId", "deletebuttonidindesign", "buttonid", "deletebuttonid", "_updatedGuideData", "_updatedGuideData$Gui", "targetStep", "ButtonName", "<PERSON>ement<PERSON><PERSON>", "handleApplyChanges", "tempColors", "selectedActions", "targetURL", "selectedInteraction", "currentButtonName", "selectedTab", "borderColor", "tab", "interaction", "children", "component", "sx", "height", "width", "display", "alignItems", "gap", "padding", "boxSizing", "style", "justifyContent", "map", "position", "onMouseLeave", "onMouseOver", "e", "variant", "borderRadius", "transition", "boxShadow", "border", "textTransform", "lineHeight", "fontSize", "opacity", "onClick", "name", "defaultValue", "size", "className", "top", "right", "zIndex", "span", "svg", "path", "fill", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "stopPropagation", "design", "gotoNext", "dangerouslySetInnerHTML", "__html", "cursor", "onClose", "anchorReference", "anchorPosition", "getBoundingClientRect", "left", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "root", "theme", "tooltip", "arrow", "title", "marginTop", "role", "disabled", "containers", "pointerEvents", "onChange"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/Buttons.tsx"], "sourcesContent": ["import React, { use<PERSON>emo, useState } from \"react\";\r\nimport { Box, Button, Popover, Typo<PERSON>, <PERSON><PERSON>ield, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { ButtonContainer, TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport ButtonSetting from \"./ButtonSetting\";\r\nimport { useAsyncError } from \"react-router-dom\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSection: React.FC<{ items: ButtonContainer; updatedGuideData: any; isCloneDisabled?: boolean }> = ({\r\n\titems: buttonsContainer,\r\n\tupdatedGuideData,\r\n\tisCloneDisabled,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\t// buttonsContainer,\r\n\t\ttooltipBtnSettingAnchorEl: settingAnchorEl,\r\n\t\tcloneTooltipButtonContainer: cloneButtonContainer,\r\n\t\tupdateButtonInTooltip: updateButton,\r\n\t\taddNewButtonInTooltip: addNewButton,\r\n\t\tdeleteButtonInTooltip: deleteButton,\r\n\t\tupdateTooltipButtonInteraction,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tdeleteTooltipButtonContainer: deleteButtonContainer,\r\n\t\tupdateTooltipBtnContainer: updateContainer,\r\n\t\tsetTooltipBtnSettingAnchorEl: setSettingAnchorEl,\r\n\t\tcurrentStep,\r\n\t\tsetbtnidss,\r\n\t\tgetCurrentButtonInfo,\r\n\t\ttoolTipGuideMetaData,\r\n\t\thighlightedButton,\r\n\t\tsetElementClick,\r\n\t\tSetElementButtonClick,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, containerId: string, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\t\tsetAnchorEl(target);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Set current container and button IDs for reference\r\n\t\tsetCurrentContainerId(containerId);\r\n\t\tsetCurrentButtonId(buttonId);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\t// const id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\t// clearTimeout(clickTimeout);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\taddNewButton(containerId);\r\n\t};\r\n\r\n\tconst shouldShowAddBtn = buttonsContainer.buttons.length;\r\n\r\n\tconst buttonInfo = useMemo(() => {\r\n\t\tlet result = null;\r\n\t\tif (settingAnchorEl.buttonId) {\r\n\t\t\tresult = buttonsContainer.buttons.find((item: any) => item.id === settingAnchorEl.buttonId);\r\n\t\t}\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.buttonId, buttonsContainer.buttons]);\r\n\r\n\t// console.log({ buttonInfo });\r\n\r\n\t// setButtonId(currentButtonId);\r\n\t// setCuntainerId(currentButtonId);\r\n\tconst handleDelteContainer = () => {\r\n\t\tdeleteButtonContainer(settingAnchorEl.containerId);\r\n\t\tsetElementClick(\"element\");\r\n\t\tconst updatedData = { ...updatedGuideData };\r\n\t\tif (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {\r\n\t\t\tconst stepData = { ...updatedData.GuideStep[currentStep - 1] };\r\n\r\n\t\t\t// Ensure GotoNext exists before modifying ButtonId\r\n\t\t\tif (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {\r\n\t\t\t\tstepData.Design = {\r\n\t\t\t\t\t...stepData.Design,\r\n\t\t\t\t\tGotoNext: {\r\n\t\t\t\t\t\t...stepData.Design.GotoNext,\r\n\t\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t\t},\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tsetbtnidss(\"\");\r\n\t\t\t// Update the GuideStep array with modified step data\r\n\t\t\tupdatedData.GuideStep = [...updatedData.GuideStep];\r\n\t\t\tupdatedData.GuideStep[currentStep - 1] = stepData;\r\n\t\t\tupdatedGuideData = updatedData;\r\n\t\t}\r\n\r\n\t\tsetbtnidss(\"\");\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\t//current container and button IDs\r\n\t\tconst containerId = settingAnchorEl.containerId || currentContainerId;\r\n\t\tconst buttonId = settingAnchorEl.buttonId || currentButtonId;\r\n\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\thandleClose();\r\n\t};\r\n\r\n\tconst handleCloseSettingPopup = (_containerId: string, _buttonId: string) => {\r\n\t\t// updateButtonAction(containerId, buttonId, {\r\n\t\t// \tvalue: selectedActions,\r\n\t\t// \ttargetURL: targetURL,\r\n\t\t// \ttab: selectedTab,\r\n\t\t// \tinteraction: null,\r\n\t\t// });\r\n\t\t// updateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId: \"\",\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\t// Clear the design button settings if the deleted button was previously selected\r\n\tconst deletebuttonidindesign = (buttonid: any, deletebuttonid: any) => {\r\n\t\tif (buttonid === deletebuttonid) {\r\n\t\t\tconst targetStep = { ...updatedGuideData?.GuideStep?.[currentStep - 1] };\r\n\t\t\tif (targetStep && targetStep.Design) {\r\n\t\t\t\ttargetStep.Design.GotoNext = {\r\n\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\tButtonName: \"\",\r\n\t\t\t\t\tElementPath: \"\",\r\n\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tupdatedGuideData.GuideStep[currentStep - 1] = targetStep;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleApplyChanges = (\r\n\t\ttempColors: any, // Changed from string to any to handle the object structure\r\n\t\tselectedActions: string,\r\n\t\ttargetURL: string,\r\n\t\tselectedInteraction: string,\r\n\t\tcurrentButtonName: string,\r\n\t\tselectedTab: string\r\n\t) => {\r\n\t\t// Get the container and button IDs\r\n\t\tconst { containerId, buttonId } = settingAnchorEl;\r\n\t\t// Update the button style - make sure we're passing the correct structure\r\n\t\tupdateButton(containerId, buttonId, \"style\", {\r\n\t\t\tbackgroundColor: tempColors.backgroundColor,\r\n\t\t\tborderColor: tempColors.borderColor,\r\n\t\t\tcolor: tempColors.color\r\n\t\t});\r\n\t\tupdateTooltipButtonAction(containerId, buttonId, {\r\n\t\t\tvalue: selectedActions,\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab,\r\n\t\t\tinteraction: null,\r\n\t\t});\r\n\t\tupdateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tupdateButton(containerId, buttonId, \"name\", currentButtonName);\r\n\r\n\t\t// Clear selection\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Box\r\n\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\tid={buttonsContainer.id}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\theight: \"60px\",\r\n\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\tpadding: \"0px\",\r\n\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\tbackgroundColor: buttonsContainer.style.backgroundColor,\r\n\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\t// onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t// onMouseLeave={(e) => setCurrentContainerId(\"\")}\r\n\t\t\t>\r\n\t\t\t\t{buttonsContainer.buttons.map((item: any) => {\r\n\t\t\t\t\treturn (\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t// flex: 1,\r\n\t\t\t\t\t\t\t\tjustifyContent: `center`,\r\n\t\t\t\t\t\t\t\t// \"&:hover .edit-icon\": { display: \"inline-flex\" },\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonMouseLeave={() => {\r\n\t\t\t\t\t\t\t\tsetIsDeleteIcon(\"\");\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t// contentEditable={item.isEditing}\r\n\t\t\t\t\t\t\t\tonMouseOver={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (item.isEditing === false && e.currentTarget.id === item.id) {\r\n\t\t\t\t\t\t\t\t\t\tsetIsDeleteIcon(item.id);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\ttransition: \"none\",\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tborder: item.style.borderColor,\r\n\t\t\t\t\t\t\t\t\t//border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\r\n\t\t\t\t\t\t\t\t\tcolor: `${item.style.color}`,\r\n\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"4px 8px !important\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\twidth: \"fit-content\",\r\n\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor, // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, buttonsContainer.id, item.id)} // Open popover when clicking the button\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(item.name, { defaultValue: item.name })}\r\n\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t{buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? (\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"del-icon\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\ttop: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t// display: \"none\", // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\tzIndex: \"1\",\r\n\t\t\t\t\t\t\t\t\t\tpadding : \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", \r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\"\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\tdeletebuttonidindesign(item.id, toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId);\r\n\t\t\t\t\t\t\t\t\t\tdeleteButton(\r\n\t\t\t\t\t\t\t\t\t\t\titem.id,\r\n\t\t\t\t\t\t\t\t\t\t\tbuttonsContainer.id,\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId\r\n\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\t\t\t\t\t\t\t// setButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t);\r\n\t\t\t\t})}\r\n\t\t\t\t{shouldShowAddBtn < 4 ? (\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\tonClick={() => handleAddIconClick(buttonsContainer.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t) : null}\r\n\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tid={\"button-toolbar\"}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\t// anchorEl={anchorEl}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\tanchorPosition={{\r\n\t\t\t\t\t\ttop: anchorEl?.getBoundingClientRect().top || 0,\r\n\t\t\t\t\t\tleft: anchorEl?.getBoundingClientRect().left || 0,\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\tpadding: \"4px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/*\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n                       */}\r\n\t\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }}\r\n\t\t\t\t\t\t\t\tstyle={{width:\"20px\", height:\"20px\"}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle=\"Background Color\"\r\n\t\t\t\t\t\t\tstyle={{ zIndex: 99999 }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\tbuttonsContainer.style.backgroundColor === \"#5f9ea0\"\r\n\t\t\t\t\t\t\t\t\t\t\t? buttonsContainer.style.backgroundColor\r\n\t\t\t\t\t\t\t\t\t\t\t: \"#e0dbdb\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t//border: `1px solid red`,\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tcloneButtonContainer(settingAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\tsetAnchorEl(null);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\ttitle={isCloneDisabled ? \"Maximum limit of 3 Button sections reached\" : \"Clone Section\"}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1, width:\"20px\", height:\"20px\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t// disabled={buttonsContainer.buttons.length === 1}\r\n\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\r\n\t\t\t\t<ButtonSetting\r\n\t\t\t\t\thandleCloseSettingPopup={handleCloseSettingPopup}\r\n\t\t\t\t\tsettingAnchorEl={settingAnchorEl}\r\n\t\t\t\t\tbuttonInfo={buttonInfo}\r\n\t\t\t\t\thandleApplyChanges={handleApplyChanges}\r\n\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t/>\r\n\t\t\t</Box>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"color-picker\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={buttonsContainer.style.backgroundColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,OAAO,CAAEC,QAAQ,KAAQ,OAAO,CAChD,OAASC,GAAG,CAAEC,MAAM,CAAEC,OAAO,CAAyBC,UAAU,CAAEC,OAAO,KAAQ,eAAe,CAChG,OAASC,YAAY,KAAqB,aAAa,CACvD,OAASC,UAAU,CAAEC,QAAQ,CAAEC,YAAY,KAAuC,6BAA6B,CAC/G,MAAO,CAAAC,cAAc,KAAoC,4BAA4B,CACrF,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAE3C,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/C,KAAM,CAAAC,aAAqG,CAAGC,IAAA,EAIxG,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,IAJyG,CAC9GC,KAAK,CAAEC,gBAAgB,CACvBC,gBAAgB,CAChBC,eACD,CAAC,CAAAR,IAAA,CACA,KAAM,CAAES,CAAC,CAAEC,SAAU,CAAC,CAAGlB,cAAc,CAAC,CAAC,CACzC,KAAM,CACL;AACAmB,yBAAyB,CAAEC,eAAe,CAC1CC,2BAA2B,CAAEC,oBAAoB,CACjDC,qBAAqB,CAAEC,YAAY,CACnCC,qBAAqB,CAAEC,YAAY,CACnCC,qBAAqB,CAAEC,YAAY,CACnCC,8BAA8B,CAC9BC,yBAAyB,CACzBC,4BAA4B,CAAEC,qBAAqB,CACnDC,yBAAyB,CAAEC,eAAe,CAC1CC,4BAA4B,CAAEC,kBAAkB,CAChDC,WAAW,CACXC,UAAU,CACVC,oBAAoB,CACpBC,oBAAoB,CACpBC,iBAAiB,CACjBC,eAAe,CACfC,qBACD,CAAC,CAAG9C,cAAc,CAAE+C,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG3D,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAAC4D,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7D,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAAC8D,YAAY,CAAEC,eAAe,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACgE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACkE,eAAe,CAAEC,kBAAkB,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,GAAI,CAAAoE,YAA4B,CAChC,KAAM,CAAAC,WAAW,CAAGA,CAACC,KAAoC,CAAEC,WAAmB,CAAEC,QAAgB,GAAK,CACpG,KAAM,CAAAC,MAAM,CAAGH,KAAK,CAACI,aAAa,CAClCf,WAAW,CAACc,MAAM,CAAC,CACnBxB,kBAAkB,CAAC,CAClBsB,WAAW,CACXC,QAAQ,CACRG,KAAK,CAAE,IACR,CAAC,CAAC,CAEF;AACAV,qBAAqB,CAACM,WAAW,CAAC,CAClCJ,kBAAkB,CAACK,QAAQ,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAI,WAAW,CAAGA,CAAA,GAAM,CACzBjB,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAAkB,0BAA0B,CAAIP,KAAoC,EAAK,CAC5ET,sBAAsB,CAACS,KAAK,CAACI,aAAa,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAIC,KAAkB,EAAK,CACjD;AACAhC,eAAe,CAACd,eAAe,CAACsC,WAAW,CAAE,OAAO,CAAE,CACrDS,eAAe,CAAED,KAAK,CAACE,GACxB,CAAC,CAAC,CAEF;AACAlC,eAAe,CAACd,eAAe,CAACsC,WAAW,CAAE,iBAAiB,CAAEQ,KAAK,CAACE,GAAG,CAAC,CAC3E,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAM,CACpCrB,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAsB,IAAI,CAAGC,OAAO,CAAC1B,QAAQ,CAAC,CAC9B;AACA;AACA,KAAM,CAAA2B,eAAe,CAAGD,OAAO,CAACxB,mBAAmB,CAAC,CAEpD,KAAM,CAAA0B,oBAAoB,CAAGA,CAC5Bf,WAAmB,CACnBC,QAAgB,CAChBe,SAAwB,CACxBZ,KAA6B,GACzB,CACJ;AACAtC,YAAY,CAACkC,WAAW,CAAEC,QAAQ,CAAEe,SAAS,CAAEZ,KAAK,CAAC,CACtD,CAAC,CAED,KAAM,CAAAa,kBAAkB,CAAGA,CAACjB,WAAmB,CAAEC,QAAgB,CAAEG,KAA6B,GAAK,CACpGtC,YAAY,CAACkC,WAAW,CAAEC,QAAQ,CAAE,MAAM,CAAEG,KAAK,CAAC,CAClDhB,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAA8B,kBAAkB,CAAIlB,WAAmB,EAAK,CACnDhC,YAAY,CAACgC,WAAW,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAmB,gBAAgB,CAAG/D,gBAAgB,CAACgE,OAAO,CAACC,MAAM,CAExD,KAAM,CAAAC,UAAU,CAAG9F,OAAO,CAAC,IAAM,CAChC,GAAI,CAAA+F,MAAM,CAAG,IAAI,CACjB,GAAI7D,eAAe,CAACuC,QAAQ,CAAE,CAC7BsB,MAAM,CAAGnE,gBAAgB,CAACgE,OAAO,CAACI,IAAI,CAAEC,IAAS,EAAKA,IAAI,CAACC,EAAE,GAAKhE,eAAe,CAACuC,QAAQ,CAAC,CAC5F,CACA,MAAO,CAAAsB,MAAM,CACd,CAAC,CAAE,CAAC7D,eAAe,CAACuC,QAAQ,CAAE7C,gBAAgB,CAACgE,OAAO,CAAC,CAAC,CAExD;AAEA;AACA;AACA,KAAM,CAAAO,oBAAoB,CAAGA,CAAA,GAAM,CAClCrD,qBAAqB,CAACZ,eAAe,CAACsC,WAAW,CAAC,CAClDhB,eAAe,CAAC,SAAS,CAAC,CAC1B,KAAM,CAAA4C,WAAW,CAAG,CAAE,GAAGvE,gBAAiB,CAAC,CAC3C,GAAIuE,WAAW,CAACC,SAAS,EAAID,WAAW,CAACC,SAAS,CAAClD,WAAW,CAAC,CAAE,CAChE,KAAM,CAAAmD,QAAQ,CAAG,CAAE,GAAGF,WAAW,CAACC,SAAS,CAAClD,WAAW,CAAG,CAAC,CAAE,CAAC,CAE9D;AACA,GAAImD,QAAQ,CAACC,MAAM,EAAID,QAAQ,CAACC,MAAM,CAACC,QAAQ,EAAIF,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAKC,SAAS,CAAE,CACnGJ,QAAQ,CAACC,MAAM,CAAG,CACjB,GAAGD,QAAQ,CAACC,MAAM,CAClBC,QAAQ,CAAE,CACT,GAAGF,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAC3BC,QAAQ,CAAE,EAAE,CACZE,QAAQ,CAAE,SACX,CACD,CAAC,CACF,CACAvD,UAAU,CAAC,EAAE,CAAC,CACd;AACAgD,WAAW,CAACC,SAAS,CAAG,CAAC,GAAGD,WAAW,CAACC,SAAS,CAAC,CAClDD,WAAW,CAACC,SAAS,CAAClD,WAAW,CAAG,CAAC,CAAC,CAAGmD,QAAQ,CACjDzE,gBAAgB,CAAGuE,WAAW,CAC/B,CAEAhD,UAAU,CAAC,EAAE,CAAC,CACdQ,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,KAAM,CAAAgD,sBAAsB,CAAIrC,KAAoC,EAAK,CACxE;AACA,KAAM,CAAAC,WAAW,CAAGtC,eAAe,CAACsC,WAAW,EAAIP,kBAAkB,CACrE,KAAM,CAAAQ,QAAQ,CAAGvC,eAAe,CAACuC,QAAQ,EAAIN,eAAe,CAE5DjB,kBAAkB,CAAC,CAClBsB,WAAW,CACXC,QAAQ,CACR;AACAG,KAAK,CAAEL,KAAK,CAACI,aACd,CAAC,CAAC,CACFE,WAAW,CAAC,CAAC,CACd,CAAC,CAED,KAAM,CAAAgC,uBAAuB,CAAGA,CAACC,YAAoB,CAAEC,SAAiB,GAAK,CAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA7D,kBAAkB,CAAC,CAClBsB,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,EAAE,CACZG,KAAK,CAAE,IACR,CAAC,CAAC,CACFhB,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED;AACA,KAAM,CAAAoD,sBAAsB,CAAGA,CAACC,QAAa,CAAEC,cAAmB,GAAK,CACtE,GAAID,QAAQ,GAAKC,cAAc,CAAE,KAAAC,iBAAA,CAAAC,qBAAA,CAChC,KAAM,CAAAC,UAAU,CAAG,CAAE,KAAAF,iBAAA,CAAGtF,gBAAgB,UAAAsF,iBAAA,kBAAAC,qBAAA,CAAhBD,iBAAA,CAAkBd,SAAS,UAAAe,qBAAA,iBAA3BA,qBAAA,CAA8BjE,WAAW,CAAG,CAAC,CAAC,CAAC,CAAC,CACxE,GAAIkE,UAAU,EAAIA,UAAU,CAACd,MAAM,CAAE,CACpCc,UAAU,CAACd,MAAM,CAACC,QAAQ,CAAG,CAC5BC,QAAQ,CAAE,EAAE,CACZa,UAAU,CAAE,EAAE,CACdC,WAAW,CAAE,EAAE,CACfZ,QAAQ,CAAE,SACX,CAAC,CACF,CACA9E,gBAAgB,CAACwE,SAAS,CAAClD,WAAW,CAAG,CAAC,CAAC,CAAGkE,UAAU,CACzD,CACD,CAAC,CAGD,KAAM,CAAAG,kBAAkB,CAAGA,CAC1BC,UAAe,CACfC,eAAuB,CACvBC,SAAiB,CACjBC,mBAA2B,CAC3BC,iBAAyB,CACzBC,WAAmB,GACf,CACJ;AACA,KAAM,CAAEtD,WAAW,CAAEC,QAAS,CAAC,CAAGvC,eAAe,CACjD;AACAI,YAAY,CAACkC,WAAW,CAAEC,QAAQ,CAAE,OAAO,CAAE,CAC5CQ,eAAe,CAAEwC,UAAU,CAACxC,eAAe,CAC3C8C,WAAW,CAAEN,UAAU,CAACM,WAAW,CACnC/C,KAAK,CAAEyC,UAAU,CAACzC,KACnB,CAAC,CAAC,CACFpC,yBAAyB,CAAC4B,WAAW,CAAEC,QAAQ,CAAE,CAChDG,KAAK,CAAE8C,eAAe,CACtBC,SAAS,CAAEA,SAAS,CACpBK,GAAG,CAAEF,WAAW,CAChBG,WAAW,CAAE,IACd,CAAC,CAAC,CACFtF,8BAA8B,CAAC6B,WAAW,CAAEC,QAAQ,CAAEmD,mBAAmB,CAAC,CAC1EtF,YAAY,CAACkC,WAAW,CAAEC,QAAQ,CAAE,MAAM,CAAEoD,iBAAiB,CAAC,CAE9D;AACA3E,kBAAkB,CAAC,CAAEsB,WAAW,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEG,KAAK,CAAE,IAAK,CAAC,CAAC,CAClEhB,WAAW,CAAC,IAAI,CAAC,CAClB,CAAC,CAED,mBACC1C,KAAA,CAAAE,SAAA,EAAA8G,QAAA,eACChH,KAAA,CAAChB,GAAG,EACHiI,SAAS,CAAE,KAAM,CACjBjC,EAAE,CAAEtE,gBAAgB,CAACsE,EAAG,CACxBkC,EAAE,CAAE,CACHC,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KAAK,CACdC,SAAS,CAAE,YAAY,CACvB1D,eAAe,CAAErD,gBAAgB,CAACgH,KAAK,CAAC3D,eAAe,CACvD4D,cAAc,CAAE,QACjB,CACA;AACA;AAAA,CAAAX,QAAA,EAECtG,gBAAgB,CAACgE,OAAO,CAACkD,GAAG,CAAE7C,IAAS,EAAK,CAC5C,mBACC/E,KAAA,CAAChB,GAAG,EACHkI,EAAE,CAAE,CACHW,QAAQ,CAAE,UAAU,CACpBR,OAAO,CAAE,MAAM,CACf;AACAM,cAAc,CAAE,QAChB;AACD,CAAE,CACFG,YAAY,CAAEA,CAAA,GAAM,CACnBhF,eAAe,CAAC,EAAE,CAAC,CACpB,CAAE,CAAAkE,QAAA,eAEFlH,IAAA,CAACb,MACA;AAAA,EACA8I,WAAW,CAAGC,CAAC,EAAK,CACnB,GAAIjD,IAAI,CAACT,SAAS,GAAK,KAAK,EAAI0D,CAAC,CAACvE,aAAa,CAACuB,EAAE,GAAKD,IAAI,CAACC,EAAE,CAAE,CAC/DlC,eAAe,CAACiC,IAAI,CAACC,EAAE,CAAC,CACzB,CACD,CAAE,CACFA,EAAE,CAAED,IAAI,CAACC,EAAG,CACZiD,OAAO,CAAE,WAAY,CACrBf,EAAE,CAAE,CACHgB,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,iBAAiB,CAC5BC,MAAM,CAAEtD,IAAI,CAAC2C,KAAK,CAACb,WAAW,CAC9B;AACA/C,KAAK,CAAE,GAAGiB,IAAI,CAAC2C,KAAK,CAAC5D,KAAK,EAAE,CAC5BwE,aAAa,CAAE,MAAM,CACrBd,OAAO,CAAE,oBAAoB,CAC7Be,UAAU,CAAE,0BAA0B,CACtCC,QAAQ,CAAE,iBAAiB,CAC3BzE,eAAe,CAAEgB,IAAI,CAAC2C,KAAK,CAAC3D,eAAe,CAC3CqD,KAAK,CAAE,aAAa,CACpB;AACA,SAAS,CAAE,CACVrD,eAAe,CAAEgB,IAAI,CAAC2C,KAAK,CAAC3D,eAAe,CAAE;AAC7C0E,OAAO,CAAE,GAAG,CAAE;AACdL,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CACFM,OAAO,CAAGV,CAAC,EAAK5E,WAAW,CAAC4E,CAAC,CAAEtH,gBAAgB,CAACsE,EAAE,CAAED,IAAI,CAACC,EAAE,CAAG;AAAA,CAAAgC,QAAA,CAE7DlG,SAAS,CAACiE,IAAI,CAAC4D,IAAI,CAAE,CAAEC,YAAY,CAAE7D,IAAI,CAAC4D,IAAK,CAAC,CAAC,CAC3C,CAAC,CAERjI,gBAAgB,CAACgE,OAAO,CAACC,MAAM,CAAG,CAAC,EAAI9B,YAAY,GAAKkC,IAAI,CAACC,EAAE,cAC/DlF,IAAA,CAACX,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZC,SAAS,CAAC,UAAU,CACpB5B,EAAE,CAAE,CACHW,QAAQ,CAAE,UAAU,CACpBkB,GAAG,CAAE,OAAO,CACZC,KAAK,CAAE,OAAO,CACdjF,eAAe,CAAE,MAAM,CACvB;AACA;AACAqE,SAAS,CAAE,gCAAgC,CAC3Ca,MAAM,CAAE,GAAG,CACXzB,OAAO,CAAG,gBAAgB,CAC1B,SAAS,CAAE,CACVzD,eAAe,CAAE,MAAM,CACvBqE,SAAS,CAAE,iBACZ,CAAC,CACDc,IAAI,CAAE,CACL/B,MAAM,CAAE,MACT,CAAC,CACDgC,GAAG,CAAE,CACJ/B,KAAK,CAAE,MAAM,CACbD,MAAM,CAAE,MAAM,CACdiC,IAAI,CAAE,CACLC,IAAI,CAAC,SACN,CACD,CACD,CAAE,CACFX,OAAO,CAAGV,CAAC,EAAK,KAAAsB,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACf3B,CAAC,CAAC4B,eAAe,CAAC,CAAC,CACnB9D,sBAAsB,CAACf,IAAI,CAACC,EAAE,EAAAsE,qBAAA,CAAElH,oBAAoB,CAACH,WAAW,CAAG,CAAC,CAAC,UAAAqH,qBAAA,kBAAAC,sBAAA,CAArCD,qBAAA,CAAuCO,MAAM,UAAAN,sBAAA,kBAAAC,sBAAA,CAA7CD,sBAAA,CAA+CO,QAAQ,UAAAN,sBAAA,iBAAvDA,sBAAA,CAAyDjE,QAAQ,CAAC,CAClG/D,YAAY,CACXuD,IAAI,CAACC,EAAE,CACPtE,gBAAgB,CAACsE,EAAE,EAAAyE,sBAAA,CACnBrH,oBAAoB,CAACH,WAAW,CAAG,CAAC,CAAC,UAAAwH,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuCI,MAAM,UAAAH,sBAAA,kBAAAC,sBAAA,CAA7CD,sBAAA,CAA+CI,QAAQ,UAAAH,sBAAA,iBAAvDA,sBAAA,CAAyDpE,QAC1D,CAAC,CACDjD,eAAe,CAAC,SAAS,CAAC,CAC1B;AACAC,qBAAqB,CAAC,KAAK,CAAC,CAC7B,CAAE,CAAAyE,QAAA,cAEFlH,IAAA,SAAMiK,uBAAuB,CAAE,CAAEC,MAAM,CAAE1K,UAAW,CAAE,CAAE,CAAC,CAC9C,CAAC,CACV,IAAI,EACJ,CAAC,CAER,CAAC,CAAC,CACDmF,gBAAgB,CAAG,CAAC,cACpB3E,IAAA,CAACX,UAAU,EACV+H,EAAE,CAAE,CACHnD,eAAe,CAAE,SAAS,CAC1BkG,MAAM,CAAE,SAAS,CACjBhB,MAAM,CAAE,IAAI,CACZzB,OAAO,CAAE,gBAAgB,CACzBY,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVrE,eAAe,CAAE,SAAS,CAC1BqE,SAAS,CAAE,iBAAmB;AAC/B,CACD,CACA;AAAA,CACAM,OAAO,CAAEA,CAAA,GAAMlE,kBAAkB,CAAC9D,gBAAgB,CAACsE,EAAE,CAAE,CAAAgC,QAAA,cAEvDlH,IAAA,CAACJ,OAAO,EACP8I,QAAQ,CAAC,OAAO,CAChBtB,EAAE,CAAE,CAAEpD,KAAK,CAAE,MAAO,CAAE,CACtB,CAAC,CACS,CAAC,CACV,IAAI,cAERhE,IAAA,CAACZ,OAAO,EACP8F,EAAE,CAAE,gBAAiB,CACrBd,IAAI,CAAEA,IACN;AAAA,CACAgG,OAAO,CAAEvG,WAAY,CACrBwG,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CAAE,CACfrB,GAAG,CAAE,CAAAtG,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4H,qBAAqB,CAAC,CAAC,CAACtB,GAAG,GAAI,CAAC,CAC/CuB,IAAI,CAAE,CAAA7H,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4H,qBAAqB,CAAC,CAAC,CAACC,IAAI,GAAI,CACjD,CAAE,CACFC,YAAY,CAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,MACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACb,CAAE,CACFE,SAAS,CAAE,CACVC,IAAI,CAAE,CACL;AACA1D,EAAE,CAAE,CACH+B,MAAM,CAAG4B,KAAK,EAAKA,KAAK,CAAC5B,MAAM,CAAC6B,OAAO,CAAG,IAC3C,CACD,CACD,CAAE,CAAA9D,QAAA,cAEFhH,KAAA,CAAChB,GAAG,EACHkI,EAAE,CAAE,CACHG,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVC,OAAO,CAAE,KACV,CAAE,CAAAR,QAAA,eA4BFlH,IAAA,CAACX,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZH,OAAO,CAAEhD,sBAAuB,CAChCwB,EAAE,CAAE,CACHkB,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVA,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAApB,QAAA,cAEFlH,IAAA,SAAMiK,uBAAuB,CAAE,CAAEC,MAAM,CAAExK,YAAa,CAAE,CACvDkI,KAAK,CAAE,CAACN,KAAK,CAAC,MAAM,CAAED,MAAM,CAAC,MAAM,CAAE,CACrC,CAAC,CACS,CAAC,cACbrH,IAAA,CAACV,OAAO,EAAC2L,KAAK,MACbC,KAAK,CAAC,kBAAkB,CACxBtD,KAAK,CAAE,CAAEuB,MAAM,CAAE,KAAM,CAAE,CAAAjC,QAAA,cAEzBlH,IAAA,CAACd,GAAG,EACHkI,EAAE,CAAE,CACHnD,eAAe,CACdrD,gBAAgB,CAACgH,KAAK,CAAC3D,eAAe,GAAK,SAAS,CACjDrD,gBAAgB,CAACgH,KAAK,CAAC3D,eAAe,CACtC,SAAS,CACbqD,KAAK,CAAE,MAAM,CACbD,MAAM,CAAE,MAAM,CACde,YAAY,CAAE,KAAK,CACnB;AACA+C,SAAS,CAAE,MACZ,CAAE,CACFhE,SAAS,CAAE,KAAM,CACjBiE,IAAI,CAAC,QAAQ,CACbxC,OAAO,CAAE9E,0BAA2B,CACpC,CAAC,CACM,CAAC,cACV9D,IAAA,CAACX,UAAU,EACV0J,IAAI,CAAC,OAAO,CACZH,OAAO,CAAEA,CAAA,GAAM,CACdxH,oBAAoB,CAACF,eAAe,CAACsC,WAAW,CAAC,CACjDZ,WAAW,CAAC,IAAI,CAAC,CAClB,CAAE,CACFyI,QAAQ,CAAEvK,eAAgB,CAC1BoK,KAAK,CAAEpK,eAAe,CAAG,4CAA4C,CAAG,eAAgB,CACxFsG,EAAE,CAAE,CACHkB,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVA,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAApB,QAAA,cAEFlH,IAAA,SACCiK,uBAAuB,CAAE,CAAEC,MAAM,CAAEzK,QAAS,CAAE,CAC9CmI,KAAK,CAAE,CAAEe,OAAO,CAAE7H,eAAe,CAAG,GAAG,CAAG,CAAC,CAAEwG,KAAK,CAAC,MAAM,CAAED,MAAM,CAAC,MAAO,CAAE,CAC3E,CAAC,CACS,CAAC,cACbrH,IAAA,CAACX,UAAU,EACV0J,IAAI,CAAC,OACL;AAAA,CACAH,OAAO,CAAEzD,oBAAqB,CAC9BkG,QAAQ,CAAE,EAAA9K,sBAAA,CAAA+B,oBAAoB,CAACH,WAAW,CAAG,CAAC,CAAC,UAAA5B,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuC+K,UAAU,UAAA9K,sBAAA,iBAAjDA,sBAAA,CAAmDqE,MAAM,IAAK,CAAE,CAC1EuC,EAAE,CAAE,CACHkB,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACVA,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAApB,QAAA,cAEFlH,IAAA,SACCiK,uBAAuB,CAAE,CAAEC,MAAM,CAAE1K,UAAW,CAAE,CAChDoI,KAAK,CAAE,CACNe,OAAO,CAAE,EAAAlI,sBAAA,CAAA6B,oBAAoB,CAACH,WAAW,CAAG,CAAC,CAAC,UAAA1B,sBAAA,kBAAAC,uBAAA,CAArCD,sBAAA,CAAuC6K,UAAU,UAAA5K,uBAAA,iBAAjDA,uBAAA,CAAmDmE,MAAM,IAAK,CAAC,CAAG,GAAG,CAAG,CAAC,CAClF0G,aAAa,CAAE,MAAM,CACrBjE,KAAK,CAAE,MAAM,CACbD,MAAM,CAAE,MACT,CAAE,CACF,CAAC,CACS,CAAC,EACT,CAAC,CACE,CAAC,cAEVrH,IAAA,CAACH,aAAa,EACbgG,uBAAuB,CAAEA,uBAAwB,CACjD3E,eAAe,CAAEA,eAAgB,CACjC4D,UAAU,CAAEA,UAAW,CACvB0B,kBAAkB,CAAEA,kBAAmB,CACvC3F,gBAAgB,CAAEA,gBAAiB,CACnC,CAAC,EACE,CAAC,cAGNb,IAAA,CAACZ,OAAO,EACPgF,IAAI,CAAEE,eAAgB,CACtB3B,QAAQ,CAAEE,mBAAoB,CAC9BuH,OAAO,CAAEjG,sBAAuB,CAChCsG,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFC,eAAe,CAAE,CAChBF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACFzF,EAAE,CAAC,cAAc,CACjB2F,SAAS,CAAE,CACVC,IAAI,CAAE,CACL;AACA1D,EAAE,CAAE,CACH+B,MAAM,CAAG4B,KAAK,EAAKA,KAAK,CAAC5B,MAAM,CAAC6B,OAAO,CAAG,IAC3C,CACD,CACD,CAAE,CAAA9D,QAAA,cAEFhH,KAAA,CAAChB,GAAG,EAAAgI,QAAA,eACHlH,IAAA,CAACT,YAAY,EACZyE,KAAK,CAAEpD,gBAAgB,CAACgH,KAAK,CAAC3D,eAAgB,CAC9CuH,QAAQ,CAAEzH,iBAAkB,CAC5B,CAAC,cACF/D,IAAA,UAAAkH,QAAA,CACE;AACP;AACA;AACA;AACA,KAAK,CACO,CAAC,EACJ,CAAC,CACE,CAAC,EACT,CAAC,CAEL,CAAC,CAED,cAAe,CAAA7G,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}