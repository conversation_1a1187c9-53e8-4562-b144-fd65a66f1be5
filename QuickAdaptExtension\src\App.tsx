import React, { useEffect, useState } from 'react';
import "./App.scss";
import GuidePopup from "./components/guideSetting/GuidePopUp";
import Drawer from "./components/drawer/Drawer";
import { AuthProvider } from "./components/auth/AuthProvider";
import { AccountProvider } from "./components/login/AccountContext";
import { SnackbarProvider } from "./components/guideSetting/guideList/SnackbarContext";
import { TranslationProvider } from "./contexts/TranslationContext";
import Rte from "./components/guideSetting/RTE";
import jwtDecode from "jwt-decode";
import useInfoStore from "./store/UserInfoStore";
import { initializeI18n } from "./multilinguial/i18n";
import ExtensionLoader from "./components/common/ExtensionLoader";
import ExtensionPopupLoader from "./components/common/ExtensionPopupLoader";
import { useExtensionInitialization } from "./hooks/useExtensionInitialization";

function App() {
	const [isI18nReady, setIsI18nReady] = useState(false);
	const [isLoginPageReady, setIsLoginPageReady] = useState(false);
	const accessToken = useInfoStore((state) => state.accessToken);
	const { clearAll, clearAccessToken } = useInfoStore.getState();
	const { isInitializing: isExtensionInitializing, isFirstLoad, hideInitializationLoader } = useExtensionInitialization();

	// Initialize i18n once when app starts
	useEffect(() => {
		const setupI18n = async () => {
			try {
				await initializeI18n();
				console.log('✅ i18n ready for use');
				setIsI18nReady(true);
			} catch (error) {
				console.error('❌ Failed to initialize i18n:', error);
				// Set ready anyway to prevent infinite loading
				setIsI18nReady(true);
			}
		};

		setupI18n();
	}, []);

	// Check token validity and login page readiness
	useEffect(() => {
		if (accessToken) {
			try {
				const decodedToken: any = jwtDecode(accessToken);
				const currentTime = Math.floor(Date.now() / 1000);
				if (decodedToken.exp < currentTime) {
					console.log('🔐 Token expired, clearing session');
					clearAll();
					clearAccessToken();
				}
			} catch (error) {
				console.error('❌ Invalid token, clearing session:', error);
				clearAll();
				clearAccessToken();
			}
		}

		// Set login page ready state - if no token, login page will be shown
		// If i18n is ready and we don't have a valid token, login page is ready
		if (isI18nReady && !accessToken) {
			setIsLoginPageReady(true);
			// Hide the initialization loader when login page is ready
			hideInitializationLoader();
		}
	}, [accessToken, clearAll, clearAccessToken, isI18nReady, hideInitializationLoader]);

	// Show loading until i18n is ready
	if (!isI18nReady) {
		return <ExtensionLoader message="Initializing QuickAdapt Extension..." />;
	}

	return (
		<div className="App">
			{/* Show popup loader on first extension load, but hide when login page is ready */}
			{isFirstLoad && isExtensionInitializing && !isLoginPageReady && (
				<ExtensionPopupLoader
					message="Extension is starting up..."
					duration={3000}
					position="top-right"
				/>
			)}

			<TranslationProvider>
				<AuthProvider>
					<AccountProvider>
						<SnackbarProvider>
							<Drawer />
						</SnackbarProvider>
					</AccountProvider>
				</AuthProvider>
			</TranslationProvider>
		</div>
	);
}

export default App;
