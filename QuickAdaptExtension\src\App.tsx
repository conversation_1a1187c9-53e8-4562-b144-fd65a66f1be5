import React, { useEffect } from 'react';
import "./App.scss";
import GuidePopup from "./components/guideSetting/GuidePopUp";
import Drawer from "./components/drawer/Drawer";
import { AuthProvider } from "./components/auth/AuthProvider";
import { AccountProvider } from "./components/login/AccountContext";
import { SnackbarProvider } from "./components/guideSetting/guideList/SnackbarContext";
import { TranslationProvider } from "./contexts/TranslationContext";
import Rte from "./components/guideSetting/RTE";
import jwtDecode from "jwt-decode";
import useInfoStore from "./store/UserInfoStore";
import { initializeI18n } from "./multilinguial/i18n";
import ExtensionPopupLoader from "./components/common/ExtensionPopupLoader";
import { useExtensionInitialization } from "./hooks/useExtensionInitialization";

function App() {
	const accessToken = useInfoStore((state) => state.accessToken);
	const { clearAll, clearAccessToken } = useInfoStore.getState();
	const { isInitializing: isExtensionInitializing, isFirstLoad } = useExtensionInitialization();

	// Initialize i18n once when app starts
	useEffect(() => {
		const setupI18n = async () => {
			try {
				await initializeI18n();
				console.log('✅ i18n ready for use');
			} catch (error) {
				console.error('❌ Failed to initialize i18n:', error);
			}
		};

		setupI18n();
	}, []);

	// Check token validity
	useEffect(() => {
		if (accessToken) {
			try {
				const decodedToken: any = jwtDecode(accessToken);
				const currentTime = Math.floor(Date.now() / 1000);
				if (decodedToken.exp < currentTime) {
					console.log('🔐 Token expired, clearing session');
					clearAll();
					clearAccessToken();
				}
			} catch (error) {
				console.error('❌ Invalid token, clearing session:', error);
				clearAll();
				clearAccessToken();
			}
		}
	}, [accessToken, clearAll, clearAccessToken]);

	return (
		<div className="App">
			{/* Show popup loader on first extension load before extension page loads */}
			{isFirstLoad && isExtensionInitializing && (
				<ExtensionPopupLoader
					message="Extension is starting up..."
					duration={3000}
					position="top-right"
				/>
			)}

			<TranslationProvider>
				<AuthProvider>
					<AccountProvider>
						<SnackbarProvider>
							<Drawer />
						</SnackbarProvider>
					</AccountProvider>
				</AuthProvider>
			</TranslationProvider>
		</div>
	);
}

export default App;
