import React, { useEffect, useState } from 'react';
import "./App.scss";
import GuidePopup from "./components/guideSetting/GuidePopUp";
import Drawer from "./components/drawer/Drawer";
import { AuthProvider } from "./components/auth/AuthProvider";
import { AccountProvider } from "./components/login/AccountContext";
import { SnackbarProvider } from "./components/guideSetting/guideList/SnackbarContext";
import { TranslationProvider } from "./contexts/TranslationContext";
import Rte from "./components/guideSetting/RTE";
import jwtDecode from "jwt-decode";
import useInfoStore from "./store/UserInfoStore";
import { initializeI18n } from "./multilinguial/i18n";
import ExtensionPopupLoader from "./components/common/ExtensionPopupLoader";
import { useExtensionInitialization } from "./hooks/useExtensionInitialization";

function App() {
	const [isI18nReady, setIsI18nReady] = useState(false);
	const accessToken = useInfoStore((state) => state.accessToken);
	const { clearAll, clearAccessToken } = useInfoStore.getState();
	const { isInitializing: isExtensionInitializing, isFirstLoad } = useExtensionInitialization();

	// Initialize i18n once when app starts
	useEffect(() => {
		const setupI18n = async () => {
			try {
				await initializeI18n();
				console.log('✅ i18n ready for use');
				setIsI18nReady(true);
			} catch (error) {
				console.error('❌ Failed to initialize i18n:', error);
				// Set ready anyway to prevent infinite loading
				setIsI18nReady(true);
			}
		};

		setupI18n();
	}, []);

	// Check token validity
	useEffect(() => {
		if (accessToken) {
			try {
				const decodedToken: any = jwtDecode(accessToken);
				const currentTime = Math.floor(Date.now() / 1000);
				if (decodedToken.exp < currentTime) {
					console.log('🔐 Token expired, clearing session');
					clearAll();
					clearAccessToken();
				}
			} catch (error) {
				console.error('❌ Invalid token, clearing session:', error);
				clearAll();
				clearAccessToken();
			}
		}
	}, [accessToken, clearAll, clearAccessToken]);

	// Show popup loader until i18n is ready or during extension initialization
	const shouldShowPopupLoader = !isI18nReady || (isFirstLoad && isExtensionInitializing);

	return (
		<div className="App">
			{/* Show popup loader before extension page loads */}
			{shouldShowPopupLoader && (
				<ExtensionPopupLoader
					duration={!isI18nReady ? 5000 : 3000}
					position="top-right"
				/>
			)}

			{/* Only render main components when i18n is ready */}
			{isI18nReady && (
				<TranslationProvider>
					<AuthProvider>
						<AccountProvider>
							<SnackbarProvider>
								<Drawer />
							</SnackbarProvider>
						</AccountProvider>
					</AuthProvider>
				</TranslationProvider>
			)}
		</div>
	);
}

export default App;
