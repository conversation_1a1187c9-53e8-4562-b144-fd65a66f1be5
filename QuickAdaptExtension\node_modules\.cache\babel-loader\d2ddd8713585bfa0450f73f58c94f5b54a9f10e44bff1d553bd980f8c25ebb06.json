{"ast": null, "code": "import{useState,useEffect}from'react';export const useExtensionInitialization=()=>{const[state,setState]=useState({isInitializing:true,isFirstLoad:true,initializationComplete:false});useEffect(()=>{// Check if this is the first time the extension is being loaded\nconst hasBeenInitialized=sessionStorage.getItem('quickadapt-initialized');if(!hasBeenInitialized){// First time loading - show popup loader\nsetState(prev=>({...prev,isFirstLoad:true,isInitializing:true}));// Simulate initialization process\nconst initializationSteps=[{delay:500,message:'Loading extension...'},{delay:1000,message:'Initializing components...'},{delay:1500,message:'Setting up environment...'},{delay:2000,message:'Almost ready...'},{delay:2500,message:'Complete!'}];// Complete initialization after all steps\nsetTimeout(()=>{setState(prev=>({...prev,isInitializing:false,initializationComplete:true}));// Mark as initialized in session storage\nsessionStorage.setItem('quickadapt-initialized','true');},3000);}else{// Already initialized in this session - skip popup\nsetState({isInitializing:false,isFirstLoad:false,initializationComplete:true});}},[]);const resetInitialization=()=>{sessionStorage.removeItem('quickadapt-initialized');setState({isInitializing:true,isFirstLoad:true,initializationComplete:false});};const hideInitializationLoader=()=>{setState(prev=>({...prev,isInitializing:false}));};return{...state,resetInitialization,hideInitializationLoader};};", "map": {"version": 3, "names": ["useState", "useEffect", "useExtensionInitialization", "state", "setState", "isInitializing", "isFirstLoad", "initializationComplete", "hasBeenInitialized", "sessionStorage", "getItem", "prev", "initializationSteps", "delay", "message", "setTimeout", "setItem", "resetInitialization", "removeItem", "hideInitializationLoader"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/hooks/useExtensionInitialization.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\ninterface ExtensionInitializationState {\n  isInitializing: boolean;\n  isFirstLoad: boolean;\n  initializationComplete: boolean;\n}\n\nexport const useExtensionInitialization = () => {\n  const [state, setState] = useState<ExtensionInitializationState>({\n    isInitializing: true,\n    isFirstLoad: true,\n    initializationComplete: false\n  });\n\n  useEffect(() => {\n    // Check if this is the first time the extension is being loaded\n    const hasBeenInitialized = sessionStorage.getItem('quickadapt-initialized');\n    \n    if (!hasBeenInitialized) {\n      // First time loading - show popup loader\n      setState(prev => ({\n        ...prev,\n        isFirstLoad: true,\n        isInitializing: true\n      }));\n\n      // Simulate initialization process\n      const initializationSteps = [\n        { delay: 500, message: 'Loading extension...' },\n        { delay: 1000, message: 'Initializing components...' },\n        { delay: 1500, message: 'Setting up environment...' },\n        { delay: 2000, message: 'Almost ready...' },\n        { delay: 2500, message: 'Complete!' }\n      ];\n\n      // Complete initialization after all steps\n      setTimeout(() => {\n        setState(prev => ({\n          ...prev,\n          isInitializing: false,\n          initializationComplete: true\n        }));\n        \n        // Mark as initialized in session storage\n        sessionStorage.setItem('quickadapt-initialized', 'true');\n      }, 3000);\n\n    } else {\n      // Already initialized in this session - skip popup\n      setState({\n        isInitializing: false,\n        isFirstLoad: false,\n        initializationComplete: true\n      });\n    }\n  }, []);\n\n  const resetInitialization = () => {\n    sessionStorage.removeItem('quickadapt-initialized');\n    setState({\n      isInitializing: true,\n      isFirstLoad: true,\n      initializationComplete: false\n    });\n  };\n\n  const hideInitializationLoader = () => {\n    setState(prev => ({\n      ...prev,\n      isInitializing: false\n    }));\n  };\n\n  return {\n    ...state,\n    resetInitialization,\n    hideInitializationLoader\n  };\n};\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAQ3C,MAAO,MAAM,CAAAC,0BAA0B,CAAGA,CAAA,GAAM,CAC9C,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGJ,QAAQ,CAA+B,CAC/DK,cAAc,CAAE,IAAI,CACpBC,WAAW,CAAE,IAAI,CACjBC,sBAAsB,CAAE,KAC1B,CAAC,CAAC,CAEFN,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAO,kBAAkB,CAAGC,cAAc,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAE3E,GAAI,CAACF,kBAAkB,CAAE,CACvB;AACAJ,QAAQ,CAACO,IAAI,GAAK,CAChB,GAAGA,IAAI,CACPL,WAAW,CAAE,IAAI,CACjBD,cAAc,CAAE,IAClB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAO,mBAAmB,CAAG,CAC1B,CAAEC,KAAK,CAAE,GAAG,CAAEC,OAAO,CAAE,sBAAuB,CAAC,CAC/C,CAAED,KAAK,CAAE,IAAI,CAAEC,OAAO,CAAE,4BAA6B,CAAC,CACtD,CAAED,KAAK,CAAE,IAAI,CAAEC,OAAO,CAAE,2BAA4B,CAAC,CACrD,CAAED,KAAK,CAAE,IAAI,CAAEC,OAAO,CAAE,iBAAkB,CAAC,CAC3C,CAAED,KAAK,CAAE,IAAI,CAAEC,OAAO,CAAE,WAAY,CAAC,CACtC,CAED;AACAC,UAAU,CAAC,IAAM,CACfX,QAAQ,CAACO,IAAI,GAAK,CAChB,GAAGA,IAAI,CACPN,cAAc,CAAE,KAAK,CACrBE,sBAAsB,CAAE,IAC1B,CAAC,CAAC,CAAC,CAEH;AACAE,cAAc,CAACO,OAAO,CAAC,wBAAwB,CAAE,MAAM,CAAC,CAC1D,CAAC,CAAE,IAAI,CAAC,CAEV,CAAC,IAAM,CACL;AACAZ,QAAQ,CAAC,CACPC,cAAc,CAAE,KAAK,CACrBC,WAAW,CAAE,KAAK,CAClBC,sBAAsB,CAAE,IAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAU,mBAAmB,CAAGA,CAAA,GAAM,CAChCR,cAAc,CAACS,UAAU,CAAC,wBAAwB,CAAC,CACnDd,QAAQ,CAAC,CACPC,cAAc,CAAE,IAAI,CACpBC,WAAW,CAAE,IAAI,CACjBC,sBAAsB,CAAE,KAC1B,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAY,wBAAwB,CAAGA,CAAA,GAAM,CACrCf,QAAQ,CAACO,IAAI,GAAK,CAChB,GAAGA,IAAI,CACPN,cAAc,CAAE,KAClB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,MAAO,CACL,GAAGF,KAAK,CACRc,mBAAmB,CACnBE,wBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}