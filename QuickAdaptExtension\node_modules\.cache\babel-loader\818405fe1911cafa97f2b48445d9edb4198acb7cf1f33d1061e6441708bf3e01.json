{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\ExtensionPopupLoader.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\nimport loaderSpinner from '../../assets/loader-spinner.gif';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExtensionPopupLoader = ({\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + 100 / (duration / 50); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popup-loader-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"popup-close-btn-minimal\",\n        onClick: () => setIsVisible(false),\n        \"aria-label\": \"Close\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup-gif-content\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: loaderSpinner,\n          alt: \"Loading...\",\n          className: \"popup-loader-gif\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(ExtensionPopupLoader, \"B3Heh1vmVMOVYZF5HYm7udhWEsk=\");\n_c = ExtensionPopupLoader;\nexport default ExtensionPopupLoader;\nvar _c;\n$RefreshReg$(_c, \"ExtensionPopupLoader\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "loaderSpinner", "jsxDEV", "_jsxDEV", "ExtensionPopup<PERSON><PERSON>der", "duration", "onComplete", "position", "_s", "isVisible", "setIsVisible", "progress", "setProgress", "progressInterval", "setInterval", "prev", "clearInterval", "hide<PERSON><PERSON>r", "setTimeout", "clearTimeout", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "_c", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionPopupLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\nimport loaderSpinner from '../../assets/loader-spinner.gif';\n\ninterface ExtensionPopupLoaderProps {\n  duration?: number;\n  onComplete?: () => void;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\n}\n\nconst ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + (100 / (duration / 50)); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>\n      <div className=\"popup-loader-container\">\n        {/* Close button */}\n        <button\n          className=\"popup-close-btn-minimal\"\n          onClick={() => setIsVisible(false)}\n          aria-label=\"Close\"\n        >\n          ×\n        </button>\n\n        {/* GIF Content */}\n        <div className=\"popup-gif-content\">\n          <img\n            src={loaderSpinner}\n            alt=\"Loading...\"\n            className=\"popup-loader-gif\"\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExtensionPopupLoader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,4BAA4B;AACnC,OAAOC,aAAa,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5D,MAAMC,oBAAyD,GAAGA,CAAC;EACjEC,QAAQ,GAAG,IAAI;EACfC,UAAU;EACVC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAE3CD,SAAS,CAAC,MAAM;IACd;IACA,MAAMc,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACzCF,WAAW,CAACG,IAAI,IAAI;QAClB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfC,aAAa,CAACH,gBAAgB,CAAC;UAC/B,OAAO,GAAG;QACZ;QACA,OAAOE,IAAI,GAAI,GAAG,IAAIV,QAAQ,GAAG,EAAE,CAAE,CAAC,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;;IAEN;IACA,MAAMY,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCR,YAAY,CAAC,KAAK,CAAC;MACnB,IAAIJ,UAAU,EAAE;QACdY,UAAU,CAACZ,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC,EAAED,QAAQ,CAAC;IAEZ,OAAO,MAAM;MACXW,aAAa,CAACH,gBAAgB,CAAC;MAC/BM,YAAY,CAACF,SAAS,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACZ,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAE1B,IAAI,CAACG,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACEN,OAAA;IAAKiB,SAAS,EAAE,0BAA0Bb,QAAQ,IAAIE,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAG;IAAAY,QAAA,eACvFlB,OAAA;MAAKiB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErClB,OAAA;QACEiB,SAAS,EAAC,yBAAyB;QACnCE,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAAC,KAAK,CAAE;QACnC,cAAW,OAAO;QAAAW,QAAA,EACnB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTvB,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChClB,OAAA;UACEwB,GAAG,EAAE1B,aAAc;UACnB2B,GAAG,EAAC,YAAY;UAChBR,SAAS,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA3DIJ,oBAAyD;AAAAyB,EAAA,GAAzDzB,oBAAyD;AA6D/D,eAAeA,oBAAoB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}