// Content script to show popup loader when extension is first enabled
import React from 'react';
import ReactDOM from 'react-dom/client';
import ExtensionPopupLoader from '../components/common/ExtensionPopupLoader';

// Check if this is the first time the extension is being loaded on this page
const showExtensionStartupLoader = () => {
  // Check if popup has already been shown in this session
  const hasShownPopup = sessionStorage.getItem('quickadapt-popup-shown');
  
  if (!hasShownPopup) {
    // Create container for the popup loader
    const popupContainer = document.createElement('div');
    popupContainer.id = 'quickadapt-extension-popup';
    popupContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 999999;
    `;
    
    // Append to body
    document.body.appendChild(popupContainer);
    
    // Create React root and render popup loader
    const root = ReactDOM.createRoot(popupContainer);
    
    const PopupComponent = React.createElement(ExtensionPopupLoader, {
     // message: "QuickAdapt Extension enabled!",
      duration: 3500,
      position: 'top-right' as const,
      onComplete: () => {
        // Clean up when popup is done
        setTimeout(() => {
          if (popupContainer && popupContainer.parentNode) {
            popupContainer.parentNode.removeChild(popupContainer);
          }
        }, 300);
      }
    });
    
    root.render(PopupComponent);
    
    // Mark popup as shown
    sessionStorage.setItem('quickadapt-popup-shown', 'true');
  }
};

// Function to inject CSS for the popup loader
const injectPopupStyles = () => {
  const styleId = 'quickadapt-popup-styles';
  
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* Extension Popup Loader Styles */
      #quickadapt-extension-popup * {
        box-sizing: border-box;
      }
      
      .extension-popup-loader {
        position: fixed;
        z-index: 999999;
        width: 280px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        pointer-events: auto;
      }
      
      .extension-popup-loader.top-right {
        top: 20px;
        right: 20px;
      }
      
      .extension-popup-loader.visible {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
      
      .extension-popup-loader.hidden {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        pointer-events: none;
      }
      
      .popup-loader-container {
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      
      .popup-loader-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px 8px 16px;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        color: white;
      }
      
      .popup-loader-content {
        padding: 16px;
      }
      
      .popup-loader-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4px;
      }
      
      .popup-loader-message {
        font-size: 13px;
        color: #6b7280;
        margin-bottom: 12px;
        line-height: 1.4;
      }
      
      .popup-progress-container {
        width: 100%;
        height: 4px;
        background: #f3f4f6;
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 8px;
      }
      
      .popup-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
        border-radius: 2px;
        transition: width 0.3s ease;
      }
      
      .popup-loader-status {
        font-size: 11px;
        color: #9ca3af;
        text-align: center;
        font-weight: 500;
      }
      
      .popup-loader-footer {
        padding: 8px 16px;
        background: #f9fafb;
        border-top: 1px solid #f3f4f6;
        display: flex;
        justify-content: center;
      }
      
      .extension-badge {
        background: #e5e7eb;
        color: #6b7280;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    `;
    
    document.head.appendChild(style);
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    injectPopupStyles();
    showExtensionStartupLoader();
  });
} else {
  injectPopupStyles();
  showExtensionStartupLoader();
}

export { showExtensionStartupLoader, injectPopupStyles };
