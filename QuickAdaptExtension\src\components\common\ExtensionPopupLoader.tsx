import React, { useEffect, useState } from 'react';
import './ExtensionPopupLoader.css';

interface ExtensionPopupLoaderProps {
  message?: string;
  duration?: number;
  onComplete?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({
  message = "QuickAdapt Extension Loading...",
  duration = 3000,
  onComplete,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Animate progress bar
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + (100 / (duration / 50)); // Update every 50ms
      });
    }, 50);

    // Auto hide after duration
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      if (onComplete) {
        setTimeout(onComplete, 300); // Wait for fade out animation
      }
    }, duration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(hideTimer);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>
      <div className="popup-loader-container">
        {/* Header with icon and close button */}
        <div className="popup-loader-header">
          <div className="popup-loader-icon">
            <div className="icon-spinner">
              <div className="spinner-dot"></div>
              <div className="spinner-dot"></div>
              <div className="spinner-dot"></div>
            </div>
          </div>
          <button 
            className="popup-close-btn"
            onClick={() => setIsVisible(false)}
            aria-label="Close"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="popup-loader-content">
          <div className="popup-loader-title">QuickAdapt</div>

          {/* Main content spinner */}
          <div className="popup-main-spinner-container">
            <div className="popup-main-spinner">
              <div className="spinner-ring"></div>
              <div className="spinner-ring"></div>
              <div className="spinner-ring"></div>
            </div>
          </div>

          <div className="popup-loader-message">{message}</div>

          {/* Progress bar */}
          <div className="popup-progress-container">
            <div
              className="popup-progress-bar"
              style={{ width: `${progress}%` }}
            ></div>
          </div>

          {/* Status text */}
          <div className="popup-loader-status">
            {progress < 30 && "Initializing..."}
            {progress >= 30 && progress < 70 && "Loading components..."}
            {progress >= 70 && progress < 100 && "Almost ready..."}
            {progress >= 100 && "Ready!"}
          </div>
        </div>

        {/* Extension logo/branding area */}
        <div className="popup-loader-footer">
          <div className="extension-badge">Extension</div>
        </div>
      </div>
    </div>
  );
};

export default ExtensionPopupLoader;
