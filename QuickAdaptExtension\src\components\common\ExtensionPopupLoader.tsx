import React, { useEffect, useState } from 'react';
import './ExtensionPopupLoader.css';
import LottieSpinner from './LottieSpinner';

interface ExtensionPopupLoaderProps {
  duration?: number;
  onComplete?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

const ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({
  duration = 3000,
  onComplete,
  position = 'top-right'
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    // Animate progress bar
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + (100 / (duration / 50)); // Update every 50ms
      });
    }, 50);

    // Auto hide after duration
    const hideTimer = setTimeout(() => {
      setIsVisible(false);
      if (onComplete) {
        setTimeout(onComplete, 300); // Wait for fade out animation
      }
    }, duration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(hideTimer);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>
      <div className="popup-loader-container">
        {/* Close button */}
        <button
          className="popup-close-btn-minimal"
          onClick={() => setIsVisible(false)}
          aria-label="Close"
        >
          ×
        </button>

        {/* Lottie Spinner Content */}
        <div className="popup-lottie-content">
          <LottieSpinner size={window.innerWidth <= 480 ? 50 : 60} />
        </div>
      </div>
    </div>
  );
};

export default ExtensionPopupLoader;
