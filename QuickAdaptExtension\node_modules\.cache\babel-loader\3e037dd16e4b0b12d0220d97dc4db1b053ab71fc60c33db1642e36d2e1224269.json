{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\Qadpt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\login\\\\ExtensionLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { TextField, Button, IconButton, InputAdornment, FormHelperText } from '@mui/material';\nimport { GetUserDetails } from '../../services/UserService';\nimport JSEncrypt from 'jsencrypt';\nimport { LoginService } from '../../services/LoginService';\nimport { getOrganizationById } from '../../services/OrganizationService';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport useInfoStore from \"../../store/UserInfoStore\";\nimport userSession from \"../../store/userSession\";\nimport { pwdeye, eyeclose } from '../../assets/icons/icons';\nimport '../login/ExtensionLogin.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  setAccessToken,\n  setOidcInfo,\n  setUser,\n  setOrgDetails,\n  setUserType\n} = useInfoStore.getState();\nconst {\n  setHasAnnouncementOpened,\n  hasAnnouncementOpened\n} = userSession.getState();\nconst {\n  clearAll,\n  clearAccessToken\n} = useInfoStore.getState();\nconst {\n  clearGuideDetails,\n  setActiveMenu,\n  setSearchText\n} = useDrawerStore.getState();\nconst {\n  clearUserSession\n} = userSession.getState();\nlet userLocalData = {};\nlet SAinitialsData;\nlet userDetails;\nconst ExtensionLogin = ({\n  setIsLoggedIn\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState(null);\n  const [loginUserDetails, setUserDetails] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // const loginStyles = {\n  //   qadptDrawerContent: {\n  //     width: '100%',\n  //     backgroundColor: 'var(--ext - background)',\n  //     marginTop: '20px',\n  //   },\n  //   qadptWelcomeMessage: {\n  //     fontWeight: \"600\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //     padding: \"14px\",\n  //   },\n  //       // container: {\n  //   //   padding: '10px',\n  //   //   backgroundColor: '#F6EEEE',\n  //   //   borderRadius: '8px',\n  //   // },\n  //   // welcomeText: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 600,\n  //   //   lineHeight: '24px',\n  //   //   textAlign: 'left',\n  //   //   color: 'rgba(34, 34, 34, 1)',\n  //   // },\n  //   // headerText: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '14px',\n  //   //   fontWeight: 400,\n  //   //   lineHeight: '24px',\n  //   //   textAlign: 'left',\n  //   //   color: 'rgba(68, 68, 68, 1)',\n  //   //   marginTop: '10px',\n  //   // },\n  //   // textField: {\n  //   //   width: '100%',\n  //   //   backgroundColor: 'rgba(255, 255, 255, 1)',\n  //   //   borderRadius: '6px',\n  //   //   height: '46px',\n  //   // },\n  //   // textFieldInput: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 400,\n  //   //   color: 'rgba(68, 68, 68, 1)',\n  //   //   padding: '12px',\n  //   //   border: '1px solid rgba(213, 213, 213, 1)',\n  //   //   borderRadius: '6px',\n  //   //   height: '46px',\n  //   // },\n  //   // loginButton: {\n  //   //   backgroundColor: '#5F9EA0',\n  //   //   color: '#fff',\n  //   //   borderRadius: '25px',\n  //   //   width: '100%',\n  //   //   marginTop: '20px',\n  //   //   textTransform: 'none',\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 500,\n  //   // },\n  //   // qadptTextdanger: {\n  //   //   color: '#d9534f',\n  //   //   fontSize: '0.9rem',\n  //   // },\n\n  //   qadptLoginForm: {\n  //     marginTop: '20px',\n  //     padding: '0px 10px 20px 10px', \n  //   },\n  //   qadptFormLabel: {\n  //     fontSize: \"14px\",\n  //     marginTop: \"10px\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //   },\n  //   qadptInvalidCreds: {\n  //     fontSize: \"14px\",\n  //   },\n  //       // qadpteyeicon: {\n  //   //   \"& .MuiIconButton-root\": {\n  //   //     backgroundColor: \"transparent !important\",\n  //   //     border: \"none !important\",\n  //   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\n  //   //   }\n  //   // },\n\n  //   qadptForgotPwd: {\n  //     color: \"var(--primarycolor)\", \n  //     cursor: \"pointer\",\n  //     fontSize: \"16px\",\n  //     fontWeight: \"400\",\n  //     lineHeight: \"24px\",\n  //     marginTop: \"10px\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //   },\n  //   qadptBtn: {\n  //     backgroundColor: \"var(--primarycolor) !important\",  \n  //     color: \"#fff\",\n  //     border: \"none\",\n  //     padding: \"10px 12px\",\n  //     cursor: \"pointer\",\n  //     fontSize: \"16px\",\n  //     borderRadius: \"12px\",\n  //     width: \"100%\",\n  //     marginTop: \"10px\",\n  //     lineHeight: \"20px\",\n  //     textTransform:\"none\",\n\n  //   } as React.CSSProperties,\n  // };\n\n  const validateForm = () => {\n    if (!email.trim() && !password.trim()) {\n      setError(\"Email and Password are required.\");\n      return false;\n    }\n    if (!email.trim()) {\n      setError(\"Email is required.\");\n      return false;\n    }\n    if (!validateEmail(email)) {\n      setError(\"Enter a valid email address.\");\n      return false;\n    }\n    if (!password.trim()) {\n      setError(\"Password is required.\");\n      return false;\n    }\n    return true;\n  };\n  const handleLoginSuccess = async () => {\n    try {\n      if (!validateForm()) return;\n      setIsLoading(true);\n      setError(null);\n      clearAll();\n      clearGuideDetails();\n      clearUserSession();\n      setActiveMenu(null);\n      setSearchText(\"\");\n      const organizationId = \"1\";\n      const rememberLogin = true;\n      const returnUrl = \"\";\n      const authType = \"admin\";\n      const tenantId = \"web\";\n      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\n      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\n      const encryptor = new JSEncrypt();\n      encryptor.setPublicKey(publicKey);\n      const now = new Date().toISOString();\n      const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\n      if (!encryptedPassword) {\n        console.error(\"Encryption failed\");\n        return;\n      }\n      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\n      if (response.access_token) {\n        setAccessToken(response.access_token);\n        setOidcInfo(response);\n        const userResponse = await GetUserDetails();\n        if (userResponse) {\n          var _userResponse$FirstNa, _userResponse$LastNam, _userResponse$UserTyp, _userResponse$Organiz;\n          setUser(userResponse);\n          const firstNameInitials = (userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$FirstNa = userResponse.FirstName) === null || _userResponse$FirstNa === void 0 ? void 0 : _userResponse$FirstNa.charAt(0).toUpperCase()) || '';\n          const lastNameInitials = (userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$LastNam = userResponse.LastName) === null || _userResponse$LastNam === void 0 ? void 0 : _userResponse$LastNam.charAt(0).toUpperCase()) || '';\n          SAinitialsData = firstNameInitials + lastNameInitials;\n          setUserType((_userResponse$UserTyp = userResponse === null || userResponse === void 0 ? void 0 : userResponse.UserType) !== null && _userResponse$UserTyp !== void 0 ? _userResponse$UserTyp : \"\");\n          const orgDetails = await getOrganizationById((_userResponse$Organiz = userResponse === null || userResponse === void 0 ? void 0 : userResponse.OrganizationId) !== null && _userResponse$Organiz !== void 0 ? _userResponse$Organiz : \"\");\n          setOrgDetails(orgDetails);\n          setIsLoggedIn(true);\n          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);\n        }\n      } else {\n        setIsLoggedIn(false);\n        setError(response.error_description || \"Login failed. Please check your credentials.\");\n      }\n    } catch (err) {\n      console.error(err);\n      setError(\"An unexpected error occurred. Please try again later.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleClickShowPassword = () => setShowPassword(!showPassword);\n  const handlePasswordChange = event => {\n    setPassword(event.target.value);\n    setError(null);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadptDrawerContent\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadptWelcomeMessage\",\n        children: \"Welcome back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadptLoginForm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadptFormLabel\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          autoFocus: true,\n          value: email,\n          onChange: e => {\n            setEmail(e.target.value);\n            setError(null);\n          },\n          className: \"qadpt-txtfld\",\n          placeholder: \"Enter your email\"\n          // sx={{\n          //   \"& .MuiInputBase-root\":{\n          //     fontSize: \"16px !important\",\n          //     fontWeight: \"400 !important\",\n          //     padding: \"12px !important\",\n          //     border: \"1px solid var(--border-color) !important\",\n          //     borderRadius: \"6px !important\",\n          //     boxShadow: \"none !important\",\n          //     height: \"42px !important\",\n          //     backgroundColor: \"var(--white-color) !important\",\n          //     marginTop: \"10px !important\",\n          //   },\n          //   \"& .MuiInputBase-input\": {\n          //     height: \"34px !important\",\n          //     border: \"none !important\",\n          //     padding:\"0 !important\",\n\n          //   }\n          // }}\n          ,\n          InputProps: {\n            // className: \"qadpt-input-field\",\n            disableUnderline: true\n          },\n          variant: \"standard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadptFormLabel\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          required: true,\n          fullWidth: true,\n          type: showPassword ? \"text\" : \"password\",\n          id: \"password\",\n          name: \"password\",\n          autoComplete: \"password\",\n          value: password,\n          onChange: handlePasswordChange,\n          className: \"qadpt-txtfld\",\n          placeholder: \"Enter your password\"\n          // sx={{\n          //   \"& .MuiInputBase-root\":{\n          //     fontSize: \"16px !important\",\n          //     fontWeight: \"400 !important\",\n          //     padding: \"12px !important\",\n          //     border: \"1px solid var(--border-color) !important\",\n          //     borderRadius: \"6px !important\",\n          //     boxShadow: \"none !important\",\n          //     height: \"46px !important\",\n          //     backgroundColor: \"var(--white-color) !important\",\n          //     marginTop: \"10px !important\",\n          //   },\n          //   \"& .MuiInputBase-input\": {\n          //     height: \"34px !important\",\n          //     border: \"none !important\",\n          //     padding:\"0 !important\",\n          //   }\n          // }}\n          ,\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              className: \"pwdicon-blk\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                \"aria-label\": \"toggle password visibility\",\n                onClick: handleClickShowPassword,\n                edge: \"end\"\n                // sx={{\n                //   backgroundColor: \"transparent !important\",\n                //   border: \"none !important\",\n                //   padding: \"0 !important\",\n                //   margin: \"0 !important\",\n                // }}\n                ,\n                className: \"qadpt-pwdicon\"\n                //style={loginStyles.qadpteyeicon} \n                ,\n                children: showPassword ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: pwdeye\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 18\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: eyeclose\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this),\n            disableUnderline: true\n          },\n          variant: \"standard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 9\n        }, this), error && /*#__PURE__*/_jsxDEV(FormHelperText, {\n          error: true,\n          className: \"qadptFormLabel\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadptForgotPwd\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            onClick: () => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank'),\n            children: \"Forgot password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleLoginSuccess,\n          disabled: isLoading\n          // style={loginStyles.qadptBtn}\n          ,\n          className: \"qadptBtn\",\n          children: isLoading ? 'Signing in...' : 'Log in'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ExtensionLogin, \"FAcXnmeA11N1qA6/mNhg5TOypmk=\");\n_c = ExtensionLogin;\nexport default ExtensionLogin;\nfunction validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\nvar _c;\n$RefreshReg$(_c, \"ExtensionLogin\");", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "IconButton", "InputAdornment", "FormHelperText", "GetUserDetails", "JSEncrypt", "LoginService", "getOrganizationById", "useDrawerStore", "useInfoStore", "userSession", "pwdeye", "eyeclose", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "setAccessToken", "setOidcInfo", "setUser", "setOrgDetails", "setUserType", "getState", "setHasAnnouncementOpened", "hasAnnouncementOpened", "clearAll", "clearAccessToken", "clearGuideDetails", "setActiveMenu", "setSearchText", "clearUserSession", "userLocalData", "SAinitialsData", "userDetails", "ExtensionLogin", "setIsLoggedIn", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loginUserDetails", "setUserDetails", "isLoading", "setIsLoading", "validateForm", "trim", "validateEmail", "handleLoginSuccess", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "returnUrl", "authType", "tenantId", "isEncryptionEnabled", "process", "env", "REACT_APP_ENABLE_ENCRYPTION", "public<PERSON>ey", "REACT_APP_PUBLIC_ENCRYPT_KEY", "encryptor", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "toString", "console", "response", "access_token", "userResponse", "_userResponse$FirstNa", "_userResponse$LastNam", "_userResponse$UserTyp", "_userResponse$Organiz", "firstNameInitials", "FirstName", "char<PERSON>t", "toUpperCase", "lastNameInitials", "LastName", "UserType", "orgDetails", "OrganizationId", "error_description", "err", "handleClickShowPassword", "handlePasswordChange", "event", "target", "value", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "autoFocus", "onChange", "e", "placeholder", "InputProps", "disableUnderline", "variant", "required", "type", "id", "name", "autoComplete", "endAdornment", "position", "onClick", "edge", "dangerouslySetInnerHTML", "__html", "window", "open", "REACT_APP_WEB_API", "disabled", "_c", "emailRegex", "test", "$RefreshReg$"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/login/ExtensionLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { TextField, Button, Typography, IconButton, InputAdornment, FormHelperText } from '@mui/material';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { GetUserDetails, GetUserDetailsById, UserLogin } from '../../services/UserService';\r\nimport { useAuth } from '../auth/AuthProvider';\r\nimport JSEncrypt from 'jsencrypt';\r\nimport { LoginService } from '../../services/LoginService';\r\nimport { User } from '../../models/User';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { Padding } from '@mui/icons-material';\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport useInfoStore from \"../../store/UserInfoStore\";\r\nimport userSession from \"../../store/userSession\";\r\nimport { pwdeye, eyeclose } from '../../assets/icons/icons';\r\nimport ExtensionLoader from '../common/ExtensionLoader';\r\nimport '../login/ExtensionLogin.css';\r\nconst { setAccessToken, setOidcInfo, setUser, setOrgDetails, setUserType } = useInfoStore.getState();\r\nconst { setHasAnnouncementOpened, hasAnnouncementOpened } = userSession.getState();\r\nconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\nconst { clearGuideDetails, setActiveMenu, setSearchText } = useDrawerStore.getState();\r\nconst {\tclearUserSession} = userSession.getState();\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet SAinitialsData: string;\r\nlet userDetails: User;\r\ninterface ExtensionLoginProps {\r\n    setIsLoggedIn: (value: boolean) => void;\r\n}\r\nconst ExtensionLogin: React.FC<ExtensionLoginProps> = ({ setIsLoggedIn }) => {\r\n  const [email, setEmail] = useState<string>('');\r\n  const [password, setPassword] = useState<string>('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // const loginStyles = {\r\n  //   qadptDrawerContent: {\r\n  //     width: '100%',\r\n  //     backgroundColor: 'var(--ext - background)',\r\n  //     marginTop: '20px',\r\n  //   },\r\n  //   qadptWelcomeMessage: {\r\n  //     fontWeight: \"600\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //     padding: \"14px\",\r\n  //   },\r\n  //       // container: {\r\n  //   //   padding: '10px',\r\n  //   //   backgroundColor: '#F6EEEE',\r\n  //   //   borderRadius: '8px',\r\n  //   // },\r\n  //   // welcomeText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 600,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(34, 34, 34, 1)',\r\n  //   // },\r\n  //   // headerText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '14px',\r\n  //   //   fontWeight: 400,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   marginTop: '10px',\r\n  //   // },\r\n  //   // textField: {\r\n  //   //   width: '100%',\r\n  //   //   backgroundColor: 'rgba(255, 255, 255, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // textFieldInput: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 400,\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   padding: '12px',\r\n  //   //   border: '1px solid rgba(213, 213, 213, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // loginButton: {\r\n  //   //   backgroundColor: '#5F9EA0',\r\n  //   //   color: '#fff',\r\n  //   //   borderRadius: '25px',\r\n  //   //   width: '100%',\r\n  //   //   marginTop: '20px',\r\n  //   //   textTransform: 'none',\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 500,\r\n  //   // },\r\n  //   // qadptTextdanger: {\r\n  //   //   color: '#d9534f',\r\n  //   //   fontSize: '0.9rem',\r\n  //   // },\r\n\r\n  //   qadptLoginForm: {\r\n  //     marginTop: '20px',\r\n  //     padding: '0px 10px 20px 10px', \r\n  //   },\r\n  //   qadptFormLabel: {\r\n  //     fontSize: \"14px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptInvalidCreds: {\r\n  //     fontSize: \"14px\",\r\n  //   },\r\n  //       // qadpteyeicon: {\r\n  //   //   \"& .MuiIconButton-root\": {\r\n  //   //     backgroundColor: \"transparent !important\",\r\n  //   //     border: \"none !important\",\r\n  //   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\r\n  //   //   }\r\n  //   // },\r\n    \r\n  //   qadptForgotPwd: {\r\n  //     color: \"var(--primarycolor)\", \r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     fontWeight: \"400\",\r\n  //     lineHeight: \"24px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptBtn: {\r\n  //     backgroundColor: \"var(--primarycolor) !important\",  \r\n  //     color: \"#fff\",\r\n  //     border: \"none\",\r\n  //     padding: \"10px 12px\",\r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     borderRadius: \"12px\",\r\n  //     width: \"100%\",\r\n  //     marginTop: \"10px\",\r\n  //     lineHeight: \"20px\",\r\n  //     textTransform:\"none\",\r\n      \r\n  //   } as React.CSSProperties,\r\n  // };\r\n\r\n  const validateForm = (): boolean => {\r\n    if (!email.trim() && !password.trim()) {\r\n      setError(\"Email and Password are required.\");\r\n      return false;\r\n    }\r\n    if (!email.trim()) {\r\n      setError(\"Email is required.\");\r\n      return false;\r\n    }\r\n    if (!validateEmail(email)) {\r\n      setError(\"Enter a valid email address.\");\r\n      return false;\r\n    }\r\n    if (!password.trim()) {\r\n      setError(\"Password is required.\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n  \r\n  \r\n\r\n  const handleLoginSuccess = async () => {\r\n    try {\r\n      if (!validateForm()) return;\r\n\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      clearAll();\r\n      clearGuideDetails();\r\n      clearUserSession();\r\n      setActiveMenu(null);\r\n      setSearchText(\"\");\r\n  \r\n      const organizationId = \"1\";\r\n      const rememberLogin = true;\r\n      const returnUrl = \"\";\r\n      const authType = \"admin\";\r\n      const tenantId = \"web\";\r\n\r\n      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n\r\n      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n                if (!encryptedPassword) {\r\n                  console.error(\"Encryption failed\");\r\n                  return; \r\n                }\r\n  \r\n      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\r\n      if (response.access_token) {\r\n        setAccessToken(response.access_token);\r\n        setOidcInfo(response);\r\n        const userResponse = await GetUserDetails();\r\n        if (userResponse) {\r\n          setUser(userResponse);\r\n          const firstNameInitials = userResponse?.FirstName?.charAt(0).toUpperCase() || '';\r\n          const lastNameInitials = userResponse?.LastName?.charAt(0).toUpperCase() || '';\r\n          SAinitialsData = firstNameInitials + lastNameInitials;\r\n          setUserType(userResponse?.UserType ?? \"\");\r\n          const orgDetails = await getOrganizationById(userResponse?.OrganizationId ?? \"\");\r\n          setOrgDetails(orgDetails);\r\n          setIsLoggedIn(true);\r\n          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);\r\n        }\r\n      } else {\r\n        setIsLoggedIn(false);\r\n        setError(response.error_description || \"Login failed. Please check your credentials.\");\r\n      }\r\n    } catch (err) {\r\n      console.error(err);\r\n      setError(\"An unexpected error occurred. Please try again later.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  \r\n\r\n  const handleClickShowPassword = () => setShowPassword(!showPassword);\r\n\r\n  const handlePasswordChange = (event: any) => {\r\n    setPassword(event.target.value);\r\n    setError(null);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* {isLoading && <ExtensionLoader message=\"Signing you in...\" />} */}\r\n      <div className='qadptDrawerContent'>\r\n  <div className='qadptWelcomeMessage'>\r\n    Welcome back\r\n  </div>\r\n      <div className='qadptLoginForm'>\r\n\r\n        <div className='qadptFormLabel'>Email</div>\r\n\r\n        <TextField\r\n            fullWidth\r\n            autoFocus            \r\n            value={email}\r\n            onChange={(e) => {\r\n                setEmail(e.target.value); \r\n                setError(null); \r\n          }}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your email\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"42px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n              \r\n          //   }\r\n          // }}\r\n            InputProps={{\t\t\t\t\t\t\t\t\t\t\r\n              // className: \"qadpt-input-field\",\r\n              disableUnderline: true,\r\n          }}\r\n          variant=\"standard\"\r\n        />\r\n\r\n        <div className='qadptFormLabel'>Password</div>\r\n        <TextField\r\n            required\r\n            fullWidth\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            id=\"password\"\r\n            name=\"password\"\r\n            autoComplete=\"password\"           \r\n            value={password}\r\n          onChange={handlePasswordChange}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your password\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"46px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n          //   }\r\n          // }}\r\n            InputProps={{ \r\n              endAdornment: (\r\n                <InputAdornment \r\n                position=\"end\" \r\n                className='pwdicon-blk'\r\n              >\r\n                <IconButton\r\n                  aria-label=\"toggle password visibility\"\r\n                  onClick={handleClickShowPassword}\r\n                    edge=\"end\"\r\n                    // sx={{\r\n                    //   backgroundColor: \"transparent !important\",\r\n                    //   border: \"none !important\",\r\n                    //   padding: \"0 !important\",\r\n                    //   margin: \"0 !important\",\r\n                    // }}\r\n                    className='qadpt-pwdicon'\r\n                    //style={loginStyles.qadpteyeicon} \r\n                >\r\n{showPassword ?  <span dangerouslySetInnerHTML={{ __html: pwdeye }}/>: <span dangerouslySetInnerHTML={{ __html: eyeclose }}/>}\r\n</IconButton>\r\n              </InputAdornment>\r\n                    \r\n                ),\r\n               \r\n                disableUnderline: true,\r\n            }}\r\n            variant=\"standard\"\r\n           \r\n        />\t\r\n         {error && (\r\n          <FormHelperText error className='qadptFormLabel'>\r\n              {error}\r\n          </FormHelperText>\r\n        )}\r\n\r\n        <div className='qadptForgotPwd'>\r\n        <span \r\n        onClick={() => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank')}\r\n        >\r\n          Forgot password?\r\n        </span>\r\n        </div>\r\n\r\n        <Button\r\n        variant=\"contained\"\r\n        onClick={handleLoginSuccess}\r\n        disabled={isLoading}\r\n          // style={loginStyles.qadptBtn}\r\n          className='qadptBtn'\r\n    >\r\n        {isLoading ? 'Signing in...' : 'Log in'}\r\n    </Button>\r\n    </div>\r\n</div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ExtensionLogin;\r\nfunction validateEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,MAAM,EAAcC,UAAU,EAAEC,cAAc,EAAEC,cAAc,QAAQ,eAAe;AAGzG,SAASC,cAAc,QAAuC,4BAA4B;AAE1F,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,YAAY,QAAQ,6BAA6B;AAE1D,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AAE3D,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACrC,MAAM;EAAEC,cAAc;EAAEC,WAAW;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAY,CAAC,GAAGZ,YAAY,CAACa,QAAQ,CAAC,CAAC;AACpG,MAAM;EAAEC,wBAAwB;EAAEC;AAAsB,CAAC,GAAGd,WAAW,CAACY,QAAQ,CAAC,CAAC;AAClF,MAAM;EAAEG,QAAQ;EAAEC;AAAiB,CAAC,GAAGjB,YAAY,CAACa,QAAQ,CAAC,CAAC;AAC9D,MAAM;EAAEK,iBAAiB;EAAEC,aAAa;EAAEC;AAAc,CAAC,GAAGrB,cAAc,CAACc,QAAQ,CAAC,CAAC;AACrF,MAAM;EAAEQ;AAAgB,CAAC,GAAGpB,WAAW,CAACY,QAAQ,CAAC,CAAC;AAClD,IAAIS,aAAqC,GAAG,CAAC,CAAC;AAC9C,IAAIC,cAAsB;AAC1B,IAAIC,WAAiB;AAIrB,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+C,gBAAgB,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAc,IAAI,CAAC;EACtE,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA,MAAMmD,YAAY,GAAGA,CAAA,KAAe;IAClC,IAAI,CAACZ,KAAK,CAACa,IAAI,CAAC,CAAC,IAAI,CAACX,QAAQ,CAACW,IAAI,CAAC,CAAC,EAAE;MACrCN,QAAQ,CAAC,kCAAkC,CAAC;MAC5C,OAAO,KAAK;IACd;IACA,IAAI,CAACP,KAAK,CAACa,IAAI,CAAC,CAAC,EAAE;MACjBN,QAAQ,CAAC,oBAAoB,CAAC;MAC9B,OAAO,KAAK;IACd;IACA,IAAI,CAACO,aAAa,CAACd,KAAK,CAAC,EAAE;MACzBO,QAAQ,CAAC,8BAA8B,CAAC;MACxC,OAAO,KAAK;IACd;IACA,IAAI,CAACL,QAAQ,CAACW,IAAI,CAAC,CAAC,EAAE;MACpBN,QAAQ,CAAC,uBAAuB,CAAC;MACjC,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAID,MAAMQ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;MAErBD,YAAY,CAAC,IAAI,CAAC;MAClBJ,QAAQ,CAAC,IAAI,CAAC;MAEdnB,QAAQ,CAAC,CAAC;MACVE,iBAAiB,CAAC,CAAC;MACnBG,gBAAgB,CAAC,CAAC;MAClBF,aAAa,CAAC,IAAI,CAAC;MACnBC,aAAa,CAAC,EAAE,CAAC;MAEjB,MAAMwB,cAAc,GAAG,GAAG;MAC1B,MAAMC,aAAa,GAAG,IAAI;MAC1B,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMC,QAAQ,GAAG,OAAO;MACxB,MAAMC,QAAQ,GAAG,KAAK;MAEtB,MAAMC,mBAAmB,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,KAAK,MAAM;MAE9E,MAAMC,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACG,4BAA4B,IAAI,EAAE;MACtD,MAAMC,SAAS,GAAG,IAAI3D,SAAS,CAAC,CAAC;MACjC2D,SAAS,CAACC,YAAY,CAACH,SAAS,CAAC;MACjC,MAAMI,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpC,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,OAAO,CAAC/B,QAAQ,GAAG,GAAG,GAAG2B,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC;MACnF,IAAI,CAACF,iBAAiB,EAAE;QACtBG,OAAO,CAAC7B,KAAK,CAAC,mBAAmB,CAAC;QAClC;MACF;MAEV,MAAM8B,QAAQ,GAAG,MAAMnE,YAAY,CAAC+B,KAAK,EAAEqB,mBAAmB,GAAGW,iBAAiB,GAAG9B,QAAQ,EAAEc,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;MAC5J,IAAIgB,QAAQ,CAACC,YAAY,EAAE;QACzBzD,cAAc,CAACwD,QAAQ,CAACC,YAAY,CAAC;QACrCxD,WAAW,CAACuD,QAAQ,CAAC;QACrB,MAAME,YAAY,GAAG,MAAMvE,cAAc,CAAC,CAAC;QAC3C,IAAIuE,YAAY,EAAE;UAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAChB5D,OAAO,CAACwD,YAAY,CAAC;UACrB,MAAMK,iBAAiB,GAAG,CAAAL,YAAY,aAAZA,YAAY,wBAAAC,qBAAA,GAAZD,YAAY,CAAEM,SAAS,cAAAL,qBAAA,uBAAvBA,qBAAA,CAAyBM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;UAChF,MAAMC,gBAAgB,GAAG,CAAAT,YAAY,aAAZA,YAAY,wBAAAE,qBAAA,GAAZF,YAAY,CAAEU,QAAQ,cAAAR,qBAAA,uBAAtBA,qBAAA,CAAwBK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;UAC9EnD,cAAc,GAAGgD,iBAAiB,GAAGI,gBAAgB;UACrD/D,WAAW,EAAAyD,qBAAA,GAACH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,QAAQ,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACzC,MAAMS,UAAU,GAAG,MAAMhF,mBAAmB,EAAAwE,qBAAA,GAACJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,cAAc,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UAChF3D,aAAa,CAACmE,UAAU,CAAC;UACzBpD,aAAa,CAAC,IAAI,CAAC;UACnB,IAAI,CAACX,qBAAqB,EAAED,wBAAwB,CAAC,IAAI,CAAC;QAC5D;MACF,CAAC,MAAM;QACLY,aAAa,CAAC,KAAK,CAAC;QACpBS,QAAQ,CAAC6B,QAAQ,CAACgB,iBAAiB,IAAI,8CAA8C,CAAC;MACxF;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZlB,OAAO,CAAC7B,KAAK,CAAC+C,GAAG,CAAC;MAClB9C,QAAQ,CAAC,uDAAuD,CAAC;IACnE,CAAC,SAAS;MACRI,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAGD,MAAM2C,uBAAuB,GAAGA,CAAA,KAAMjD,eAAe,CAAC,CAACD,YAAY,CAAC;EAEpE,MAAMmD,oBAAoB,GAAIC,KAAU,IAAK;IAC3CrD,WAAW,CAACqD,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAC/BnD,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,oBACE9B,OAAA,CAAAE,SAAA;IAAAgF,QAAA,eAEElF,OAAA;MAAKmF,SAAS,EAAC,oBAAoB;MAAAD,QAAA,gBACvClF,OAAA;QAAKmF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,EAAC;MAErC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACFvF,OAAA;QAAKmF,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAE7BlF,OAAA;UAAKmF,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAE3CvF,OAAA,CAACf,SAAS;UACNuG,SAAS;UACTC,SAAS;UACTR,KAAK,EAAE1D,KAAM;UACbmE,QAAQ,EAAGC,CAAC,IAAK;YACbnE,QAAQ,CAACmE,CAAC,CAACX,MAAM,CAACC,KAAK,CAAC;YACxBnD,QAAQ,CAAC,IAAI,CAAC;UACpB,CAAE;UACFqD,SAAS,EAAC,cAAc;UACxBS,WAAW,EAAC;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UAAA;UACEC,UAAU,EAAE;YACV;YACAC,gBAAgB,EAAE;UACtB,CAAE;UACFC,OAAO,EAAC;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEFvF,OAAA;UAAKmF,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9CvF,OAAA,CAACf,SAAS;UACN+G,QAAQ;UACRR,SAAS;UACTS,IAAI,EAAEtE,YAAY,GAAG,MAAM,GAAG,UAAW;UACzCuE,EAAE,EAAC,UAAU;UACbC,IAAI,EAAC,UAAU;UACfC,YAAY,EAAC,UAAU;UACvBnB,KAAK,EAAExD,QAAS;UAClBiE,QAAQ,EAAEZ,oBAAqB;UAC/BK,SAAS,EAAC,cAAc;UACxBS,WAAW,EAAC;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA;UACEC,UAAU,EAAE;YACVQ,YAAY,eACVrG,OAAA,CAACZ,cAAc;cACfkH,QAAQ,EAAC,KAAK;cACdnB,SAAS,EAAC,aAAa;cAAAD,QAAA,eAEvBlF,OAAA,CAACb,UAAU;gBACT,cAAW,4BAA4B;gBACvCoH,OAAO,EAAE1B,uBAAwB;gBAC/B2B,IAAI,EAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBACArB,SAAS,EAAC;gBACV;gBAAA;gBAAAD,QAAA,EAEnBvD,YAAY,gBAAI3B,OAAA;kBAAMyG,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7G;kBAAO;gBAAE;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,gBAAEvF,OAAA;kBAAMyG,uBAAuB,EAAE;oBAAEC,MAAM,EAAE5G;kBAAS;gBAAE;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACiB,CAEb;YAEDO,gBAAgB,EAAE;UACtB,CAAE;UACFC,OAAO,EAAC;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErB,CAAC,EACA1D,KAAK,iBACL7B,OAAA,CAACX,cAAc;UAACwC,KAAK;UAACsD,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAC3CrD;QAAK;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,eAEDvF,OAAA;UAAKmF,SAAS,EAAC,gBAAgB;UAAAD,QAAA,eAC/BlF,OAAA;YACAuG,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,IAAI,CAAC,GAAG/D,OAAO,CAACC,GAAG,CAAC+D,iBAAiB,iBAAiB,EAAE,QAAQ,CAAE;YAAA3B,QAAA,EACvF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENvF,OAAA,CAACd,MAAM;UACP6G,OAAO,EAAC,WAAW;UACnBQ,OAAO,EAAEjE,kBAAmB;UAC5BwE,QAAQ,EAAE7E;UACR;UAAA;UACAkD,SAAS,EAAC,UAAU;UAAAD,QAAA,EAErBjD,SAAS,GAAG,eAAe,GAAG;QAAQ;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBACA,CAAC;AAEP,CAAC;AAACjE,EAAA,CArVIF,cAA6C;AAAA2F,EAAA,GAA7C3F,cAA6C;AAuVnD,eAAeA,cAAc;AAC7B,SAASiB,aAAaA,CAACd,KAAa,EAAW;EAC7C,MAAMyF,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACC,IAAI,CAAC1F,KAAK,CAAC;AAC/B;AAAC,IAAAwF,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}