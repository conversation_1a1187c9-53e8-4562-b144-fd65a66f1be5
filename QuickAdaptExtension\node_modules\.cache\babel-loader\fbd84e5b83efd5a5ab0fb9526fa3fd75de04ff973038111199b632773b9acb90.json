{"ast": null, "code": "import React,{useState}from\"react\";import{<PERSON>,Typo<PERSON>,Popover,IconButton,TextField,MenuItem,Button,Tooltip,Snackbar,Alert}from\"@mui/material\";import RemoveIcon from\"@mui/icons-material/Remove\";import AddIcon from\"@mui/icons-material/Add\";import{useTranslation}from'react-i18next';import{uploadfile,hyperlink,files,uploadicon,replaceimageicon,copyicon,deleteicon,sectionheight,Settings,CrossIcon}from\"../../../assets/icons/icons\";import useDrawerStore,{IMG_CONTAINER_DEFAULT_HEIGHT,IMG_CONTAINER_MAX_HEIGHT,IMG_CONTAINER_MIN_HEIGHT,IMG_OBJECT_FIT,IMG_STEP_VALUE}from\"../../../store/drawerStore\";import{ChromePicker}from\"react-color\";import\"./PopupSections.css\";import SelectImageFromApplication from\"../../common/SelectImageFromApplication\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ImageSection=_ref=>{var _imagesContainer$find;let{setImageSrc,setImageName,onDelete,onClone,isCloneDisabled}=_ref;const{t:translate}=useTranslation();const{uploadImage,imagesContainer,imageAnchorEl,setImageAnchorEl,replaceImage,cloneImageContainer,deleteImageContainer,updateImageContainer,toggleFit,setImageSrc:storeImageSrc}=useDrawerStore(state=>state);const[snackbarOpen,setSnackbarOpen]=useState(false);const[snackbarMessage,setSnackbarMessage]=useState('');const[snackbarSeverity,setSnackbarSeverity]=useState('info');const[snackbarKey,setSnackbarKey]=useState(0);const openSnackbar=()=>{setSnackbarKey(prev=>prev+1);setSnackbarOpen(true);};const closeSnackbar=()=>{setSnackbarOpen(false);};const[showHyperlinkInput,setShowHyperlinkInput]=useState({currentContainerId:\"\",isOpen:false});const[imageLink,setImageLink]=useState(\"\");const[colorPickerAnchorEl,setColorPickerAnchorEl]=useState(null);const[currentImageSectionInfo,setCurrentImageSectionInfo]=useState({currentContainerId:\"\",isImage:false,height:IMG_CONTAINER_DEFAULT_HEIGHT});const[selectedAction,setSelectedAction]=useState(\"none\");const[isModelOpen,setModelOpen]=useState(false);const[formOfUpload,setFormOfUpload]=useState(\"\");const[settingsAnchorEl,setSettingsAnchorEl]=useState(null);const[selectedColor,setSelectedColor]=useState(\"#313030\");const[isReplaceImage,setReplaceImage]=useState(false);const openSettingsPopover=Boolean(settingsAnchorEl);const handleActionChange=event=>{setSelectedAction(event.target.value);};const handleSettingsClick=event=>{setSettingsAnchorEl(event.currentTarget);};const handleCloseSettingsPopover=()=>{setSettingsAnchorEl(null);};const imageContainerStyle={width:\"100%\",height:\"100%\",display:\"flex\",justifyContent:\"center\",alignItems:\"center\",padding:0,margin:0,overflow:\"hidden\"};const imageStyle={width:\"100%\",height:\"100%\",margin:0,padding:0,borderRadius:\"0\"};const iconRowStyle={display:\"flex\",justifyContent:\"center\",gap:\"16px\",marginTop:\"10px\"};const iconTextStyle={display:\"flex\",flexDirection:\"column\",alignItems:\"center\",justifyContent:\"center\",width:\"100%\"};const handleImageUpload=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){var _event$target$files2;const parts=file.name.split('.');const extension=parts.pop();// Check for double extensions (e.g. file.html.png) or missing/invalid extension\nif(parts.length>1||!extension){setSnackbarMessage(\"Uploaded file name should not contain any special character\");setSnackbarSeverity(\"error\");setSnackbarOpen(true);event.target.value='';return;}if(file.name.length>128){setSnackbarMessage(\"File name should not exceed 128 characters\");setSnackbarSeverity(\"error\");setSnackbarOpen(true);event.target.value='';return;}setImageName((_event$target$files2=event.target.files)===null||_event$target$files2===void 0?void 0:_event$target$files2[0].name);const reader=new FileReader();reader.onloadend=()=>{const base64Image=reader.result;storeImageSrc(base64Image);setImageSrc(base64Image);uploadImage(imageAnchorEl.containerId,{altText:file.name,id:crypto.randomUUID(),url:base64Image,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});};reader.readAsDataURL(file);}setModelOpen(false);};const handleImageUploadFormApp=file=>{if(file){storeImageSrc(file.Url);setImageSrc(file.Url);if(isReplaceImage){replaceImage(imageAnchorEl.containerId,imageAnchorEl.buttonId,{altText:file.FileName,id:imageAnchorEl.buttonId,url:file.Url,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});setReplaceImage(false);}else{uploadImage(imageAnchorEl.containerId,{altText:file.FileName,id:crypto.randomUUID(),// Use existing ID\nurl:file.Url,// Directly use the URL\nbackgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});}}setModelOpen(false);};const handleReplaceImage=event=>{var _event$target$files3;const file=(_event$target$files3=event.target.files)===null||_event$target$files3===void 0?void 0:_event$target$files3[0];if(file){const reader=new FileReader();reader.onloadend=()=>{replaceImage(imageAnchorEl.containerId,imageAnchorEl.buttonId,{altText:file.name,id:imageAnchorEl.buttonId,url:reader.result,backgroundColor:\"#ffffff\",objectFit:IMG_OBJECT_FIT});};reader.readAsDataURL(file);}};const handleClick=(event,containerId,imageId,isImage,currentHeight)=>{// @ts-ignore\nif([\"file-upload\",\"hyperlink\"].includes(event.target.id))return;setImageAnchorEl({buttonId:imageId,containerId:containerId,// @ts-ignore\nvalue:event.currentTarget});setSettingsAnchorEl(null);setCurrentImageSectionInfo({currentContainerId:containerId,isImage,height:currentHeight});setShowHyperlinkInput({currentContainerId:\"\",isOpen:false});};const handleClose=()=>{setImageAnchorEl({buttonId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});};const open=Boolean(imageAnchorEl.value);const colorPickerOpen=Boolean(colorPickerAnchorEl);const id=open?\"image-popover\":undefined;const handleIncreaseHeight=prevHeight=>{if(prevHeight>=IMG_CONTAINER_MAX_HEIGHT)return;const newHeight=Math.min(prevHeight+IMG_STEP_VALUE,IMG_CONTAINER_MAX_HEIGHT);updateImageContainer(imageAnchorEl.containerId,\"style\",{height:newHeight});setCurrentImageSectionInfo(prev=>({...prev,height:newHeight}));};const handleDecreaseHeight=prevHeight=>{if(prevHeight<=IMG_CONTAINER_MIN_HEIGHT)return;const newHeight=Math.max(prevHeight-IMG_STEP_VALUE,IMG_CONTAINER_MIN_HEIGHT);updateImageContainer(imageAnchorEl.containerId,\"style\",{height:newHeight});setCurrentImageSectionInfo(prev=>({...prev,height:newHeight}));};const triggerImageUpload=()=>{var _document$getElementB;(_document$getElementB=document.getElementById(\"replace-upload\"))===null||_document$getElementB===void 0?void 0:_document$getElementB.click();// setModelOpen(true);\n// setReplaceImage(true);\n};const currentContainerColor=((_imagesContainer$find=imagesContainer.find(item=>item.id===imageAnchorEl.containerId))===null||_imagesContainer$find===void 0?void 0:_imagesContainer$find.style.backgroundColor)||\"transparent\";// Function to delete the section\nconst handleDeleteSection=()=>{setImageAnchorEl({buttonId:\"\",containerId:\"\",// @ts-ignore\nvalue:null});// Call the delete function from the store\ndeleteImageContainer(imageAnchorEl.containerId);// Call the onDelete callback if provided\nif(onDelete){onDelete();}};const handleLinkSubmit=event=>{if(event.key===\"Enter\"&&imageLink){uploadImage(imageAnchorEl.containerId,{altText:\"New Image\",id:crypto.randomUUID(),url:imageLink,backgroundColor:\"transparent\",objectFit:IMG_OBJECT_FIT});setShowHyperlinkInput({currentContainerId:\"\",isOpen:false});}};const handleCloneImgContainer=()=>{// Check if cloning is disabled due to section limits\nif(isCloneDisabled){return;// Don't clone if limit is reached\n}// Call the clone function from the store\ncloneImageContainer(imageAnchorEl.containerId);// Call the onClone callback if provided\nif(onClone){onClone();}};const handleCloseColorPicker=()=>{setColorPickerAnchorEl(null);};const handleColorChange=color=>{setSelectedColor(color.hex);updateImageContainer(imageAnchorEl.containerId,\"style\",{backgroundColor:color.hex});};const handleBackgroundColorClick=event=>{setColorPickerAnchorEl(event.currentTarget);};return/*#__PURE__*/_jsxs(_Fragment,{children:[imagesContainer.map(item=>{var _item$images$,_item$images$2,_item$images$3,_item$style;const imageSrc=(_item$images$=item.images[0])===null||_item$images$===void 0?void 0:_item$images$.url;const imageId=(_item$images$2=item.images[0])===null||_item$images$2===void 0?void 0:_item$images$2.id;const objectFit=((_item$images$3=item.images[0])===null||_item$images$3===void 0?void 0:_item$images$3.objectFit)||IMG_OBJECT_FIT;const currentSecHeight=(item===null||item===void 0?void 0:(_item$style=item.style)===null||_item$style===void 0?void 0:_item$style.height)||IMG_CONTAINER_DEFAULT_HEIGHT;const id=item.id;return/*#__PURE__*/_jsx(Box,{sx:{width:\"100%\",height:\"100%\",display:\"flex\",flexDirection:\"column\",justifyContent:\"flex-start\",alignItems:\"center\",// padding: \"5px\",\nmargin:\"0px\",overflow:\"auto\"},children:/*#__PURE__*/_jsx(Box,{sx:{...imageContainerStyle,backgroundColor:item.style.backgroundColor,height:`${item.style.height}px`},onClick:e=>handleClick(e,id,imageId,imageSrc?true:false,currentSecHeight),component:\"div\",id:id,onMouseOver:()=>{setImageAnchorEl({buttonId:imageId,containerId:id,value:null});},children:imageSrc?/*#__PURE__*/_jsx(\"img\",{src:imageSrc,alt:\"Uploaded\",style:{...imageStyle,objectFit}}):/*#__PURE__*/_jsxs(Box,{sx:{textAlign:\"center\",width:\"100%\",height:\"100%\",display:\"flex\",flexDirection:\"column\",justifyContent:\"center\"},children:[/*#__PURE__*/_jsxs(Box,{sx:iconTextStyle,component:\"div\",children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:uploadfile},style:{display:\"inline-block\"}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",sx:{fontSize:\"14px\",fontWeight:\"600\"},children:translate(\"Upload file\")})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",sx:{fontSize:\"14px\"},children:translate(\"Drag & Drop to upload file\")}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",align:\"center\",color:\"textSecondary\",sx:{marginTop:\"8px\",fontSize:\"14px\"},children:translate(\"Or\")}),showHyperlinkInput.isOpen&&showHyperlinkInput.currentContainerId===id?/*#__PURE__*/_jsx(TextField,{value:imageLink,onChange:e=>setImageLink(e.target.value),onKeyDown:handleLinkSubmit,autoFocus:true}):/*#__PURE__*/_jsxs(Box,{sx:iconRowStyle,children:[/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\"),arrow:true,children:/*#__PURE__*/_jsx(\"div\",{style:{pointerEvents:\"auto\",cursor:\"pointer\"},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:hyperlink},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\",opacity:\"0.5\",pointerEvents:\"none\"},id:\"hyperlink\",className:\"qadpt-image-upload\"})})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\"),arrow:true,children:/*#__PURE__*/_jsx(\"span\",{onClick:event=>{//setModelOpen(true);\n},dangerouslySetInnerHTML:{__html:files},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\",opacity:\"0.5\"},id:\"folder\",className:\"qadpt-image-upload\"//title=\"Coming Soon\"\n})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Upload File\"),arrow:true,children:/*#__PURE__*/_jsx(\"span\",{onClick:event=>{var _document$getElementB2;event===null||event===void 0?void 0:event.stopPropagation();(_document$getElementB2=document.getElementById(\"file-upload\"))===null||_document$getElementB2===void 0?void 0:_document$getElementB2.click();},id:\"file-upload1\",className:\"qadpt-image-upload\",dangerouslySetInnerHTML:{__html:uploadicon},style:{color:\"black\",cursor:\"pointer\",fontSize:\"32px\"}})}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"file-upload\",style:{display:\"none\"},accept:\"image/*\",onChange:handleImageUpload}),/*#__PURE__*/_jsx(Snackbar,{open:snackbarOpen,autoHideDuration:3000,onClose:closeSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:closeSnackbar,severity:snackbarSeverity,sx:{width:'100%'},children:snackbarMessage})})]})]})})},id);}),Boolean(imageAnchorEl.value)?/*#__PURE__*/_jsx(Popover,{className:\"qadpt-imgsec-popover\",id:id,open:open,anchorEl:imageAnchorEl.value,onClose:handleClose,anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"},children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:\"20px\",height:\"100%\",padding:\"0 10px\",fontSize:\"12px\"},children:[/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\"},children:currentImageSectionInfo.currentContainerId===imageAnchorEl.containerId&&currentImageSectionInfo.isImage?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:replaceimageicon}}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",marginLeft:\"5px\",onClick:triggerImageUpload,children:translate(\"Replace Image\")}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"replace-upload\",style:{display:\"none\"},accept:\"image/*\",onChange:handleReplaceImage})]}):null}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-tool-items\",sx:{display:\"flex\",alignItems:\"center\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:sectionheight}}),/*#__PURE__*/_jsx(Tooltip,{title:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?translate(\"Minimum height reached\"):translate(\"Decrease height\"),arrow:true,children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleDecreaseHeight(currentImageSectionInfo.height),size:\"small\",disabled:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT,sx:{opacity:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?0.5:1,cursor:currentImageSectionInfo.height<=IMG_CONTAINER_MIN_HEIGHT?'not-allowed':'pointer'},children:/*#__PURE__*/_jsx(RemoveIcon,{fontSize:\"small\"})})})}),/*#__PURE__*/_jsx(Typography,{fontSize:\"12px\",children:currentImageSectionInfo.height}),/*#__PURE__*/_jsx(Tooltip,{title:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?translate(\"Maximum height reached\"):translate(\"Increase height\"),arrow:true,children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIncreaseHeight(currentImageSectionInfo.height),size:\"small\",disabled:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT,sx:{opacity:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?0.5:1,cursor:currentImageSectionInfo.height>=IMG_CONTAINER_MAX_HEIGHT?'not-allowed':'pointer'},children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\"})})})})]}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Settings\"),arrow:true,children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-tool-items\",children:[/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSettingsClick,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:Settings},style:{color:\"black\"}})})}),/*#__PURE__*/_jsx(Popover,{className:\"qadpt-imgset\",open:openSettingsPopover,anchorEl:settingsAnchorEl,onClose:handleCloseSettingsPopover,anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"},slotProps:{paper:{sx:{mt:12,ml:20,width:\"205px\"}}},children:/*#__PURE__*/_jsxs(Box,{p:2,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{color:\"rgba(95, 158, 160, 1)\"},children:translate(\"Image Properties\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleCloseSettingsPopover,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:CrossIcon},style:{color:\"black\"}})})]}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Coming soon\"),arrow:true,children:/*#__PURE__*/_jsxs(Box,{mt:2,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{marginBottom:\"10px\"},children:translate(\"Image Actions\")}),/*#__PURE__*/_jsxs(TextField,{select:true,fullWidth:true,variant:\"outlined\",size:\"small\",value:selectedAction,onChange:handleActionChange,sx:{\"& .MuiOutlinedInput-root\":{borderColor:\"rgba(246, 238, 238, 1)\"}},disabled:true,children:[/*#__PURE__*/_jsx(MenuItem,{value:\"none\",children:translate(\"None\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"specificStep\",children:translate(\"Specific Step\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"openUrl\",children:translate(\"Open URL\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"clickElement\",children:translate(\"Click Element\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"startTour\",children:translate(\"Start Tour\")}),/*#__PURE__*/_jsx(MenuItem,{value:\"startMicroSurvey\",children:translate(\"Start Micro Survey\")})]})]})}),/*#__PURE__*/_jsxs(Box,{mt:2,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:translate(\"Image Formatting\")}),/*#__PURE__*/_jsx(Box,{display:\"flex\",gap:1,mt:1,children:[\"Fill\",\"Fit\"].map(item=>{// Get current image's objectFit to determine selected state\nconst currentContainer=imagesContainer.find(c=>c.id===imageAnchorEl.containerId);const currentImage=currentContainer===null||currentContainer===void 0?void 0:currentContainer.images.find(img=>img.id===imageAnchorEl.buttonId);const currentObjectFit=(currentImage===null||currentImage===void 0?void 0:currentImage.objectFit)||IMG_OBJECT_FIT;// Determine if this button should be selected\nconst isSelected=item===\"Fill\"&&currentObjectFit===\"cover\"||item===\"Fit\"&&currentObjectFit===\"contain\";return/*#__PURE__*/_jsx(Button,{onClick:()=>toggleFit(imageAnchorEl.containerId,imageAnchorEl.buttonId,item),variant:\"outlined\",size:\"small\",sx:{width:\"88.5px\",height:\"41px\",padding:\"10px 12px\",gap:\"12px\",borderRadius:\"6px 6px 6px 6px\",border:isSelected?\"1px solid rgba(95, 158, 160, 1)\":\"1px solid rgba(246, 238, 238, 1)\",backgroundColor:isSelected?\"rgba(95, 158, 160, 0.2)\":\"rgba(246, 238, 238, 0.5)\",backgroundBlendMode:\"multiply\",color:\"black\",\"&:hover\":{backgroundColor:isSelected?\"rgba(95, 158, 160, 0.3)\":\"rgba(246, 238, 238, 0.6)\"}},children:translate(item)},item);})})]})]})})]})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Background Color\"),arrow:true,children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleBackgroundColorClick,size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{style:{backgroundColor:selectedColor,borderRadius:\"100%\",width:\"20px\",height:\"20px\",display:\"inline-block\",marginTop:\"-3px\"}})})})}),/*#__PURE__*/_jsx(Tooltip,{title:isCloneDisabled?translate(\"Maximum limit of 3 Image sections reached\"):translate(\"Clone Section\"),arrow:true,children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleCloneImgContainer,size:\"small\",disabled:isCloneDisabled,children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:copyicon},style:{opacity:isCloneDisabled?0.5:1}})})})}),/*#__PURE__*/_jsx(Tooltip,{title:translate(\"Delete Section\"),arrow:true,children:/*#__PURE__*/_jsx(Box,{className:\"qadpt-tool-items\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleDeleteSection,size:\"small\",children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:deleteicon},style:{marginTop:\"-3px\"}})})})})]})}):null,isModelOpen&&/*#__PURE__*/_jsx(SelectImageFromApplication,{isOpen:isModelOpen,handleModelClose:()=>setModelOpen(false),onImageSelect:handleImageUploadFormApp,setFormOfUpload:setFormOfUpload,formOfUpload:formOfUpload,handleReplaceImage:handleReplaceImage,isReplaceImage:isReplaceImage}),/*#__PURE__*/_jsx(Popover,{open:colorPickerOpen,anchorEl:colorPickerAnchorEl,onClose:handleCloseColorPicker,anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"top\",horizontal:\"center\"},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(ChromePicker,{color:currentContainerColor,onChange:handleColorChange}),/*#__PURE__*/_jsx(\"style\",{children:`\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `})]})})]});};export default ImageSection;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "TextField", "MenuItem", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "RemoveIcon", "AddIcon", "useTranslation", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "copyicon", "deleteicon", "sectionheight", "Settings", "CrossIcon", "useDrawerStore", "IMG_CONTAINER_DEFAULT_HEIGHT", "IMG_CONTAINER_MAX_HEIGHT", "IMG_CONTAINER_MIN_HEIGHT", "IMG_OBJECT_FIT", "IMG_STEP_VALUE", "ChromePicker", "SelectImageFromApplication", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ImageSection", "_ref", "_imagesContainer$find", "setImageSrc", "setImageName", "onDelete", "onClone", "isCloneDisabled", "t", "translate", "uploadImage", "imagesContainer", "imageAnchorEl", "setImageAnchorEl", "replaceImage", "cloneImageContainer", "deleteImageContainer", "updateImageContainer", "toggleFit", "storeImageSrc", "state", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarKey", "setSnackbarKey", "openSnackbar", "prev", "closeSnackbar", "showHyperlinkInput", "setShowHyperlinkInput", "currentContainerId", "isOpen", "imageLink", "setImageLink", "colorPickerAnchorEl", "setColorPickerAnchorEl", "currentImageSectionInfo", "setCurrentImageSectionInfo", "isImage", "height", "selectedAction", "setSelectedAction", "isModelOpen", "setModelOpen", "formOfUpload", "setFormOfUpload", "settingsAnchorEl", "setSettingsAnchorEl", "selectedColor", "setSelectedColor", "isReplaceImage", "setReplaceImage", "openSettingsPopover", "Boolean", "handleActionChange", "event", "target", "value", "handleSettingsClick", "currentTarget", "handleCloseSettingsPopover", "imageContainerStyle", "width", "display", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageStyle", "borderRadius", "iconRowStyle", "gap", "marginTop", "iconTextStyle", "flexDirection", "handleImageUpload", "_event$target$files", "file", "_event$target$files2", "parts", "name", "split", "extension", "pop", "length", "reader", "FileReader", "onloadend", "base64Image", "result", "containerId", "altText", "id", "crypto", "randomUUID", "url", "backgroundColor", "objectFit", "readAsDataURL", "handleImageUploadFormApp", "Url", "buttonId", "FileName", "handleReplaceImage", "_event$target$files3", "handleClick", "imageId", "currentHeight", "includes", "handleClose", "open", "colorPickerOpen", "undefined", "handleIncreaseHeight", "prevHeight", "newHeight", "Math", "min", "handleDecreaseHeight", "max", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "currentContainerColor", "find", "item", "style", "handleDeleteSection", "handleLinkSubmit", "key", "handleCloneImgContainer", "handleCloseColorPicker", "handleColorChange", "color", "hex", "handleBackgroundColorClick", "children", "map", "_item$images$", "_item$images$2", "_item$images$3", "_item$style", "imageSrc", "images", "currentSecHeight", "sx", "onClick", "e", "component", "onMouseOver", "src", "alt", "textAlign", "dangerouslySetInnerHTML", "__html", "variant", "align", "fontSize", "fontWeight", "onChange", "onKeyDown", "autoFocus", "title", "arrow", "pointerEvents", "cursor", "opacity", "className", "_document$getElementB2", "stopPropagation", "type", "accept", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "anchorEl", "transform<PERSON><PERSON>in", "marginLeft", "size", "disabled", "slotProps", "paper", "mt", "ml", "p", "marginBottom", "select", "fullWidth", "borderColor", "currentC<PERSON><PERSON>", "c", "currentImage", "img", "currentObjectFit", "isSelected", "border", "backgroundBlendMode", "handleModelClose", "onImageSelect"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Imagesection.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n\t<PERSON>,\r\n\tTypo<PERSON>,\r\n\t<PERSON>over,\r\n\tIconButton,\r\n\tTextField,\r\n\tMenuItem,\r\n\tButton,\r\n\tTooltip,\r\n\tSnackbar,\r\n\tAlert\r\n} from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { FileUpload } from \"../../../models/FileUpload\";\r\n\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n\tSettings,\r\n\tCrossIcon,\r\n} from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, {\r\n\tIMG_CONTAINER_DEFAULT_HEIGHT,\r\n\tIMG_CONTAINER_MAX_HEIGHT,\r\n\tIMG_CONTAINER_MIN_HEIGHT,\r\n\tIMG_OBJECT_FIT,\r\n\tIMG_STEP_VALUE,\r\n} from \"../../../store/drawerStore\";\r\nimport { Chrome<PERSON>icker, ColorResult } from \"react-color\";\r\nimport \"./PopupSections.css\";\r\nimport SelectImageFromApplication from \"../../common/SelectImageFromApplication\";\r\n\r\nconst ImageSection = ({ setImageSrc, setImageName, onDelete, onClone, isCloneDisabled }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tuploadImage,\r\n\t\timagesContainer,\r\n\t\timageAnchorEl,\r\n\t\tsetImageAnchorEl,\r\n\t\treplaceImage,\r\n\t\tcloneImageContainer,\r\n\t\tdeleteImageContainer,\r\n\t\tupdateImageContainer,\r\n\t\ttoggleFit,\r\n\t\tsetImageSrc: storeImageSrc,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState('');\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info' | 'warning'>('info');\r\n\r\n\tconst [snackbarKey, setSnackbarKey] = useState<number>(0); \r\n\r\n\tconst openSnackbar = () => {\r\n\t\tsetSnackbarKey(prev => prev + 1);\r\n\t\tsetSnackbarOpen(true);\r\n\t};\r\n\tconst closeSnackbar = () => {\r\n\t\tsetSnackbarOpen(false);\r\n\t};\r\n\tconst [showHyperlinkInput, setShowHyperlinkInput] = useState<{ currentContainerId: string; isOpen: boolean }>({\r\n\t\tcurrentContainerId: \"\",\r\n\t\tisOpen: false,\r\n\t});\r\n\tconst [imageLink, setImageLink] = useState<string>(\"\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [currentImageSectionInfo, setCurrentImageSectionInfo] = useState<{\r\n\t\tcurrentContainerId: string;\r\n\t\tisImage: boolean;\r\n\t\theight: number;\r\n\t}>({ currentContainerId: \"\", isImage: false, height: IMG_CONTAINER_DEFAULT_HEIGHT });\r\n\r\n\tconst [selectedAction, setSelectedAction] = useState(\"none\");\r\n\tconst [isModelOpen, setModelOpen] = useState(false);\r\n\tconst [formOfUpload, setFormOfUpload] = useState<String>(\"\");\r\n\tconst [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [isReplaceImage, setReplaceImage] = useState(false);\r\n\r\n\r\n\tconst openSettingsPopover = Boolean(settingsAnchorEl);\r\n\r\n\tconst handleActionChange = (event: any) => {\r\n\t\tsetSelectedAction(event.target.value);\r\n\t};\r\n\r\n\tconst handleSettingsClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSettingsAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleCloseSettingsPopover = () => {\r\n\t\tsetSettingsAnchorEl(null);\r\n\t};\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t\tmarginTop: \"10px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"column\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst parts = file.name.split('.');\r\n   \t\t\tconst extension = parts.pop();\r\n\r\n   \t\t // Check for double extensions (e.g. file.html.png) or missing/invalid extension\r\n   \t\t\t if (parts.length > 1 || !extension ) {\r\n\t\t\t  setSnackbarMessage(\"Uploaded file name should not contain any special character\");\r\n       \t\t setSnackbarSeverity(\"error\");\r\n\t\t\t setSnackbarOpen(true);\r\n\t\t\t event.target.value = '';\r\n      \t\t return;\r\n\t\t\t \r\n   \t\t\t }\r\n\t\t\t if(file.name.length > 128){\r\n\t\t\t\tsetSnackbarMessage(\"File name should not exceed 128 characters\");\r\n       \t\t\tsetSnackbarSeverity(\"error\");\r\n\t\t\t \tsetSnackbarOpen(true);\r\n\t\t\t \tevent.target.value = '';\r\n      \t\t \treturn;\r\n\t\t\t }\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tconst base64Image = reader.result as string;\r\n\t\t\t\tstoreImageSrc(base64Image);\r\n\t\t\t\tsetImageSrc(base64Image);\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\t\turl: base64Image,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\r\n\tconst handleImageUploadFormApp = (file: FileUpload) => {\r\n\t\tif (file) {\r\n\t\t\tstoreImageSrc(file.Url);\r\n\t\t\tsetImageSrc(file.Url);\r\n\t\t\tif (isReplaceImage) {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: file.Url,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t\tsetReplaceImage(false);\r\n\t\t\t} else {\r\n\t\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\t\taltText: file.FileName,\r\n\t\t\t\t\tid: crypto.randomUUID(), // Use existing ID\r\n\t\t\t\t\turl: file.Url, // Directly use the URL\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetModelOpen(false);\r\n\t};\r\n\tconst handleReplaceImage = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\treplaceImage(imageAnchorEl.containerId, imageAnchorEl.buttonId, {\r\n\t\t\t\t\taltText: file.name,\r\n\t\t\t\t\tid: imageAnchorEl.buttonId,\r\n\t\t\t\t\turl: reader.result,\r\n\t\t\t\t\tbackgroundColor: \"#ffffff\",\r\n\t\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (\r\n\t\tevent: React.MouseEvent<HTMLElement>,\r\n\t\tcontainerId: string,\r\n\t\timageId: string,\r\n\t\tisImage: boolean,\r\n\t\tcurrentHeight: number\r\n\t) => {\r\n\t\t// @ts-ignore\r\n\t\tif ([\"file-upload\", \"hyperlink\"].includes(event.target.id)) return;\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: imageId,\r\n\t\t\tcontainerId: containerId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\tsetSettingsAnchorEl(null);\r\n\t\tsetCurrentImageSectionInfo({\r\n\t\t\tcurrentContainerId: containerId,\r\n\t\t\tisImage,\r\n\t\t\theight: currentHeight,\r\n\t\t});\r\n\t\tsetShowHyperlinkInput({\r\n\t\t\tcurrentContainerId: \"\",\r\n\t\t\tisOpen: false,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t};\r\n\r\n\tconst open = Boolean(imageAnchorEl.value);\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight >= IMG_CONTAINER_MAX_HEIGHT) return;\r\n\t\tconst newHeight = Math.min(prevHeight + IMG_STEP_VALUE, IMG_CONTAINER_MAX_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = (prevHeight: number) => {\r\n\t\tif (prevHeight <= IMG_CONTAINER_MIN_HEIGHT) return;\r\n\t\tconst newHeight = Math.max(prevHeight - IMG_STEP_VALUE, IMG_CONTAINER_MIN_HEIGHT);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\theight: newHeight,\r\n\t\t});\r\n\t\tsetCurrentImageSectionInfo((prev) => ({ ...prev, height: newHeight }));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t\t// setModelOpen(true);\r\n\t\t// setReplaceImage(true);\r\n\t};\r\n\r\n\tconst currentContainerColor =\r\n\t\timagesContainer.find((item) => item.id === imageAnchorEl.containerId)?.style.backgroundColor || \"transparent\";\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetImageAnchorEl({\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tcontainerId: \"\",\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Call the delete function from the store\r\n\t\tdeleteImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleLinkSubmit = (event: React.KeyboardEvent<HTMLInputElement>) => {\r\n\t\tif (event.key === \"Enter\" && imageLink) {\r\n\t\t\tuploadImage(imageAnchorEl.containerId, {\r\n\t\t\t\taltText: \"New Image\",\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\turl: imageLink,\r\n\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\tobjectFit: IMG_OBJECT_FIT,\r\n\t\t\t});\r\n\t\t\tsetShowHyperlinkInput({\r\n\t\t\t\tcurrentContainerId: \"\",\r\n\t\t\t\tisOpen: false,\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloneImgContainer = () => {\r\n\t\t// Check if cloning is disabled due to section limits\r\n\t\tif (isCloneDisabled) {\r\n\t\t\treturn; // Don't clone if limit is reached\r\n\t\t}\r\n\r\n\t\t// Call the clone function from the store\r\n\t\tcloneImageContainer(imageAnchorEl.containerId);\r\n\r\n\t\t// Call the onClone callback if provided\r\n\t\tif (onClone) {\r\n\t\t\tonClone();\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\tupdateImageContainer(imageAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{imagesContainer.map((item) => {\r\n\t\t\t\tconst imageSrc = item.images[0]?.url;\r\n\t\t\t\tconst imageId = item.images[0]?.id;\r\n\t\t\t\tconst objectFit = item.images[0]?.objectFit || IMG_OBJECT_FIT;\r\n\t\t\t\tconst currentSecHeight = (item?.style?.height as number) || IMG_CONTAINER_DEFAULT_HEIGHT;\r\n\t\t\t\tconst id = item.id;\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tkey={id}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t// padding: \"5px\",\r\n\t\t\t\t\t\t\tmargin: \"0px\",\r\n\t\t\t\t\t\t\toverflow: \"auto\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t...imageContainerStyle,\r\n\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\theight: `${item.style.height}px`,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={(e) => handleClick(e, id, imageId, imageSrc ? true : false, currentSecHeight)}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid={id}\r\n\t\t\t\t\t\t\tonMouseOver={() => {\r\n\t\t\t\t\t\t\t\tsetImageAnchorEl({\r\n\t\t\t\t\t\t\t\t\tbuttonId: imageId,\r\n\t\t\t\t\t\t\t\t\tcontainerId: id,\r\n\t\t\t\t\t\t\t\t\tvalue: null,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{imageSrc ? (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={imageSrc}\r\n\t\t\t\t\t\t\t\t\talt=\"Uploaded\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ ...imageStyle, objectFit }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={iconTextStyle}\r\n\t\t\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadfile }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"inline-block\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"h6\"\r\n\t\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\", fontWeight: \"600\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Drag & Drop to upload file\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\talign=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ marginTop: \"8px\", fontSize: \"14px\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Or\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t{showHyperlinkInput.isOpen && showHyperlinkInput.currentContainerId === id ? (\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={imageLink}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setImageLink(e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tonKeyDown={handleLinkSubmit}\r\n\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={iconRowStyle}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")} arrow>\r\n  <div style={{ pointerEvents: \"auto\", cursor:\"pointer\"}}>\r\n    <span\r\n      dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n      style={{\r\n        color: \"black\",\r\n        cursor: \"pointer\",\r\n        fontSize: \"32px\",\r\n        opacity: \"0.5\",\r\n        pointerEvents: \"none\",\r\n      }}\r\n      id=\"hyperlink\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n    />\r\n  </div>\r\n</Tooltip>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")} arrow>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t//setModelOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: files }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\", opacity: \"0.5\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"folder\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t//title=\"Coming Soon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Upload File\")} arrow>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={(event) => {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tevent?.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdocument.getElementById(\"file-upload\")?.click();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload1\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-image-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: uploadicon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\", cursor: \"pointer\", fontSize: \"32px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"file-upload\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<Snackbar open={snackbarOpen} autoHideDuration={3000} onClose={closeSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Alert onClose={closeSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{snackbarMessage}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Alert>\r\n\t\t\t\t\t\t\t\t\t\t\t</Snackbar>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t{Boolean(imageAnchorEl.value) ? (\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tclassName=\"qadpt-imgsec-popover\"\r\n\t\t\t\t\tid={id}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\tanchorEl={imageAnchorEl.value}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"20px\",\r\n\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"0 10px\",\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<Box sx={{ display: \"flex\" }}>\r\n\t\t\t\t\t\t\t{currentImageSectionInfo.currentContainerId === imageAnchorEl.containerId &&\r\n\t\t\t\t\t\t\tcurrentImageSectionInfo.isImage ? (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tfontSize=\"12px\"\r\n\t\t\t\t\t\t\t\t\t\tmarginLeft={\"5px\"}\r\n\t\t\t\t\t\t\t\t\t\tonClick={triggerImageUpload}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleReplaceImage}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\r\n<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-tool-items\"\r\n\t\t\t\t\t\t\tsx={{ display: \"flex\", alignItems: \"center\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? translate(\"Minimum height reached\") : translate(\"Decrease height\")} arrow>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleDecreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height <= IMG_CONTAINER_MIN_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t<Typography fontSize=\"12px\">{currentImageSectionInfo.height}</Typography>\r\n\t\t\t\t\t\t\t<Tooltip title={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? translate(\"Maximum height reached\") : translate(\"Increase height\")} arrow>\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => handleIncreaseHeight(currentImageSectionInfo.height)}\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tdisabled={currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\topacity: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: currentImageSectionInfo.height >= IMG_CONTAINER_MAX_HEIGHT ? 'not-allowed' : 'pointer'\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Settings\")} arrow>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleSettingsClick}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Settings }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-imgset\"\r\n\t\t\t\t\t\t\t\topen={openSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorEl={settingsAnchorEl}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"right\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\t\t\t\tvertical: \"center\",\r\n\t\t\t\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\tpaper: {\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\tmt: 12,\r\n\t\t\t\t\t\t\t\t\t\tml: 20,\r\n\t\t\t\t\t\t\t\t\t\twidth: \"205px\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box p={2}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"space-between\"\r\n\t\t\t\t\t\t\t\t\t\talignItems=\"center\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"subtitle1\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ color: \"rgba(95, 158, 160, 1)\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Properties\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleCloseSettingsPopover}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CrossIcon }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"black\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t<Tooltip title={translate(\"Coming soon\")} arrow>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Actions\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={selectedAction}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={handleActionChange}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"none\">{translate(\"None\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"specificStep\">{translate(\"Specific Step\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"openUrl\">{translate(\"Open URL\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"clickElement\">{translate(\"Click Element\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startTour\">{translate(\"Start Tour\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"startMicroSurvey\">{translate(\"Start Micro Survey\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t<Box mt={2}>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\t\t\t\tcolor=\"textSecondary\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Image Formatting\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\t\tgap={1}\r\n\t\t\t\t\t\t\t\t\t\t\tmt={1}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{[\"Fill\", \"Fit\"].map((item) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Get current image's objectFit to determine selected state\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentContainer = imagesContainer.find((c) => c.id === imageAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentImage = currentContainer?.images.find((img) => img.id === imageAnchorEl.buttonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst currentObjectFit = currentImage?.objectFit || IMG_OBJECT_FIT;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t// Determine if this button should be selected\r\n\t\t\t\t\t\t\t\t\t\t\t\tconst isSelected = (item === \"Fill\" && currentObjectFit === \"cover\") ||\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (item === \"Fit\" && currentObjectFit === \"contain\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() =>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttoggleFit(imageAnchorEl.containerId, imageAnchorEl.buttonId, item as \"Fit\" | \"Fill\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"88.5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"41px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"10px 12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"6px 6px 6px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"1px solid rgba(95, 158, 160, 1)\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"1px solid rgba(246, 238, 238, 1)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.2)\" : \"rgba(246, 238, 238, 0.5)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundBlendMode: \"multiply\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"black\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisSelected ? \"rgba(95, 158, 160, 0.3)\" : \"rgba(246, 238, 238, 0.6)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(item)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")} arrow>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t    display: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\tmarginTop:\"-3px\"\r\n\t\t\t\t\t\t\t\t}} />\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Image sections reached\") : translate(\"Clone Section\")} arrow>\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleCloneImgContainer}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Section\")} arrow>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-tool-items\">\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tonClick={handleDeleteSection}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} style={{ marginTop: \"-3px\" }}/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\t\t\t) : null}\r\n\t\t\t{\r\n\t\t\t\tisModelOpen && (\r\n\t\t\t\t\t<SelectImageFromApplication isOpen={isModelOpen} handleModelClose={() => setModelOpen(false)} onImageSelect={handleImageUploadFormApp} setFormOfUpload={setFormOfUpload} formOfUpload={formOfUpload} handleReplaceImage={handleReplaceImage} isReplaceImage={isReplaceImage}/>\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<style>\r\n    {`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n  </style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSection;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,QAAQ,KAAQ,OAAO,CAClD,OACCC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,UAAU,CACVC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,OAAO,CACPC,QAAQ,CACRC,KAAK,KACC,eAAe,CACtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,OAASC,cAAc,KAAQ,eAAe,CAI9C,OACCC,UAAU,CACVC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,gBAAgB,CAChBC,QAAQ,CACRC,UAAU,CACVC,aAAa,CACbC,QAAQ,CACRC,SAAS,KACH,6BAA6B,CACpC,MAAO,CAAAC,cAAc,EACpBC,4BAA4B,CAC5BC,wBAAwB,CACxBC,wBAAwB,CACxBC,cAAc,CACdC,cAAc,KACR,4BAA4B,CACnC,OAASC,YAAY,KAAqB,aAAa,CACvD,MAAO,qBAAqB,CAC5B,MAAO,CAAAC,0BAA0B,KAAM,yCAAyC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjF,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA4E,KAAAC,qBAAA,IAA3E,CAAEC,WAAW,CAAEC,YAAY,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,eAAqB,CAAC,CAAAN,IAAA,CAC3F,KAAM,CAAEO,CAAC,CAAEC,SAAU,CAAC,CAAGlC,cAAc,CAAC,CAAC,CACzC,KAAM,CACLmC,WAAW,CACXC,eAAe,CACfC,aAAa,CACbC,gBAAgB,CAChBC,YAAY,CACZC,mBAAmB,CACnBC,oBAAoB,CACpBC,oBAAoB,CACpBC,SAAS,CACTf,WAAW,CAAEgB,aACd,CAAC,CAAGjC,cAAc,CAAEkC,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC6D,eAAe,CAAEC,kBAAkB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhE,QAAQ,CAA2C,MAAM,CAAC,CAE1G,KAAM,CAACiE,WAAW,CAAEC,cAAc,CAAC,CAAGlE,QAAQ,CAAS,CAAC,CAAC,CAEzD,KAAM,CAAAmE,YAAY,CAAGA,CAAA,GAAM,CAC1BD,cAAc,CAACE,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAChCR,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,CACD,KAAM,CAAAS,aAAa,CAAGA,CAAA,GAAM,CAC3BT,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACD,KAAM,CAACU,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvE,QAAQ,CAAkD,CAC7GwE,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG3E,QAAQ,CAAS,EAAE,CAAC,CACtD,KAAM,CAAC4E,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG7E,QAAQ,CAAqB,IAAI,CAAC,CACxF,KAAM,CAAC8E,uBAAuB,CAAEC,0BAA0B,CAAC,CAAG/E,QAAQ,CAInE,CAAEwE,kBAAkB,CAAE,EAAE,CAAEQ,OAAO,CAAE,KAAK,CAAEC,MAAM,CAAExD,4BAA6B,CAAC,CAAC,CAEpF,KAAM,CAACyD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnF,QAAQ,CAAC,MAAM,CAAC,CAC5D,KAAM,CAACoF,WAAW,CAAEC,YAAY,CAAC,CAAGrF,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACsF,YAAY,CAAEC,eAAe,CAAC,CAAGvF,QAAQ,CAAS,EAAE,CAAC,CAC5D,KAAM,CAACwF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzF,QAAQ,CAAqB,IAAI,CAAC,CAClF,KAAM,CAAC0F,aAAa,CAAEC,gBAAgB,CAAC,CAAG3F,QAAQ,CAAS,SAAS,CAAC,CACrE,KAAM,CAAC4F,cAAc,CAAEC,eAAe,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CAGzD,KAAM,CAAA8F,mBAAmB,CAAGC,OAAO,CAACP,gBAAgB,CAAC,CAErD,KAAM,CAAAQ,kBAAkB,CAAIC,KAAU,EAAK,CAC1Cd,iBAAiB,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACtC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIH,KAAoC,EAAK,CACrER,mBAAmB,CAACQ,KAAK,CAACI,aAAa,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,0BAA0B,CAAGA,CAAA,GAAM,CACxCb,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CACD,KAAM,CAAAc,mBAAwC,CAAG,CAChDC,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACdwB,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,CAAC,CACVC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QACX,CAAC,CAED,KAAM,CAAAC,UAA+B,CAAG,CACvCP,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACd4B,MAAM,CAAE,CAAC,CACTD,OAAO,CAAE,CAAC,CACVI,YAAY,CAAE,GACf,CAAC,CAED,KAAM,CAAAC,YAAiC,CAAG,CACzCR,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBQ,GAAG,CAAE,MAAM,CACXC,SAAS,CAAE,MACZ,CAAC,CAED,KAAM,CAAAC,aAAkC,CAAG,CAC1CX,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBV,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBF,KAAK,CAAE,MACR,CAAC,CAED,KAAM,CAAAc,iBAAiB,CAAIrB,KAA0C,EAAK,KAAAsB,mBAAA,CACzE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGtB,KAAK,CAACC,MAAM,CAAClF,KAAK,UAAAuG,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIC,IAAI,CAAE,KAAAC,oBAAA,CACT,KAAM,CAAAC,KAAK,CAAGF,IAAI,CAACG,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAC/B,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,GAAG,CAAC,CAAC,CAE7B;AACC,GAAIJ,KAAK,CAACK,MAAM,CAAG,CAAC,EAAI,CAACF,SAAS,CAAG,CACvC/D,kBAAkB,CAAC,6DAA6D,CAAC,CAC5EE,mBAAmB,CAAC,OAAO,CAAC,CAClCJ,eAAe,CAAC,IAAI,CAAC,CACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAG,EAAE,CAClB,OAEF,CACH,GAAGqB,IAAI,CAACG,IAAI,CAACI,MAAM,CAAG,GAAG,CAAC,CAC1BjE,kBAAkB,CAAC,4CAA4C,CAAC,CAC1DE,mBAAmB,CAAC,OAAO,CAAC,CACjCJ,eAAe,CAAC,IAAI,CAAC,CACrBqC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAG,EAAE,CAClB,OACN,CACDzD,YAAY,EAAA+E,oBAAA,CAACxB,KAAK,CAACC,MAAM,CAAClF,KAAK,UAAAyG,oBAAA,iBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACE,IAAI,CAAC,CAE1C,KAAM,CAAAK,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACxB,KAAM,CAAAC,WAAW,CAAGH,MAAM,CAACI,MAAgB,CAC3C3E,aAAa,CAAC0E,WAAW,CAAC,CAC1B1F,WAAW,CAAC0F,WAAW,CAAC,CACxBnF,WAAW,CAACE,aAAa,CAACmF,WAAW,CAAE,CACtCC,OAAO,CAAEd,IAAI,CAACG,IAAI,CAClBY,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,GAAG,CAAEP,WAAW,CAChBQ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAEhH,cACZ,CAAC,CAAC,CACH,CAAC,CACDoG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC,CAC3B,CACAnC,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC,CAED,KAAM,CAAAyD,wBAAwB,CAAItB,IAAgB,EAAK,CACtD,GAAIA,IAAI,CAAE,CACT/D,aAAa,CAAC+D,IAAI,CAACuB,GAAG,CAAC,CACvBtG,WAAW,CAAC+E,IAAI,CAACuB,GAAG,CAAC,CACrB,GAAInD,cAAc,CAAE,CACnBxC,YAAY,CAACF,aAAa,CAACmF,WAAW,CAAEnF,aAAa,CAAC8F,QAAQ,CAAE,CAC/DV,OAAO,CAAEd,IAAI,CAACyB,QAAQ,CACtBV,EAAE,CAAErF,aAAa,CAAC8F,QAAQ,CAC1BN,GAAG,CAAElB,IAAI,CAACuB,GAAG,CACbJ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAEhH,cACZ,CAAC,CAAC,CACFiE,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,IAAM,CACN7C,WAAW,CAACE,aAAa,CAACmF,WAAW,CAAE,CACtCC,OAAO,CAAEd,IAAI,CAACyB,QAAQ,CACtBV,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CAAE;AACzBC,GAAG,CAAElB,IAAI,CAACuB,GAAG,CAAE;AACfJ,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAEhH,cACZ,CAAC,CAAC,CACH,CACD,CACAyD,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC,CACD,KAAM,CAAA6D,kBAAkB,CAAIjD,KAA0C,EAAK,KAAAkD,oBAAA,CAC1E,KAAM,CAAA3B,IAAI,EAAA2B,oBAAA,CAAGlD,KAAK,CAACC,MAAM,CAAClF,KAAK,UAAAmI,oBAAA,iBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CACpC,GAAI3B,IAAI,CAAE,CACT,KAAM,CAAAQ,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACxB9E,YAAY,CAACF,aAAa,CAACmF,WAAW,CAAEnF,aAAa,CAAC8F,QAAQ,CAAE,CAC/DV,OAAO,CAAEd,IAAI,CAACG,IAAI,CAClBY,EAAE,CAAErF,aAAa,CAAC8F,QAAQ,CAC1BN,GAAG,CAAEV,MAAM,CAACI,MAAM,CAClBO,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAEhH,cACZ,CAAC,CAAC,CACH,CAAC,CACDoG,MAAM,CAACa,aAAa,CAACrB,IAAI,CAAC,CAC3B,CACD,CAAC,CAED,KAAM,CAAA4B,WAAW,CAAGA,CACnBnD,KAAoC,CACpCoC,WAAmB,CACnBgB,OAAe,CACfrE,OAAgB,CAChBsE,aAAqB,GACjB,CACJ;AACA,GAAI,CAAC,aAAa,CAAE,WAAW,CAAC,CAACC,QAAQ,CAACtD,KAAK,CAACC,MAAM,CAACqC,EAAE,CAAC,CAAE,OAC5DpF,gBAAgB,CAAC,CAChB6F,QAAQ,CAAEK,OAAO,CACjBhB,WAAW,CAAEA,WAAW,CACxB;AACAlC,KAAK,CAAEF,KAAK,CAACI,aACd,CAAC,CAAC,CACFZ,mBAAmB,CAAC,IAAI,CAAC,CACzBV,0BAA0B,CAAC,CAC1BP,kBAAkB,CAAE6D,WAAW,CAC/BrD,OAAO,CACPC,MAAM,CAAEqE,aACT,CAAC,CAAC,CACF/E,qBAAqB,CAAC,CACrBC,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAA+E,WAAW,CAAGA,CAAA,GAAM,CACzBrG,gBAAgB,CAAC,CAChB6F,QAAQ,CAAE,EAAE,CACZX,WAAW,CAAE,EAAE,CACf;AACAlC,KAAK,CAAE,IACR,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAsD,IAAI,CAAG1D,OAAO,CAAC7C,aAAa,CAACiD,KAAK,CAAC,CACzC,KAAM,CAAAuD,eAAe,CAAG3D,OAAO,CAACnB,mBAAmB,CAAC,CAEpD,KAAM,CAAA2D,EAAE,CAAGkB,IAAI,CAAG,eAAe,CAAGE,SAAS,CAE7C,KAAM,CAAAC,oBAAoB,CAAIC,UAAkB,EAAK,CACpD,GAAIA,UAAU,EAAInI,wBAAwB,CAAE,OAC5C,KAAM,CAAAoI,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAAGhI,cAAc,CAAEH,wBAAwB,CAAC,CACjF6B,oBAAoB,CAACL,aAAa,CAACmF,WAAW,CAAE,OAAO,CAAE,CACxDpD,MAAM,CAAE6E,SACT,CAAC,CAAC,CACF/E,0BAA0B,CAAEX,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEa,MAAM,CAAE6E,SAAU,CAAC,CAAC,CAAC,CACvE,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAIJ,UAAkB,EAAK,CACpD,GAAIA,UAAU,EAAIlI,wBAAwB,CAAE,OAC5C,KAAM,CAAAmI,SAAS,CAAGC,IAAI,CAACG,GAAG,CAACL,UAAU,CAAGhI,cAAc,CAAEF,wBAAwB,CAAC,CACjF4B,oBAAoB,CAACL,aAAa,CAACmF,WAAW,CAAE,OAAO,CAAE,CACxDpD,MAAM,CAAE6E,SACT,CAAC,CAAC,CACF/E,0BAA0B,CAAEX,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAEa,MAAM,CAAE6E,SAAU,CAAC,CAAC,CAAC,CACvE,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAChC,CAAAA,qBAAA,CAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,UAAAF,qBAAA,iBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC,CAClD;AACA;AACD,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAC1B,EAAAhI,qBAAA,CAAAS,eAAe,CAACwH,IAAI,CAAEC,IAAI,EAAKA,IAAI,CAACnC,EAAE,GAAKrF,aAAa,CAACmF,WAAW,CAAC,UAAA7F,qBAAA,iBAArEA,qBAAA,CAAuEmI,KAAK,CAAChC,eAAe,GAAI,aAAa,CAC9G;AACA,KAAM,CAAAiC,mBAAmB,CAAGA,CAAA,GAAM,CACjCzH,gBAAgB,CAAC,CAChB6F,QAAQ,CAAE,EAAE,CACZX,WAAW,CAAE,EAAE,CACf;AACAlC,KAAK,CAAE,IACR,CAAC,CAAC,CAEF;AACA7C,oBAAoB,CAACJ,aAAa,CAACmF,WAAW,CAAC,CAE/C;AACA,GAAI1F,QAAQ,CAAE,CACbA,QAAQ,CAAC,CAAC,CACX,CACD,CAAC,CAGD,KAAM,CAAAkI,gBAAgB,CAAI5E,KAA4C,EAAK,CAC1E,GAAIA,KAAK,CAAC6E,GAAG,GAAK,OAAO,EAAIpG,SAAS,CAAE,CACvC1B,WAAW,CAACE,aAAa,CAACmF,WAAW,CAAE,CACtCC,OAAO,CAAE,WAAW,CACpBC,EAAE,CAAEC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvBC,GAAG,CAAEhE,SAAS,CACdiE,eAAe,CAAE,aAAa,CAC9BC,SAAS,CAAEhH,cACZ,CAAC,CAAC,CACF2C,qBAAqB,CAAC,CACrBC,kBAAkB,CAAE,EAAE,CACtBC,MAAM,CAAE,KACT,CAAC,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAAsG,uBAAuB,CAAGA,CAAA,GAAM,CACrC;AACA,GAAIlI,eAAe,CAAE,CACpB,OAAQ;AACT,CAEA;AACAQ,mBAAmB,CAACH,aAAa,CAACmF,WAAW,CAAC,CAE9C;AACA,GAAIzF,OAAO,CAAE,CACZA,OAAO,CAAC,CAAC,CACV,CACD,CAAC,CAED,KAAM,CAAAoI,sBAAsB,CAAGA,CAAA,GAAM,CACpCnG,sBAAsB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAoG,iBAAiB,CAAIC,KAAkB,EAAK,CACjDvF,gBAAgB,CAACuF,KAAK,CAACC,GAAG,CAAC,CAC3B5H,oBAAoB,CAACL,aAAa,CAACmF,WAAW,CAAE,OAAO,CAAE,CACxDM,eAAe,CAAEuC,KAAK,CAACC,GACxB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,0BAA0B,CAAInF,KAAoC,EAAK,CAC5EpB,sBAAsB,CAACoB,KAAK,CAACI,aAAa,CAAC,CAC5C,CAAC,CAED,mBACClE,KAAA,CAAAE,SAAA,EAAAgJ,QAAA,EACEpI,eAAe,CAACqI,GAAG,CAAEZ,IAAI,EAAK,KAAAa,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,WAAA,CAC9B,KAAM,CAAAC,QAAQ,EAAAJ,aAAA,CAAGb,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,UAAAL,aAAA,iBAAdA,aAAA,CAAgB7C,GAAG,CACpC,KAAM,CAAAW,OAAO,EAAAmC,cAAA,CAAGd,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,UAAAJ,cAAA,iBAAdA,cAAA,CAAgBjD,EAAE,CAClC,KAAM,CAAAK,SAAS,CAAG,EAAA6C,cAAA,CAAAf,IAAI,CAACkB,MAAM,CAAC,CAAC,CAAC,UAAAH,cAAA,iBAAdA,cAAA,CAAgB7C,SAAS,GAAIhH,cAAc,CAC7D,KAAM,CAAAiK,gBAAgB,CAAG,CAACnB,IAAI,SAAJA,IAAI,kBAAAgB,WAAA,CAAJhB,IAAI,CAAEC,KAAK,UAAAe,WAAA,iBAAXA,WAAA,CAAazG,MAAM,GAAexD,4BAA4B,CACxF,KAAM,CAAA8G,EAAE,CAAGmC,IAAI,CAACnC,EAAE,CAClB,mBACCtG,IAAA,CAAChC,GAAG,EAEH6L,EAAE,CAAE,CACHtF,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACdwB,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBX,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpB;AACAE,MAAM,CAAE,KAAK,CACbC,QAAQ,CAAE,MACX,CAAE,CAAAuE,QAAA,cAEFpJ,IAAA,CAAChC,GAAG,EACH6L,EAAE,CAAE,CACH,GAAGvF,mBAAmB,CACtBoC,eAAe,CAAE+B,IAAI,CAACC,KAAK,CAAChC,eAAe,CAC3C1D,MAAM,CAAE,GAAGyF,IAAI,CAACC,KAAK,CAAC1F,MAAM,IAC7B,CAAE,CACF8G,OAAO,CAAGC,CAAC,EAAK5C,WAAW,CAAC4C,CAAC,CAAEzD,EAAE,CAAEc,OAAO,CAAEsC,QAAQ,CAAG,IAAI,CAAG,KAAK,CAAEE,gBAAgB,CAAE,CACvFI,SAAS,CAAE,KAAM,CACjB1D,EAAE,CAAEA,EAAG,CACP2D,WAAW,CAAEA,CAAA,GAAM,CAClB/I,gBAAgB,CAAC,CAChB6F,QAAQ,CAAEK,OAAO,CACjBhB,WAAW,CAAEE,EAAE,CACfpC,KAAK,CAAE,IACR,CAAC,CAAC,CACH,CAAE,CAAAkF,QAAA,CAEDM,QAAQ,cACR1J,IAAA,QACCkK,GAAG,CAAER,QAAS,CACdS,GAAG,CAAC,UAAU,CACdzB,KAAK,CAAE,CAAE,GAAG5D,UAAU,CAAE6B,SAAU,CAAE,CACpC,CAAC,cAEFzG,KAAA,CAAClC,GAAG,EACH6L,EAAE,CAAE,CACHO,SAAS,CAAE,QAAQ,CACnB7F,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACdwB,OAAO,CAAE,MAAM,CACfY,aAAa,CAAE,QAAQ,CACvBX,cAAc,CAAE,QACjB,CAAE,CAAA2E,QAAA,eAEFlJ,KAAA,CAAClC,GAAG,EACH6L,EAAE,CAAE1E,aAAc,CAClB6E,SAAS,CAAE,KAAM,CAAAZ,QAAA,eAEjBpJ,IAAA,SACCqK,uBAAuB,CAAE,CAAEC,MAAM,CAAEzL,UAAW,CAAE,CAChD6J,KAAK,CAAE,CAAElE,OAAO,CAAE,cAAe,CAAE,CACnC,CAAC,cACFxE,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,IAAI,CACZC,KAAK,CAAC,QAAQ,CACdX,EAAE,CAAE,CAAEY,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAtB,QAAA,CAE1CtI,SAAS,CAAC,aAAa,CAAC,CACf,CAAC,EACT,CAAC,cAENd,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,OAAO,CACfC,KAAK,CAAC,QAAQ,CACdvB,KAAK,CAAC,eAAe,CACrBY,EAAE,CAAE,CAAEY,QAAQ,CAAE,MAAO,CAAE,CAAArB,QAAA,CAEvBtI,SAAS,CAAC,4BAA4B,CAAC,CAC9B,CAAC,cACbd,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,OAAO,CACfC,KAAK,CAAC,QAAQ,CACdvB,KAAK,CAAC,eAAe,CACrBY,EAAE,CAAE,CAAE3E,SAAS,CAAE,KAAK,CAAEuF,QAAQ,CAAE,MAAO,CAAE,CAAArB,QAAA,CAEzCtI,SAAS,CAAC,IAAI,CAAC,CACN,CAAC,CACZuB,kBAAkB,CAACG,MAAM,EAAIH,kBAAkB,CAACE,kBAAkB,GAAK+D,EAAE,cACzEtG,IAAA,CAAC5B,SAAS,EACT8F,KAAK,CAAEzB,SAAU,CACjBkI,QAAQ,CAAGZ,CAAC,EAAKrH,YAAY,CAACqH,CAAC,CAAC9F,MAAM,CAACC,KAAK,CAAE,CAC9C0G,SAAS,CAAEhC,gBAAiB,CAC5BiC,SAAS,MACT,CAAC,cAEF3K,KAAA,CAAClC,GAAG,EAAC6L,EAAE,CAAE7E,YAAa,CAAAoE,QAAA,eACnBpJ,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,aAAa,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAC1DpJ,IAAA,QAAK0I,KAAK,CAAE,CAAEsC,aAAa,CAAE,MAAM,CAAEC,MAAM,CAAC,SAAS,CAAE,CAAA7B,QAAA,cACrDpJ,IAAA,SACEqK,uBAAuB,CAAE,CAAEC,MAAM,CAAExL,SAAU,CAAE,CAC/C4J,KAAK,CAAE,CACLO,KAAK,CAAE,OAAO,CACdgC,MAAM,CAAE,SAAS,CACjBR,QAAQ,CAAE,MAAM,CAChBS,OAAO,CAAE,KAAK,CACdF,aAAa,CAAE,MACjB,CAAE,CACF1E,EAAE,CAAC,WAAW,CACP6E,SAAS,CAAC,oBAAoB,CACtC,CAAC,CACC,CAAC,CACC,CAAC,cAGGnL,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,aAAa,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAC9CpJ,IAAA,SACC8J,OAAO,CAAG9F,KAAK,EAAK,CACnB;AAAA,CACC,CACLqG,uBAAuB,CAAE,CAAEC,MAAM,CAAEvL,KAAM,CAAE,CAC3C2J,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAO,CAAEgC,MAAM,CAAE,SAAS,CAAER,QAAQ,CAAE,MAAM,CAAES,OAAO,CAAE,KAAM,CAAE,CAC/E5E,EAAE,CAAC,QAAQ,CACX6E,SAAS,CAAC,oBACV;AAAA,CACG,CAAC,CACM,CAAC,cACVnL,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,aAAa,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cACjDpJ,IAAA,SACI8J,OAAO,CAAG9F,KAAK,EAAK,KAAAoH,sBAAA,CAEtBpH,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEqH,eAAe,CAAC,CAAC,CACxB,CAAAD,sBAAA,CAAAhD,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,UAAA+C,sBAAA,iBAAtCA,sBAAA,CAAwC9C,KAAK,CAAC,CAAC,CAChD,CAAE,CACFhC,EAAE,CAAC,cAAc,CACjB6E,SAAS,CAAC,oBAAoB,CAC9Bd,uBAAuB,CAAE,CAAEC,MAAM,CAAEtL,UAAW,CAAE,CAChD0J,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAO,CAAEgC,MAAM,CAAE,SAAS,CAAER,QAAQ,CAAE,MAAO,CAAE,CAC5D,CAAC,CACO,CAAC,cACbzK,IAAA,UACCsL,IAAI,CAAC,MAAM,CACXhF,EAAE,CAAC,aAAa,CAChBoC,KAAK,CAAE,CAAElE,OAAO,CAAE,MAAO,CAAE,CAC3B+G,MAAM,CAAC,SAAS,CAChBZ,QAAQ,CAAEtF,iBAAkB,CAC5B,CAAC,cACFrF,IAAA,CAACxB,QAAQ,EAACgJ,IAAI,CAAE9F,YAAa,CAAC8J,gBAAgB,CAAE,IAAK,CAACC,OAAO,CAAErJ,aAAc,CAACsJ,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAxC,QAAA,cACxIpJ,IAAA,CAACvB,KAAK,EAACgN,OAAO,CAAErJ,aAAc,CAACyJ,QAAQ,CAAE/J,gBAAiB,CAAC+H,EAAE,CAAE,CAAEtF,KAAK,CAAE,MAAO,CAAE,CAAA6E,QAAA,CAC/ExH,eAAe,CACV,CAAC,CACC,CAAC,EACP,CACL,EACG,CACL,CACG,CAAC,EApJD0E,EAqJD,CAAC,CAER,CAAC,CAAC,CACDxC,OAAO,CAAC7C,aAAa,CAACiD,KAAK,CAAC,cAC5BlE,IAAA,CAAC9B,OAAO,EACPiN,SAAS,CAAC,sBAAsB,CAChC7E,EAAE,CAAEA,EAAG,CACPkB,IAAI,CAAEA,IAAK,CACXsE,QAAQ,CAAE7K,aAAa,CAACiD,KAAM,CAC9BuH,OAAO,CAAElE,WAAY,CACrBmE,YAAY,CAAE,CACbC,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CACFG,eAAe,CAAE,CAChBJ,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CAAAxC,QAAA,cAEFlJ,KAAA,CAAClC,GAAG,EACH6L,EAAE,CAAE,CACHrF,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBO,GAAG,CAAE,MAAM,CACXjC,MAAM,CAAE,MAAM,CACd2B,OAAO,CAAE,QAAQ,CACjB8F,QAAQ,CAAE,MACX,CAAE,CAAArB,QAAA,eAEFpJ,IAAA,CAAChC,GAAG,EAAC6L,EAAE,CAAE,CAAErF,OAAO,CAAE,MAAO,CAAE,CAAA4E,QAAA,CAC3BvG,uBAAuB,CAACN,kBAAkB,GAAKtB,aAAa,CAACmF,WAAW,EACzEvD,uBAAuB,CAACE,OAAO,cAC9B7C,KAAA,CAAAE,SAAA,EAAAgJ,QAAA,eACCpJ,IAAA,SAAMqK,uBAAuB,CAAE,CAAEC,MAAM,CAAErL,gBAAiB,CAAE,CAAE,CAAC,cAC/De,IAAA,CAAC/B,UAAU,EACVwM,QAAQ,CAAC,MAAM,CACfuB,UAAU,CAAE,KAAM,CAClBlC,OAAO,CAAE5B,kBAAmB,CAAAkB,QAAA,CAE1BtI,SAAS,CAAC,eAAe,CAAC,CACjB,CAAC,cACbd,IAAA,UACCsL,IAAI,CAAC,MAAM,CACXhF,EAAE,CAAC,gBAAgB,CACnBoC,KAAK,CAAE,CAAElE,OAAO,CAAE,MAAO,CAAE,CAC3B+G,MAAM,CAAC,SAAS,CAChBZ,QAAQ,CAAE1D,kBAAmB,CAC7B,CAAC,EACD,CAAC,CACA,IAAI,CACJ,CAAC,cAEZ/G,KAAA,CAAClC,GAAG,EACGmN,SAAS,CAAC,kBAAkB,CAC5BtB,EAAE,CAAE,CAAErF,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAA0E,QAAA,eAE9CpJ,IAAA,SAAMqK,uBAAuB,CAAE,CAAEC,MAAM,CAAElL,aAAc,CAAE,CAAE,CAAC,cAC5DY,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEjI,uBAAuB,CAACG,MAAM,EAAItD,wBAAwB,CAAGoB,SAAS,CAAC,wBAAwB,CAAC,CAAGA,SAAS,CAAC,iBAAiB,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cACrJpJ,IAAA,SAAAoJ,QAAA,cACCpJ,IAAA,CAAC7B,UAAU,EACV2L,OAAO,CAAEA,CAAA,GAAM9B,oBAAoB,CAACnF,uBAAuB,CAACG,MAAM,CAAE,CACpEiJ,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAErJ,uBAAuB,CAACG,MAAM,EAAItD,wBAAyB,CACrEmK,EAAE,CAAE,CACHqB,OAAO,CAAErI,uBAAuB,CAACG,MAAM,EAAItD,wBAAwB,CAAG,GAAG,CAAG,CAAC,CAC7EuL,MAAM,CAAEpI,uBAAuB,CAACG,MAAM,EAAItD,wBAAwB,CAAG,aAAa,CAAG,SACtF,CAAE,CAAA0J,QAAA,cAEFpJ,IAAA,CAACtB,UAAU,EAAC+L,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,CACR,CAAC,CACC,CAAC,cACVzK,IAAA,CAAC/B,UAAU,EAACwM,QAAQ,CAAC,MAAM,CAAArB,QAAA,CAAEvG,uBAAuB,CAACG,MAAM,CAAa,CAAC,cACzEhD,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEjI,uBAAuB,CAACG,MAAM,EAAIvD,wBAAwB,CAAGqB,SAAS,CAAC,wBAAwB,CAAC,CAAGA,SAAS,CAAC,iBAAiB,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cACrJpJ,IAAA,SAAAoJ,QAAA,cACCpJ,IAAA,CAAC7B,UAAU,EACV2L,OAAO,CAAEA,CAAA,GAAMnC,oBAAoB,CAAC9E,uBAAuB,CAACG,MAAM,CAAE,CACpEiJ,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAErJ,uBAAuB,CAACG,MAAM,EAAIvD,wBAAyB,CACrEoK,EAAE,CAAE,CACHqB,OAAO,CAAErI,uBAAuB,CAACG,MAAM,EAAIvD,wBAAwB,CAAG,GAAG,CAAG,CAAC,CAC7EwL,MAAM,CAAEpI,uBAAuB,CAACG,MAAM,EAAIvD,wBAAwB,CAAG,aAAa,CAAG,SACtF,CAAE,CAAA2J,QAAA,cAEFpJ,IAAA,CAACrB,OAAO,EAAC8L,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjB,CAAC,CACR,CAAC,CACC,CAAC,EACN,CAAC,cACNzK,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,UAAU,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAC5ClJ,KAAA,CAAClC,GAAG,EAACmN,SAAS,CAAC,kBAAkB,CAAA/B,QAAA,eAChCpJ,IAAA,CAAChC,GAAG,EAACmN,SAAS,CAAC,kBAAkB,CAAA/B,QAAA,cAChCpJ,IAAA,CAAC7B,UAAU,EACV8N,IAAI,CAAC,OAAO,CACZnC,OAAO,CAAE3F,mBAAoB,CAAAiF,QAAA,cAE7BpJ,IAAA,SACCqK,uBAAuB,CAAE,CAAEC,MAAM,CAAEjL,QAAS,CAAE,CAC9CqJ,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAQ,CAAE,CAC1B,CAAC,CACS,CAAC,CACT,CAAC,cAELjJ,IAAA,CAAC9B,OAAO,EACEiN,SAAS,CAAC,cAAc,CAClC3D,IAAI,CAAE3D,mBAAoB,CAC1BiI,QAAQ,CAAEvI,gBAAiB,CAC3BkI,OAAO,CAAEpH,0BAA2B,CACpCqH,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,OACb,CAAE,CACFG,eAAe,CAAE,CAChBJ,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACb,CAAE,CACFO,SAAS,CAAE,CACVC,KAAK,CAAE,CACPvC,EAAE,CAAE,CACHwC,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EAAE,CACN/H,KAAK,CAAE,OACR,CACA,CACD,CAAE,CAAA6E,QAAA,cAEFlJ,KAAA,CAAClC,GAAG,EAACuO,CAAC,CAAE,CAAE,CAAAnD,QAAA,eACTlJ,KAAA,CAAClC,GAAG,EACHwG,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,eAAe,CAC9BC,UAAU,CAAC,QAAQ,CAAA0E,QAAA,eAEnBpJ,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,WAAW,CACnBV,EAAE,CAAE,CAAEZ,KAAK,CAAE,uBAAwB,CAAE,CAAAG,QAAA,CAErCtI,SAAS,CAAC,kBAAkB,CAAC,CACpB,CAAC,cACbd,IAAA,CAAC7B,UAAU,EACV8N,IAAI,CAAC,OAAO,CACZnC,OAAO,CAAEzF,0BAA2B,CAAA+E,QAAA,cAEpCpJ,IAAA,SACCqK,uBAAuB,CAAE,CAAEC,MAAM,CAAEhL,SAAU,CAAE,CAC/CoJ,KAAK,CAAE,CAAEO,KAAK,CAAE,OAAQ,CAAE,CAC1B,CAAC,CACS,CAAC,EACR,CAAC,cACNjJ,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,aAAa,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAChDlJ,KAAA,CAAClC,GAAG,EAACqO,EAAE,CAAE,CAAE,CAAAjD,QAAA,eACVpJ,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,OAAO,CACbtB,KAAK,CAAC,eAAe,CACrBY,EAAE,CAAE,CAAE2C,YAAY,CAAE,MAAO,CAAE,CAAApD,QAAA,CAE5BtI,SAAS,CAAC,eAAe,CAAC,CAClB,CAAC,cACbZ,KAAA,CAAC9B,SAAS,EACTqO,MAAM,MACNC,SAAS,MACTnC,OAAO,CAAC,UAAU,CAClB0B,IAAI,CAAC,OAAO,CACZ/H,KAAK,CAAEjB,cAAe,CACtB0H,QAAQ,CAAE5G,kBAAmB,CAC7B8F,EAAE,CAAE,CACH,0BAA0B,CAAE,CAC3B8C,WAAW,CAAE,wBACd,CACD,CAAE,CACFT,QAAQ,MAAA9C,QAAA,eAENpJ,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,MAAM,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,MAAM,CAAC,CAAW,CAAC,cACrDd,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,cAAc,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,eAAe,CAAC,CAAW,CAAC,cACtEd,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,SAAS,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,UAAU,CAAC,CAAW,CAAC,cAC5Dd,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,cAAc,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,eAAe,CAAC,CAAW,CAAC,cACtEd,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,WAAW,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,YAAY,CAAC,CAAW,CAAC,cAChEd,IAAA,CAAC3B,QAAQ,EAAC6F,KAAK,CAAC,kBAAkB,CAAAkF,QAAA,CAAEtI,SAAS,CAAC,oBAAoB,CAAC,CAAW,CAAC,EACvE,CAAC,EACR,CAAC,CACG,CAAC,cACVZ,KAAA,CAAClC,GAAG,EAACqO,EAAE,CAAE,CAAE,CAAAjD,QAAA,eACVpJ,IAAA,CAAC/B,UAAU,EACVsM,OAAO,CAAC,OAAO,CACftB,KAAK,CAAC,eAAe,CAAAG,QAAA,CAEnBtI,SAAS,CAAC,kBAAkB,CAAC,CACpB,CAAC,cACbd,IAAA,CAAChC,GAAG,EACHwG,OAAO,CAAC,MAAM,CACdS,GAAG,CAAE,CAAE,CACPoH,EAAE,CAAE,CAAE,CAAAjD,QAAA,CAEL,CAAC,MAAM,CAAE,KAAK,CAAC,CAACC,GAAG,CAAEZ,IAAI,EAAK,CAC9B;AACA,KAAM,CAAAmE,gBAAgB,CAAG5L,eAAe,CAACwH,IAAI,CAAEqE,CAAC,EAAKA,CAAC,CAACvG,EAAE,GAAKrF,aAAa,CAACmF,WAAW,CAAC,CACxF,KAAM,CAAA0G,YAAY,CAAGF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEjD,MAAM,CAACnB,IAAI,CAAEuE,GAAG,EAAKA,GAAG,CAACzG,EAAE,GAAKrF,aAAa,CAAC8F,QAAQ,CAAC,CAC9F,KAAM,CAAAiG,gBAAgB,CAAG,CAAAF,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEnG,SAAS,GAAIhH,cAAc,CAElE;AACA,KAAM,CAAAsN,UAAU,CAAIxE,IAAI,GAAK,MAAM,EAAIuE,gBAAgB,GAAK,OAAO,EAC5DvE,IAAI,GAAK,KAAK,EAAIuE,gBAAgB,GAAK,SAAU,CAExD,mBACChN,IAAA,CAAC1B,MAAM,EAENwL,OAAO,CAAEA,CAAA,GACRvI,SAAS,CAACN,aAAa,CAACmF,WAAW,CAAEnF,aAAa,CAAC8F,QAAQ,CAAE0B,IAAsB,CACnF,CACD8B,OAAO,CAAC,UAAU,CAClB0B,IAAI,CAAC,OAAO,CACZpC,EAAE,CAAE,CACHtF,KAAK,CAAE,QAAQ,CACfvB,MAAM,CAAE,MAAM,CACd2B,OAAO,CAAE,WAAW,CACpBM,GAAG,CAAE,MAAM,CACXF,YAAY,CAAE,iBAAiB,CAC/BmI,MAAM,CACLD,UAAU,CACP,iCAAiC,CACjC,kCAAkC,CACtCvG,eAAe,CACduG,UAAU,CAAG,yBAAyB,CAAG,0BAA0B,CACpEE,mBAAmB,CAAE,UAAU,CAC/BlE,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACVvC,eAAe,CACduG,UAAU,CAAG,yBAAyB,CAAG,0BAC3C,CACD,CAAE,CAAA7D,QAAA,CAEDtI,SAAS,CAAC2H,IAAI,CAAC,EA1BXA,IA2BE,CAAC,CAEX,CAAC,CAAC,CACE,CAAC,EACF,CAAC,EACF,CAAC,CACE,CAAC,EACL,CAAC,CACE,CAAC,cACVzI,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,kBAAkB,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cACpDpJ,IAAA,CAAChC,GAAG,EAACmN,SAAS,CAAC,kBAAkB,CAAA/B,QAAA,cAChCpJ,IAAA,CAAC7B,UAAU,EACV2L,OAAO,CAAEX,0BAA2B,CACpC8C,IAAI,CAAC,OAAO,CAAA7C,QAAA,cAEZpJ,IAAA,SACA0I,KAAK,CAAE,CACNhC,eAAe,CAAEjD,aAAa,CAC9BsB,YAAY,CAAE,MAAM,CACpBR,KAAK,CAAE,MAAM,CACbvB,MAAM,CAAE,MAAM,CACXwB,OAAO,CAAE,cAAc,CAC1BU,SAAS,CAAC,MACX,CAAE,CAAE,CAAC,CACM,CAAC,CACT,CAAC,CACG,CAAC,cACVlF,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAElK,eAAe,CAAGE,SAAS,CAAC,2CAA2C,CAAC,CAAGA,SAAS,CAAC,eAAe,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAC5HpJ,IAAA,CAAChC,GAAG,EAACmN,SAAS,CAAC,kBAAkB,CAAA/B,QAAA,cAChCpJ,IAAA,CAAC7B,UAAU,EACV2L,OAAO,CAAEhB,uBAAwB,CACjCmD,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAEtL,eAAgB,CAAAwI,QAAA,cAE1BpJ,IAAA,SACCqK,uBAAuB,CAAE,CAAEC,MAAM,CAAEpL,QAAS,CAAE,CAC9CwJ,KAAK,CAAE,CAAEwC,OAAO,CAAEtK,eAAe,CAAG,GAAG,CAAG,CAAE,CAAE,CAC9C,CAAC,CACS,CAAC,CAER,CAAC,CACE,CAAC,cACVZ,IAAA,CAACzB,OAAO,EAACuM,KAAK,CAAEhK,SAAS,CAAC,gBAAgB,CAAE,CAACiK,KAAK,MAAA3B,QAAA,cAElDpJ,IAAA,CAAChC,GAAG,EAACmN,SAAS,CAAC,kBAAkB,CAAA/B,QAAA,cAChCpJ,IAAA,CAAC7B,UAAU,EACV2L,OAAO,CAAEnB,mBAAoB,CAC7BsD,IAAI,CAAC,OAAO,CAAA7C,QAAA,cAEZpJ,IAAA,SAAMqK,uBAAuB,CAAE,CAAEC,MAAM,CAAEnL,UAAW,CAAE,CAACuJ,KAAK,CAAE,CAAExD,SAAS,CAAE,MAAO,CAAE,CAAC,CAAC,CAC3E,CAAC,CACR,CAAC,CACG,CAAC,EACP,CAAC,CACE,CAAC,CACP,IAAI,CAEP/B,WAAW,eACVnD,IAAA,CAACF,0BAA0B,EAAC0C,MAAM,CAAEW,WAAY,CAACiK,gBAAgB,CAAEA,CAAA,GAAMhK,YAAY,CAAC,KAAK,CAAE,CAACiK,aAAa,CAAExG,wBAAyB,CAACvD,eAAe,CAAEA,eAAgB,CAACD,YAAY,CAAEA,YAAa,CAAC4D,kBAAkB,CAAEA,kBAAmB,CAACtD,cAAc,CAAEA,cAAe,CAAC,CAC7Q,cAEF3D,IAAA,CAAC9B,OAAO,EACPsJ,IAAI,CAAEC,eAAgB,CACtBqE,QAAQ,CAAEnJ,mBAAoB,CAC9B8I,OAAO,CAAE1C,sBAAuB,CAChC2C,YAAY,CAAE,CACbC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,QACb,CAAE,CACFG,eAAe,CAAE,CAChBJ,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,QACb,CAAE,CAAAxC,QAAA,cAEFlJ,KAAA,CAAClC,GAAG,EAAAoL,QAAA,eACHpJ,IAAA,CAACH,YAAY,EACZoJ,KAAK,CAAEV,qBAAsB,CAC7BoC,QAAQ,CAAE3B,iBAAkB,CAC5B,CAAC,cACAhJ,IAAA,UAAAoJ,QAAA,CACF;AACL;AACA;AACA;AACA,KAAK,CACI,CAAC,EACD,CAAC,CACE,CAAC,EACT,CAAC,CAEL,CAAC,CAED,cAAe,CAAA/I,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}