{"ast": null, "code": "import React,{useState}from'react';import{Select,MenuItem,FormControl,Box,Typography,Tooltip,IconButton}from'@mui/material';import{Language as LanguageIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import useInfoStore from'../../store/UserInfoStore';import{useTranslationContext}from'../../contexts/TranslationContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LANGUAGE_STORAGE_KEY_PREFIX='quickadapt_language_';function getOrgLanguageKey(orgId){return`${LANGUAGE_STORAGE_KEY_PREFIX}${orgId||'default'}`;}const LanguageSelector=_ref=>{var _sortedLanguages$;let{variant='select',size='small',showLabel=false,className=''}=_ref;const{t:translate}=useTranslation();const{availableLanguages,currentLanguage,changeLanguage,isLoading,isInitialized}=useTranslationContext();const[anchorEl,setAnchorEl]=useState(null);const[localLoading,setLocalLoading]=useState(false);const orgId=useInfoStore(state=>{var _state$orgDetails;return(_state$orgDetails=state.orgDetails)===null||_state$orgDetails===void 0?void 0:_state$orgDetails.OrganizationId;});// Don't render if not initialized or no languages available\nif(!isInitialized||availableLanguages.length===0){return null;}// Sort languages alphabetically by their display name\nconst sortedLanguages=[...availableLanguages].sort((a,b)=>a.Language.localeCompare(b.Language));// Ensure we have a valid current language\nconst validCurrentLanguage=sortedLanguages.find(lang=>lang.LanguageCode.toLowerCase()===currentLanguage.toLowerCase())?currentLanguage:((_sortedLanguages$=sortedLanguages[0])===null||_sortedLanguages$===void 0?void 0:_sortedLanguages$.LanguageCode)||'en';const handleLanguageChange=async event=>{const newLanguageCode=event.target.value;if(newLanguageCode===validCurrentLanguage)return;setLocalLoading(true);try{await changeLanguage(newLanguageCode);// Language saving is now handled in the i18n module\n}catch(error){console.error('Language change failed:',error);}finally{setLocalLoading(false);}};const handleIconClick=event=>{setAnchorEl(event.currentTarget);};const handleClose=()=>{setAnchorEl(null);};const handleMenuItemClick=async languageCode=>{if(languageCode===validCurrentLanguage){handleClose();return;}setLocalLoading(true);try{await changeLanguage(languageCode);// Language saving is now handled in the i18n module\n}catch(error){console.error('Language change failed:',error);}finally{setLocalLoading(false);handleClose();}};if(variant==='icon'){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate('Change Language'),children:/*#__PURE__*/_jsx(IconButton,{onClick:handleIconClick,size:size,className:className,disabled:isLoading||localLoading,children:/*#__PURE__*/_jsx(LanguageIcon,{sx:{height:\"22px\",width:\"22px\"}})})}),/*#__PURE__*/_jsx(Select,{open:Boolean(anchorEl),onClose:handleClose,value:validCurrentLanguage,MenuProps:{anchorEl,open:Boolean(anchorEl),onClose:handleClose,anchorOrigin:{vertical:'bottom',horizontal:'right'},transformOrigin:{vertical:'top',horizontal:'right'}},sx:{display:'none'},children:sortedLanguages.map(lang=>/*#__PURE__*/_jsx(MenuItem,{value:lang.LanguageCode,onClick:()=>handleMenuItemClick(lang.LanguageCode),selected:lang.LanguageCode===validCurrentLanguage,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:lang.Language}),lang.LanguageCode===validCurrentLanguage&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"primary\",children:\"(Current)\"})]})},lang.LanguageId))})]});}return/*#__PURE__*/_jsxs(FormControl,{size:size,className:className,children:[showLabel&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{mb:0.5},children:translate('Language')}),/*#__PURE__*/_jsx(Select,{value:validCurrentLanguage,onChange:handleLanguageChange,disabled:isLoading||localLoading,sx:{minWidth:120,'& .MuiSelect-select':{display:'flex',alignItems:'center',gap:1}},children:sortedLanguages.map(lang=>/*#__PURE__*/_jsx(MenuItem,{value:lang.LanguageCode,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",gap:1,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:lang.Language}),lang.LanguageCode===validCurrentLanguage&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"primary\",children:\"(Current)\"})]})},lang.LanguageId))})]});};export default LanguageSelector;", "map": {"version": 3, "names": ["React", "useState", "Select", "MenuItem", "FormControl", "Box", "Typography", "<PERSON><PERSON><PERSON>", "IconButton", "Language", "LanguageIcon", "useTranslation", "useInfoStore", "useTranslationContext", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "LANGUAGE_STORAGE_KEY_PREFIX", "getOrgLanguageKey", "orgId", "LanguageSelector", "_ref", "_sortedLanguages$", "variant", "size", "showLabel", "className", "t", "translate", "availableLanguages", "currentLanguage", "changeLanguage", "isLoading", "isInitialized", "anchorEl", "setAnchorEl", "localLoading", "setLocal<PERSON>oading", "state", "_state$orgDetails", "orgDetails", "OrganizationId", "length", "sortedLanguages", "sort", "a", "b", "localeCompare", "validCurrentLanguage", "find", "lang", "LanguageCode", "toLowerCase", "handleLanguageChange", "event", "newLanguageCode", "target", "value", "error", "console", "handleIconClick", "currentTarget", "handleClose", "handleMenuItemClick", "languageCode", "children", "arrow", "title", "onClick", "disabled", "sx", "height", "width", "open", "Boolean", "onClose", "MenuProps", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "display", "map", "selected", "alignItems", "gap", "color", "LanguageId", "mb", "onChange", "min<PERSON><PERSON><PERSON>"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/LanguageSelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  Box,\r\n  Typography,\r\n  SelectChangeEvent,\r\n  Tooltip,\r\n  IconButton,\r\n} from '@mui/material';\r\nimport { Language as LanguageIcon } from '@mui/icons-material';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useInfoStore from '../../store/UserInfoStore';\r\nimport { useTranslationContext } from '../../contexts/TranslationContext';\r\n\r\ninterface LanguageSelectorProps {\r\n  variant?: 'select' | 'icon';\r\n  size?: 'small' | 'medium';\r\n  showLabel?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst LANGUAGE_STORAGE_KEY_PREFIX = 'quickadapt_language_';\r\n\r\nfunction getOrgLanguageKey(orgId: string | undefined) {\r\n  return `${LANGUAGE_STORAGE_KEY_PREFIX}${orgId || 'default'}`;\r\n}\r\n\r\nconst LanguageSelector: React.FC<LanguageSelectorProps> = ({\r\n  variant = 'select',\r\n  size = 'small',\r\n  showLabel = false,\r\n  className = '',\r\n}) => {\r\n  const { t: translate } = useTranslation();\r\n  const { availableLanguages, currentLanguage, changeLanguage, isLoading, isInitialized } = useTranslationContext();\r\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId);\r\n\r\n  // Don't render if not initialized or no languages available\r\n  if (!isInitialized || availableLanguages.length === 0) {\r\n    return null;\r\n  }\r\n\r\n\r\n  // Sort languages alphabetically by their display name\r\n  const sortedLanguages = [...availableLanguages].sort((a, b) =>\r\n    a.Language.localeCompare(b.Language)\r\n  );\r\n\r\n  // Ensure we have a valid current language\r\n  const validCurrentLanguage = sortedLanguages.find(\r\n    lang => lang.LanguageCode.toLowerCase() === currentLanguage.toLowerCase()\r\n  ) ? currentLanguage : sortedLanguages[0]?.LanguageCode || 'en';\r\n\r\n  const handleLanguageChange = async (event: SelectChangeEvent<string>) => {\r\n    const newLanguageCode = event.target.value;\r\n    if (newLanguageCode === validCurrentLanguage) return;\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await changeLanguage(newLanguageCode);\r\n      // Language saving is now handled in the i18n module\r\n    } catch (error) {\r\n      console.error('Language change failed:', error);\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const handleMenuItemClick = async (languageCode: string) => {\r\n    if (languageCode === validCurrentLanguage) {\r\n      handleClose();\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await changeLanguage(languageCode);\r\n      // Language saving is now handled in the i18n module\r\n    } catch (error) {\r\n      console.error('Language change failed:', error);\r\n    } finally {\r\n      setLocalLoading(false);\r\n      handleClose();\r\n    }\r\n  };\r\n\r\n  if (variant === 'icon') {\r\n    return (\r\n      <>\r\n        <Tooltip arrow title={translate('Change Language')}>\r\n          <IconButton\r\n            onClick={handleIconClick}\r\n            size={size}\r\n            className={className}\r\n            disabled={isLoading || localLoading}\r\n          >\r\n            <LanguageIcon sx={{height :\"22px\" , width : \"22px\"}} />\r\n          </IconButton>\r\n        </Tooltip>\r\n        <Select\r\n          open={Boolean(anchorEl)}\r\n          onClose={handleClose}\r\n          value={validCurrentLanguage}\r\n          MenuProps={{\r\n            anchorEl,\r\n            open: Boolean(anchorEl),\r\n            onClose: handleClose,\r\n            anchorOrigin: { vertical: 'bottom', horizontal: 'right' },\r\n            transformOrigin: { vertical: 'top', horizontal: 'right' },\r\n          }}\r\n          sx={{ display: 'none' }}\r\n        >\r\n          {sortedLanguages.map((lang) => (\r\n            <MenuItem\r\n              key={lang.LanguageId}\r\n              value={lang.LanguageCode}\r\n              onClick={() => handleMenuItemClick(lang.LanguageCode)}\r\n              selected={lang.LanguageCode === validCurrentLanguage}\r\n            >\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Typography variant=\"body2\">{lang.Language}</Typography>\r\n                {lang.LanguageCode === validCurrentLanguage && (\r\n                  <Typography variant=\"caption\" color=\"primary\">\r\n                    (Current)\r\n                  </Typography>\r\n                )}\r\n              </Box>\r\n            </MenuItem>\r\n          ))}\r\n        </Select>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <FormControl size={size} className={className}>\r\n      {showLabel && (\r\n        <Typography variant=\"caption\" sx={{ mb: 0.5 }}>\r\n          {translate('Language')}\r\n        </Typography>\r\n      )}\r\n      <Select\r\n        value={validCurrentLanguage}\r\n        onChange={handleLanguageChange}\r\n        disabled={isLoading || localLoading}\r\n        sx={{\r\n          minWidth: 120,\r\n          '& .MuiSelect-select': {\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 1,\r\n          },\r\n        }}\r\n      >\r\n        {sortedLanguages.map((lang) => (\r\n          <MenuItem key={lang.LanguageId} value={lang.LanguageCode}>\r\n            <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n              <Typography variant=\"body2\">{lang.Language}</Typography>\r\n              {lang.LanguageCode === validCurrentLanguage && (\r\n                <Typography variant=\"caption\" color=\"primary\">\r\n                  (Current)\r\n                </Typography>\r\n              )}\r\n            </Box>\r\n          </MenuItem>\r\n        ))}\r\n      </Select>\r\n    </FormControl>\r\n  );\r\n};\r\n\r\nexport default LanguageSelector;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,GAAG,CACHC,UAAU,CAEVC,OAAO,CACPC,UAAU,KACL,eAAe,CACtB,OAASC,QAAQ,GAAI,CAAAC,YAAY,KAAQ,qBAAqB,CAC9D,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,OAASC,qBAAqB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAS1E,KAAM,CAAAC,2BAA2B,CAAG,sBAAsB,CAE1D,QAAS,CAAAC,iBAAiBA,CAACC,KAAyB,CAAE,CACpD,MAAO,GAAGF,2BAA2B,GAAGE,KAAK,EAAI,SAAS,EAAE,CAC9D,CAEA,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAKpD,KAAAC,iBAAA,IALqD,CACzDC,OAAO,CAAG,QAAQ,CAClBC,IAAI,CAAG,OAAO,CACdC,SAAS,CAAG,KAAK,CACjBC,SAAS,CAAG,EACd,CAAC,CAAAL,IAAA,CACC,KAAM,CAAEM,CAAC,CAAEC,SAAU,CAAC,CAAGpB,cAAc,CAAC,CAAC,CACzC,KAAM,CAAEqB,kBAAkB,CAAEC,eAAe,CAAEC,cAAc,CAAEC,SAAS,CAAEC,aAAc,CAAC,CAAGvB,qBAAqB,CAAC,CAAC,CACjH,KAAM,CAACwB,QAAQ,CAAEC,WAAW,CAAC,CAAGrC,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAEvD,KAAM,CAAAqB,KAAK,CAAGV,YAAY,CAAE6B,KAAK,OAAAC,iBAAA,QAAAA,iBAAA,CAAKD,KAAK,CAACE,UAAU,UAAAD,iBAAA,iBAAhBA,iBAAA,CAAkBE,cAAc,GAAC,CAEvE;AACA,GAAI,CAACR,aAAa,EAAIJ,kBAAkB,CAACa,MAAM,GAAK,CAAC,CAAE,CACrD,MAAO,KAAI,CACb,CAGA;AACA,KAAM,CAAAC,eAAe,CAAG,CAAC,GAAGd,kBAAkB,CAAC,CAACe,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GACxDD,CAAC,CAACvC,QAAQ,CAACyC,aAAa,CAACD,CAAC,CAACxC,QAAQ,CACrC,CAAC,CAED;AACA,KAAM,CAAA0C,oBAAoB,CAAGL,eAAe,CAACM,IAAI,CAC/CC,IAAI,EAAIA,IAAI,CAACC,YAAY,CAACC,WAAW,CAAC,CAAC,GAAKtB,eAAe,CAACsB,WAAW,CAAC,CAC1E,CAAC,CAAGtB,eAAe,CAAG,EAAAR,iBAAA,CAAAqB,eAAe,CAAC,CAAC,CAAC,UAAArB,iBAAA,iBAAlBA,iBAAA,CAAoB6B,YAAY,GAAI,IAAI,CAE9D,KAAM,CAAAE,oBAAoB,CAAG,KAAO,CAAAC,KAAgC,EAAK,CACvE,KAAM,CAAAC,eAAe,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAC1C,GAAIF,eAAe,GAAKP,oBAAoB,CAAE,OAE9CX,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAAN,cAAc,CAACwB,eAAe,CAAC,CACrC;AACF,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,OAAS,CACRrB,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAuB,eAAe,CAAIN,KAAoC,EAAK,CAChEnB,WAAW,CAACmB,KAAK,CAACO,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB3B,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAG,KAAO,CAAAC,YAAoB,EAAK,CAC1D,GAAIA,YAAY,GAAKhB,oBAAoB,CAAE,CACzCc,WAAW,CAAC,CAAC,CACb,OACF,CAEAzB,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAAN,cAAc,CAACiC,YAAY,CAAC,CAClC;AACF,CAAE,MAAON,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CAAC,OAAS,CACRrB,eAAe,CAAC,KAAK,CAAC,CACtByB,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CAED,GAAIvC,OAAO,GAAK,MAAM,CAAE,CACtB,mBACET,KAAA,CAAAE,SAAA,EAAAiD,QAAA,eACErD,IAAA,CAACR,OAAO,EAAC8D,KAAK,MAACC,KAAK,CAAEvC,SAAS,CAAC,iBAAiB,CAAE,CAAAqC,QAAA,cACjDrD,IAAA,CAACP,UAAU,EACT+D,OAAO,CAAER,eAAgB,CACzBpC,IAAI,CAAEA,IAAK,CACXE,SAAS,CAAEA,SAAU,CACrB2C,QAAQ,CAAErC,SAAS,EAAII,YAAa,CAAA6B,QAAA,cAEpCrD,IAAA,CAACL,YAAY,EAAC+D,EAAE,CAAE,CAACC,MAAM,CAAE,MAAM,CAAGC,KAAK,CAAG,MAAM,CAAE,CAAE,CAAC,CAC7C,CAAC,CACN,CAAC,cACV5D,IAAA,CAACb,MAAM,EACL0E,IAAI,CAAEC,OAAO,CAACxC,QAAQ,CAAE,CACxByC,OAAO,CAAEb,WAAY,CACrBL,KAAK,CAAET,oBAAqB,CAC5B4B,SAAS,CAAE,CACT1C,QAAQ,CACRuC,IAAI,CAAEC,OAAO,CAACxC,QAAQ,CAAC,CACvByC,OAAO,CAAEb,WAAW,CACpBe,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAC1D,CAAE,CACFT,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAEvBtB,eAAe,CAACuC,GAAG,CAAEhC,IAAI,eACxBtC,IAAA,CAACZ,QAAQ,EAEPyD,KAAK,CAAEP,IAAI,CAACC,YAAa,CACzBiB,OAAO,CAAEA,CAAA,GAAML,mBAAmB,CAACb,IAAI,CAACC,YAAY,CAAE,CACtDgC,QAAQ,CAAEjC,IAAI,CAACC,YAAY,GAAKH,oBAAqB,CAAAiB,QAAA,cAErDnD,KAAA,CAACZ,GAAG,EAAC+E,OAAO,CAAC,MAAM,CAACG,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAApB,QAAA,eAC7CrD,IAAA,CAACT,UAAU,EAACoB,OAAO,CAAC,OAAO,CAAA0C,QAAA,CAAEf,IAAI,CAAC5C,QAAQ,CAAa,CAAC,CACvD4C,IAAI,CAACC,YAAY,GAAKH,oBAAoB,eACzCpC,IAAA,CAACT,UAAU,EAACoB,OAAO,CAAC,SAAS,CAAC+D,KAAK,CAAC,SAAS,CAAArB,QAAA,CAAC,WAE9C,CAAY,CACb,EACE,CAAC,EAZDf,IAAI,CAACqC,UAaF,CACX,CAAC,CACI,CAAC,EACT,CAAC,CAEP,CAEA,mBACEzE,KAAA,CAACb,WAAW,EAACuB,IAAI,CAAEA,IAAK,CAACE,SAAS,CAAEA,SAAU,CAAAuC,QAAA,EAC3CxC,SAAS,eACRb,IAAA,CAACT,UAAU,EAACoB,OAAO,CAAC,SAAS,CAAC+C,EAAE,CAAE,CAAEkB,EAAE,CAAE,GAAI,CAAE,CAAAvB,QAAA,CAC3CrC,SAAS,CAAC,UAAU,CAAC,CACZ,CACb,cACDhB,IAAA,CAACb,MAAM,EACL0D,KAAK,CAAET,oBAAqB,CAC5ByC,QAAQ,CAAEpC,oBAAqB,CAC/BgB,QAAQ,CAAErC,SAAS,EAAII,YAAa,CACpCkC,EAAE,CAAE,CACFoB,QAAQ,CAAE,GAAG,CACb,qBAAqB,CAAE,CACrBT,OAAO,CAAE,MAAM,CACfG,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CACF,CAAE,CAAApB,QAAA,CAEDtB,eAAe,CAACuC,GAAG,CAAEhC,IAAI,eACxBtC,IAAA,CAACZ,QAAQ,EAAuByD,KAAK,CAAEP,IAAI,CAACC,YAAa,CAAAc,QAAA,cACvDnD,KAAA,CAACZ,GAAG,EAAC+E,OAAO,CAAC,MAAM,CAACG,UAAU,CAAC,QAAQ,CAACC,GAAG,CAAE,CAAE,CAAApB,QAAA,eAC7CrD,IAAA,CAACT,UAAU,EAACoB,OAAO,CAAC,OAAO,CAAA0C,QAAA,CAAEf,IAAI,CAAC5C,QAAQ,CAAa,CAAC,CACvD4C,IAAI,CAACC,YAAY,GAAKH,oBAAoB,eACzCpC,IAAA,CAACT,UAAU,EAACoB,OAAO,CAAC,SAAS,CAAC+D,KAAK,CAAC,SAAS,CAAArB,QAAA,CAAC,WAE9C,CAAY,CACb,EACE,CAAC,EAROf,IAAI,CAACqC,UASV,CACX,CAAC,CACI,CAAC,EACE,CAAC,CAElB,CAAC,CAED,cAAe,CAAAnE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}