{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{<PERSON>,Typo<PERSON>,<PERSON><PERSON>ield,IconButton,Button,Tooltip}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";import{chkicn1,chkicn2,chkicn3,chkicn4,chkicn5,chkicn6,launlftun,launrgtse,launlftse,launrgtun}from\"../../assets/icons/icons\";import ArrowBackIosNewOutlinedIcon from\"@mui/icons-material/ArrowBackIosNewOutlined\";import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LauncherSettings=_ref=>{var _checklistLauncherPro,_checklistLauncherPro2;let{currentGuide}=_ref;const{t:translate}=useTranslation();const{titlePopup,setTitlePopup,setDesignPopup,titleColor,setTitleColor,launcherColor,setLauncherColor,iconColor,setIconColor,setShowLauncherSettings,checklistGuideMetaData,updateChecklistLauncher,setIsUnSavedChanges}=useDrawerStore(state=>state);const encodeToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};const[icons,setIcons]=useState(()=>{return[{id:1,base64:encodeToBase64(chkicn1),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn1},style:{zoom:1,display:\"flex\"}}),selected:false},{id:2,base64:encodeToBase64(chkicn2),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn2},style:{zoom:1,display:\"flex\"}}),selected:false},{id:3,base64:encodeToBase64(chkicn3),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn3},style:{zoom:1,display:\"flex\"}}),selected:false},{id:4,base64:encodeToBase64(chkicn4),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn4},style:{zoom:1,display:\"flex\"}}),selected:false},{id:5,base64:encodeToBase64(chkicn5),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn5},style:{zoom:1,display:\"flex\"}}),selected:false},{id:6,base64:encodeToBase64(chkicn6),component:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:chkicn6},style:{zoom:1,display:\"flex\"}}),selected:false}];});const[checklistLauncherProperties,setChecklistLauncherProperties]=useState(()=>{var _checklistGuideMetaDa;const initialchecklistLauncherProperties=((_checklistGuideMetaDa=checklistGuideMetaData[0])===null||_checklistGuideMetaDa===void 0?void 0:_checklistGuideMetaDa.launcher)||{type:\"Icon\",icon:\"\",text:\"Get Started\",iconColor:\"#fff\",textColor:\"#fff\",launcherColor:\"var(--primarycolor)\",launcherposition:{left:false,right:true,xaxisOffset:\"10\",yaxisOffset:\"10\"},notificationBadge:false,notificationBadgeColor:\"red\",notificationTextColor:\"#fff\"};return initialchecklistLauncherProperties;});// State for tracking changes and apply button\nconst[isDisabled,setIsDisabled]=useState(true);const[hasChanges,setHasChanges]=useState(false);const[initialState,setInitialState]=useState(()=>{var _checklistGuideMetaDa2;// Use the actual data from store if available, otherwise use the default\nreturn((_checklistGuideMetaDa2=checklistGuideMetaData[0])===null||_checklistGuideMetaDa2===void 0?void 0:_checklistGuideMetaDa2.launcher)||checklistLauncherProperties;});// Sync local state with store data only when component mounts (not on every store change)\nuseEffect(()=>{var _checklistGuideMetaDa3;if((_checklistGuideMetaDa3=checklistGuideMetaData[0])!==null&&_checklistGuideMetaDa3!==void 0&&_checklistGuideMetaDa3.launcher){const newLauncher=checklistGuideMetaData[0].launcher;setChecklistLauncherProperties(newLauncher);setInitialState(newLauncher);setHasChanges(false);setIsDisabled(true);// Clear any validation errors on mount\nsetTextError(\"\");}else{// If no launcher data exists, ensure default icon is set in initialState\nconst defaultIcon=icons[0];if(defaultIcon){var _defaultIcon$componen;const svgElement=(_defaultIcon$componen=defaultIcon.component.props.dangerouslySetInnerHTML)===null||_defaultIcon$componen===void 0?void 0:_defaultIcon$componen.__html;if(svgElement){const base64Icon=encodeToBase64(svgElement);const defaultProperties={...checklistLauncherProperties,icon:base64Icon};setInitialState(defaultProperties);}}}},[]);// Empty dependency array - only run on mount\n// State for tracking validation errors\nconst[textError,setTextError]=useState(\"\");// Function to check if the Apply button should be enabled\nconst updateApplyButtonState=function(changed){let hasErrors=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;setIsDisabled(!changed||hasErrors);};// Effect to check for any changes compared to initial state\nuseEffect(()=>{// Compare current properties with initial state\nconst hasAnyChanges=JSON.stringify(checklistLauncherProperties)!==JSON.stringify(initialState);setHasChanges(hasAnyChanges);// Check for validation errors\nconst hasValidationErrors=!!textError;updateApplyButtonState(hasAnyChanges,hasValidationErrors);},[checklistLauncherProperties,initialState,textError]);const handleTitleColorChange=e=>setTitleColor(e.target.value);useEffect(()=>{if(checklistLauncherProperties.icon){setIcons(prevIcons=>prevIcons.map(icon=>({...icon,selected:icon.base64===checklistLauncherProperties.icon// Compare Base64 strings directly\n})));}},[checklistLauncherProperties.icon]);const handleClose=()=>{setShowLauncherSettings(false);};const handledesignclose=()=>{setDesignPopup(false);};const handleSizeChange=value=>{const sizeInPx=16+(value-1)*4;onPropertyChange(\"Size\",sizeInPx);};const onReselectElement=()=>{};const onPropertyChange=(key,value)=>{// Validate text input\nif(key===\"text\"){let errorMessage=\"\";if(value.length<2){errorMessage=\"Min: 2 Characters\";}else if(value.length>20){errorMessage=\"Max: 20 Characters\";}setTextError(errorMessage);}setChecklistLauncherProperties(prevState=>{let newState;// Handle nested launcherposition properties\nif(key===\"xaxisOffset\"||key===\"yaxisOffset\"){newState={...prevState,launcherposition:{...prevState.launcherposition,[key]:value}};}else{// Handle other properties normally\nnewState={...prevState,[key]:value};}// Mark that changes have been made\nsetHasChanges(true);return newState;});};const handleIconColorChange=e=>{setIconColor(e.target.value);};const handleLauncherColorChange=e=>{setLauncherColor(e.target.value);};const handleApplyChanges=()=>{updateChecklistLauncher(checklistLauncherProperties);// Update the initial state to the current state after applying changes\nsetInitialState({...checklistLauncherProperties});// Reset the changes flag\nsetHasChanges(false);// Disable the Apply button\nsetIsDisabled(true);setIsUnSavedChanges(true);handleClose();};const[type,setType]=useState('Text');const[text,setText]=useState('Get Started');const[textColor,setTextColor]=useState('#ffffff');const handleTypeChange=newType=>{setType(newType);onPropertyChange(\"type\",newType);// // Reset icon selection when type changes\n// setIcons(prevIcons => prevIcons.map(icon => ({ ...icon, selected: false })));\n// // Also reset the selected icon in checklistLauncherProperties\n// setChecklistLauncherProperties((prev:any) => ({\n// \t...prev,\n// \ticon: null, // Clear the selected icon\n// }));\n};const[error,setError]=useState(null);const[icon,setIcon]=useState();// Helper function to convert SVG to Base64\nconst svgToBase64=svgString=>{return`data:image/svg+xml;base64,${btoa(svgString)}`;};useEffect(()=>{const defaultIcon=icons[0];if(defaultIcon&&!checklistLauncherProperties.icon){var _defaultIcon$componen2;const svgElement=(_defaultIcon$componen2=defaultIcon.component.props.dangerouslySetInnerHTML)===null||_defaultIcon$componen2===void 0?void 0:_defaultIcon$componen2.__html;if(svgElement){const base64Icon=encodeToBase64(svgElement);const appliedIconColorBase64Icon=modifySVGColor(base64Icon,checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.iconColor);setIcon(base64Icon);// Create the updated properties with default icon\nconst updatedProperties={...checklistLauncherProperties,icon:base64Icon};// Update the state\nsetChecklistLauncherProperties(updatedProperties);// IMPORTANT: Also update the initialState to include the default icon\n// This prevents the change detection from thinking there's a user change\nsetInitialState(updatedProperties);// Update the store\nupdateChecklistLauncher(updatedProperties);}}},[]);const modifySVGColor=(base64SVG,color)=>{if(!base64SVG){return\"\";}try{// Check if the string is a valid base64 SVG\nif(!base64SVG.includes(\"data:image/svg+xml;base64,\")){return base64SVG;// Return the original if it's not an SVG\n}const decodedSVG=atob(base64SVG.split(\",\")[1]);// Check if this is primarily a stroke-based or fill-based icon\nconst hasStroke=decodedSVG.includes('stroke=\"');const hasColoredFill=/fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);let modifiedSVG=decodedSVG;if(hasStroke&&!hasColoredFill){// This is a stroke-based icon (like chkicn2-6) - only change stroke color\nmodifiedSVG=modifiedSVG.replace(/stroke=\"[^\"]+\"/g,`stroke=\"${color}\"`);}else if(hasColoredFill){// This is a fill-based icon (like chkicn1) - only change fill color\nmodifiedSVG=modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g,`fill=\"${color}\"`);}else{// No existing fill or stroke, add fill to make it visible\nmodifiedSVG=modifiedSVG.replace(/<path(?![^>]*fill=)/g,`<path fill=\"${color}\"`);modifiedSVG=modifiedSVG.replace(/<svg(?![^>]*fill=)/g,`<svg fill=\"${color}\"`);}const modifiedBase64=`data:image/svg+xml;base64,${btoa(modifiedSVG)}`;return modifiedBase64;}catch(error){// console.error(\"Error modifying SVG color:\", error);\nreturn base64SVG;// Return the original if there's an error\n}};const handleIconClick=async id=>{setIcons(prevIcons=>prevIcons.map(icon=>({...icon,selected:icon.id===id})));const selectedIcon=icons.find(icon=>icon.id===id);if(selectedIcon){var _selectedIcon$compone;const svgElement=(_selectedIcon$compone=selectedIcon.component.props.dangerouslySetInnerHTML)===null||_selectedIcon$compone===void 0?void 0:_selectedIcon$compone.__html;if(svgElement){const base64Icon=svgToBase64(svgElement);const appliedIconColorBase64Icon=modifySVGColor(base64Icon,checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.iconColor);setIcon(base64Icon);setChecklistLauncherProperties(prevState=>({...prevState,// Copy previous state\nicon:base64Icon// Update icon property\n}));setHasChanges(true);}}};const[setPositionLeft,setSetPositionLeft]=useState(false);const[launlft,setLaunlft]=useState(launlftun);const[launrgt,setLaunrgt]=useState(launrgtse);const handleLauncherLeft=()=>{setChecklistLauncherProperties(prev=>({...prev,launcherposition:{...prev.launcherposition,left:true,right:false}}));setHasChanges(true);setLaunlft(launlftse);setLaunrgt(launrgtun);};useEffect(()=>{const position=checklistLauncherProperties.launcherposition;if(position.left){setLaunlft(launlftse);setLaunrgt(launrgtun);}else if(position.right){setLaunlft(launlftun);setLaunrgt(launrgtse);}},[checklistLauncherProperties.launcherposition]);const handleLauncherRight=()=>{setChecklistLauncherProperties(prev=>({...prev,launcherposition:{...prev.launcherposition,left:false,right:true}}));setHasChanges(true);setLaunlft(launlftun);setLaunrgt(launrgtse);};return/*#__PURE__*/_jsx(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(IconButton,{\"aria-label\":translate('back',{defaultValue:'back'}),onClick:handleClose,children:/*#__PURE__*/_jsx(ArrowBackIosNewOutlinedIcon,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate('Launcher',{defaultValue:'Launcher'})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate('close',{defaultValue:'close'}),onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-canblock\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-control-box\",style:{height:\"auto\",flexDirection:\"column\",padding:\"0\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Type',{defaultValue:'Type'})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"inline-block\",gap:\"5px\",padding:\"0 8px 8px 8px\",textAlign:\"left\"},children:[/*#__PURE__*/_jsx(\"button\",{className:`qadpt-type-option ${checklistLauncherProperties.type==='Icon'?'selected':''}`,onClick:()=>handleTypeChange('Icon'),children:translate('Icon',{defaultValue:'Icon'})}),/*#__PURE__*/_jsx(\"button\",{className:`qadpt-type-option ${checklistLauncherProperties.type==='Text'?'selected':''}`,onClick:()=>handleTypeChange('Text'),children:translate('Text',{defaultValue:'Text'})}),/*#__PURE__*/_jsx(\"button\",{className:`qadpt-type-option ${checklistLauncherProperties.type==='Icon+Txt'?'selected':''}`,onClick:()=>handleTypeChange('Icon+Txt'),children:translate('Icon+Text',{defaultValue:'Icon+Text'})})]})]}),checklistLauncherProperties.type===\"Text\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Text',{defaultValue:'Text'})}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate('Step Title',{defaultValue:'Step Title'}),className:\"qadpt-control-input\",style:{width:\"calc(100% - 13px)\",padding:\"0 8px 8px 8px\",margin:\"0\"},value:checklistLauncherProperties.text,onChange:e=>onPropertyChange(\"text\",e.target.value),error:Boolean(textError),helperText:textError,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate('Text Color',{defaultValue:'Text Color'})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.textColor,onChange:e=>onPropertyChange(\"textColor\",e.target.value),className:\"qadpt-color-input\"})]})]}),checklistLauncherProperties.type===\"Icon\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\",padding:\"0 !important\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate('Icon',{defaultValue:'Icon'})}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",gap:1,alignItems:\"center\",width:\"-webkit-fill-available\",flexWrap:\"wrap\",padding:\"0 8px 8px 8px\"},children:icons.map(icon=>/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate('Select Icon',{defaultValue:'Select Icon'}),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIconClick(icon.id),sx:{border:icon.selected?\"2px solid var(--primarycolor)\":\"none\",borderRadius:\"8px\",padding:\"8px\",background:\"#F1ECEC\"},children:icon.component})},icon.id))})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate('Icon Color',{defaultValue:'Icon Color'})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.iconColor,onChange:e=>onPropertyChange(\"iconColor\",e.target.value),className:\"qadpt-color-input\",style:{backgroundColor:'#fff'}})]})]}),checklistLauncherProperties.type===\"Icon+Txt\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Text',{defaultValue:'Text'})}),/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",size:\"small\",placeholder:translate('Step Title',{defaultValue:'Step Title'}),className:\"qadpt-control-input\",style:{width:\"calc(100% - 13px)\",padding:\"0 8px 8px 8px\",margin:\"0\"},value:checklistLauncherProperties.text,onChange:e=>onPropertyChange(\"text\",e.target.value),error:Boolean(textError),helperText:textError,InputProps:{endAdornment:\"\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"},\"& input\":{textAlign:\"left !important\",paddingLeft:\"10px !important\"},\"&.MuiInputBase-root\":{height:\"auto !important\"}}}})]}),/*#__PURE__*/_jsxs(Box,{id:\"qadpt-designpopup\",className:\"qadpt-control-box\",sx:{flexDirection:\"column\",height:\"auto !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Icon',{defaultValue:'Icon'})}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",gap:1,alignItems:\"center\",width:\"-webkit-fill-available\",flexWrap:\"wrap\",padding:\"0 8px 8px 8px\"},children:icons.map(icon=>/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:translate('Select Icon',{defaultValue:'Select Icon'}),children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleIconClick(icon.id),sx:{border:icon.selected?\"2px solid var(--primarycolor)\":\"none\",borderRadius:\"8px\",padding:\"8px\",background:\"#F1ECEC\"},children:icon.component})},icon.id))})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate('Icon Color',{defaultValue:'Icon Color'})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.iconColor,onChange:e=>onPropertyChange(\"iconColor\",e.target.value),className:\"qadpt-color-input\",style:{backgroundColor:'#fff'}})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",children:translate('Text Color',{defaultValue:'Text Color'})}),/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.textColor,onChange:e=>onPropertyChange(\"textColor\",e.target.value),className:\"qadpt-color-input\"})]})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Launcher Color',{defaultValue:'Launcher Color'})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.launcherColor,onChange:e=>onPropertyChange(\"launcherColor\",e.target.value),className:\"qadpt-color-input\",style:{backgroundColor:'#5F9EA0'}})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",sx:{height:\"auto !important\",flexDirection:\"column !important\",padding:\"0 !important\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Position',{defaultValue:'Position'})}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:\"0 8px 8px 8px\",display:\"flex\",gap:\"4px\",cursor:\"pointer\"},children:[/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:launlft},onClick:handleLauncherLeft,style:{zoom:\"0.95\"}}),/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:launrgt},onClick:handleLauncherRight,style:{zoom:\"0.95\"}})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",paddingRight:\"8px\"},children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-control-label\",style:{whiteSpace:\"normal\",wordBreak:\"break-word\"},children:translate('X Axis Offset',{defaultValue:'X Axis Offset'})}),/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-chkoffset\",children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:((_checklistLauncherPro=checklistLauncherProperties.launcherposition)===null||_checklistLauncherPro===void 0?void 0:_checklistLauncherPro.xaxisOffset)||\"10\",size:\"small\",className:\"qadpt-control-input\",onChange:e=>onPropertyChange(\"xaxisOffset\",e.target.value),InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}}})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",paddingRight:\"8px\"},children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",style:{whiteSpace:\"normal\",wordBreak:\"break-word\"},children:translate('Y Axis Offset',{defaultValue:'Y Axis Offset'})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-chkoffset\",children:/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",value:((_checklistLauncherPro2=checklistLauncherProperties.launcherposition)===null||_checklistLauncherPro2===void 0?void 0:_checklistLauncherPro2.yaxisOffset)||\"10\",size:\"small\",className:\"qadpt-control-input\",onChange:e=>onPropertyChange(\"yaxisOffset\",e.target.value),InputProps:{endAdornment:\"px\",sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"none\"},\"& fieldset\":{border:\"none\"}}}})})]})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Notification Badge',{defaultValue:'Notification Badge'})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"label\",{className:\"toggle-switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:checklistLauncherProperties.notificationBadge,onChange:e=>onPropertyChange(\"notificationBadge\",e.target.checked),name:\"showByDefault\"}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),checklistLauncherProperties.notificationBadge&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Badge Color',{defaultValue:'Badge Color'})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.notificationBadgeColor,onChange:e=>onPropertyChange(\"notificationBadgeColor\",e.target.value),className:\"qadpt-color-input\",style:{backgroundColor:'red'}})})]}),/*#__PURE__*/_jsxs(Box,{className:\"qadpt-control-box\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-control-label\",children:translate('Text Color',{defaultValue:'Text Color'})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:\"color\",value:checklistLauncherProperties===null||checklistLauncherProperties===void 0?void 0:checklistLauncherProperties.notificationTextColor,onChange:e=>onPropertyChange(\"notificationTextColor\",e.target.value),className:\"qadpt-color-input\"})})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-drawerFooter\",children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleApplyChanges,className:`qadpt-btn ${isDisabled?\"disabled\":\"\"}`,disabled:isDisabled,children:translate('Apply',{defaultValue:'Apply'})})})]})});};export default LauncherSettings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CloseIcon", "useDrawerStore", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "<PERSON><PERSON><PERSON><PERSON>", "laun<PERSON>tse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "la<PERSON><PERSON><PERSON>", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "LauncherSettings", "_ref", "_checklistLauncherPro", "_checklistLauncherPro2", "currentGuide", "t", "translate", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "launcherColor", "setLauncherColor", "iconColor", "setIconColor", "setShowLauncherSettings", "checklistGuideMetaData", "updateChecklistLauncher", "setIsUnSavedChanges", "state", "encodeToBase64", "svgString", "btoa", "icons", "setIcons", "id", "base64", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "selected", "checklistLauncherProperties", "setChecklistLauncherProperties", "_checklistGuideMetaDa", "initialchecklistLauncherProperties", "launcher", "type", "icon", "text", "textColor", "launcherposition", "left", "right", "xaxisOffset", "yaxisOffset", "notificationBadge", "notificationBadgeColor", "notificationTextColor", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "newLauncher", "setTextError", "defaultIcon", "_defaultIcon$componen", "svgElement", "props", "base64Icon", "defaultProperties", "textError", "updateApplyButtonState", "changed", "hasErrors", "arguments", "length", "undefined", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "handleTitleColorChange", "e", "target", "value", "prevIcons", "map", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "errorMessage", "prevState", "newState", "handleIconColorChange", "handleLauncherColorChange", "handleApplyChanges", "setType", "setText", "setTextColor", "handleTypeChange", "newType", "error", "setError", "setIcon", "svgToBase64", "_defaultIcon$componen2", "appliedIconColorBase64Icon", "modifySVGColor", "updatedProperties", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "handleIconClick", "selectedIcon", "find", "_selectedIcon$compone", "setPositionLeft", "setSetPositionLeft", "la<PERSON><PERSON><PERSON>", "setLaunlft", "la<PERSON><PERSON>t", "setLaunrgt", "handleLauncherLeft", "prev", "position", "handleLauncherRight", "className", "children", "defaultValue", "onClick", "size", "height", "flexDirection", "padding", "gap", "textAlign", "sx", "variant", "placeholder", "width", "margin", "onChange", "Boolean", "helperText", "InputProps", "endAdornment", "border", "paddingLeft", "alignItems", "flexWrap", "arrow", "title", "borderRadius", "background", "backgroundColor", "cursor", "justifyContent", "paddingRight", "whiteSpace", "wordBreak", "checked", "name", "disabled"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/checklist/LauncherSettings.tsx"], "sourcesContent": ["import React, { useReducer, useState, useEffect } from \"react\";\r\nimport { <PERSON>, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, ToggleButton, ToggleButtonGroup, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tInfoFilled,\r\n\tQuestionFill,\r\n\tReselect,\r\n\tediticon,\r\n\tSolid,\r\n\tlaunlftun,\r\n\tlaunrgtse,\r\n\tlaun<PERSON>tse,\r\n\tlaunrgtun,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\r\nimport { position } from \"jodit/esm/core/helpers\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\nconst LauncherSettings = ({ currentGuide }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\t\r\n\tconst {\r\n\r\n\t\ttitlePopup,\r\n\t\tsetTitlePopup,\r\n\t\tsetDesignPopup,\r\n\t\ttitleColor,\r\n\t\tsetTitleColor,\r\n\t\tlauncherColor,\r\n\t\tsetLauncherColor,\r\n\t\ticonColor,\r\n\t\tsetIconColor,\r\n\t\tsetShowLauncherSettings,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistLauncher,\r\n\t\tsetIsUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t  };\r\n\t  \r\n\t  const [icons, setIcons] = useState<any[]>(() => {\r\n\t\treturn [\r\n\t\t  { id: 1, base64: encodeToBase64(chkicn1), component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 2, base64: encodeToBase64(chkicn2), component: <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 3, base64: encodeToBase64(chkicn3), component: <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 4, base64: encodeToBase64(chkicn4), component: <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 5, base64: encodeToBase64(chkicn5), component: <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t  { id: 6, base64: encodeToBase64(chkicn6), component: <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1, display: \"flex\" }} />, selected: false },\r\n\t\t];\r\n\t  });\r\n\tconst [\r\n\t\tchecklistLauncherProperties, setChecklistLauncherProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistLauncherProperties = checklistGuideMetaData[0]?.launcher || {\r\n\t\t\ttype: \"Icon\",\r\n\t\t\ticon: \"\",\r\n\t\t\ttext: \"Get Started\",\r\n\t\t\ticonColor: \"#fff\",\r\n\t\t\ttextColor: \"#fff\",\r\n\t\t\tlauncherColor: \"var(--primarycolor)\",\r\n\t\t\tlauncherposition: {\r\n\t\t\t\tleft: false,\r\n\t\t\t\tright: true,\r\n\t\t\t\txaxisOffset: \"10\",\r\n\t\t\t\tyaxisOffset: \"10\",\r\n\t\t\t},\r\n\t\t\tnotificationBadge: false,\r\n\t\t\tnotificationBadgeColor: \"red\",\r\n\t\t\tnotificationTextColor: \"#fff\",\r\n\r\n\t\t};\r\n\t\treturn initialchecklistLauncherProperties;\r\n\t});\r\n\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(() => {\r\n\t\t// Use the actual data from store if available, otherwise use the default\r\n\t\treturn checklistGuideMetaData[0]?.launcher || checklistLauncherProperties;\r\n\t});\r\n\r\n\t// Sync local state with store data only when component mounts (not on every store change)\r\n\tuseEffect(() => {\r\n\t\tif (checklistGuideMetaData[0]?.launcher) {\r\n\t\t\tconst newLauncher = checklistGuideMetaData[0].launcher;\r\n\t\t\tsetChecklistLauncherProperties(newLauncher);\r\n\t\t\tsetInitialState(newLauncher);\r\n\t\t\tsetHasChanges(false);\r\n\t\t\tsetIsDisabled(true);\r\n\t\t\t// Clear any validation errors on mount\r\n\t\t\tsetTextError(\"\");\r\n\t\t} else {\r\n\t\t\t// If no launcher data exists, ensure default icon is set in initialState\r\n\t\t\tconst defaultIcon = icons[0];\r\n\t\t\tif (defaultIcon) {\r\n\t\t\t\tconst svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\t\t\tif (svgElement) {\r\n\t\t\t\t\tconst base64Icon = encodeToBase64(svgElement);\r\n\t\t\t\t\tconst defaultProperties = {\r\n\t\t\t\t\t\t...checklistLauncherProperties,\r\n\t\t\t\t\t\ticon: base64Icon\r\n\t\t\t\t\t};\r\n\t\t\t\t\tsetInitialState(defaultProperties);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}, []); // Empty dependency array - only run on mount\r\n\r\n\t// State for tracking validation errors\r\n\tconst [textError, setTextError] = useState(\"\");\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistLauncherProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = !!textError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistLauncherProperties, initialState, textError]);\r\n\tconst handleTitleColorChange = (e: any) => setTitleColor(e.target.value);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (checklistLauncherProperties.icon) {\r\n\t\t  setIcons((prevIcons) =>\r\n\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t  ...icon,\r\n\t\t\t  selected: icon.base64 === checklistLauncherProperties.icon, // Compare Base64 strings directly\r\n\t\t\t}))\r\n\t\t  );\r\n\t\t}\r\n\t}, [checklistLauncherProperties.icon]);\r\n\r\n\t\r\n\tconst handleClose = () => {\r\n\t\tsetShowLauncherSettings(false);\r\n\t};\r\n\tconst handledesignclose = () => {\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\tconst handleSizeChange = (value: number) => {\r\n\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t};\r\n\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t// Validate text input\r\n\t\tif (key === \"text\") {\r\n\t\t\tlet errorMessage = \"\";\r\n\t\t\tif (value.length < 2) {\r\n\t\t\t\terrorMessage = \"Min: 2 Characters\";\r\n\t\t\t} else if (value.length > 20) {\r\n\t\t\t\terrorMessage = \"Max: 20 Characters\";\r\n\t\t\t}\r\n\t\t\tsetTextError(errorMessage);\r\n\t\t}\r\n\r\n\r\n\t\tsetChecklistLauncherProperties((prevState: any) => {\r\n\t\t\tlet newState;\r\n\t\t\t// Handle nested launcherposition properties\r\n\t\t\tif (key === \"xaxisOffset\" || key === \"yaxisOffset\") {\r\n\t\t\t\tnewState = {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\tlauncherposition: {\r\n\t\t\t\t\t\t...prevState.launcherposition,\r\n\t\t\t\t\t\t[key]: value,\r\n\t\t\t\t\t},\r\n\t\t\t\t};\r\n\t\t\t} else {\r\n\t\t\t\t// Handle other properties normally\r\n\t\t\t\tnewState = {\r\n\t\t\t\t\t...prevState,\r\n\t\t\t\t\t[key]: value,\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\r\n\tconst handleIconColorChange = (e: any) => {\r\n\t\tsetIconColor(e.target.value);\r\n\t}\r\n\tconst handleLauncherColorChange = (e: any) => { setLauncherColor(e.target.value) }\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\tupdateChecklistLauncher(checklistLauncherProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistLauncherProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\thandleClose();\r\n\t};\r\n\tconst [type, setType] = useState('Text');\r\n\tconst [text, setText] = useState('Get Started');\r\n\tconst [textColor, setTextColor] = useState('#ffffff');\r\n\r\n\tconst handleTypeChange = (newType: any) => {\r\n\t\tsetType(newType);\r\n\t\tonPropertyChange(\"type\", newType);\r\n\t\r\n\t\t// // Reset icon selection when type changes\r\n\t\t// setIcons(prevIcons => prevIcons.map(icon => ({ ...icon, selected: false })));\r\n\t\r\n\t\t// // Also reset the selected icon in checklistLauncherProperties\r\n\t\t// setChecklistLauncherProperties((prev:any) => ({\r\n\t\t// \t...prev,\r\n\t\t// \ticon: null, // Clear the selected icon\r\n\t\t// }));\r\n\t};\r\n\t\r\n\tconst [error, setError] = useState<string | null>(null);\r\n\t\r\n\tconst [icon, setIcon] = useState<any>();\r\n\r\n\r\n\t\t// Helper function to convert SVG to Base64\r\n\t\tconst svgToBase64 = (svgString: string): string => {\r\n\t\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t\t};\r\n\t\r\nuseEffect(() => {\r\n\tconst defaultIcon = icons[0];\r\n\tif (defaultIcon && !checklistLauncherProperties.icon) {\r\n\t\tconst svgElement = defaultIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\tif (svgElement) {\r\n\t\t\tconst base64Icon = encodeToBase64(svgElement);\r\n\t\t\tconst appliedIconColorBase64Icon = modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);\r\n\t\t\tsetIcon(base64Icon);\r\n\r\n\t\t\t// Create the updated properties with default icon\r\n\t\t\tconst updatedProperties = {\r\n\t\t\t\t...checklistLauncherProperties, \r\n\t\t\t\ticon: base64Icon, \r\n\t\t\t};\r\n\r\n\t\t\t// Update the state\r\n\t\t\tsetChecklistLauncherProperties(updatedProperties);\r\n\t\t\t\r\n\t\t\t// IMPORTANT: Also update the initialState to include the default icon\r\n\t\t\t// This prevents the change detection from thinking there's a user change\r\n\t\t\tsetInitialState(updatedProperties);\r\n\t\t\t\r\n\t\t\t// Update the store\r\n\t\t\tupdateChecklistLauncher(updatedProperties);\r\n\t\t}\r\n\t}\r\n}, [])\r\n\r\n\tconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\t\tif (!base64SVG) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\ttry {\r\n\t\t\t// Check if the string is a valid base64 SVG\r\n\t\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t\t}\r\n\r\n\t\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t\t} else if (hasColoredFill) {\r\n\t\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t\t} else {\r\n\t\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t\t}\r\n\r\n\t\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\t\treturn modifiedBase64;\r\n\t\t} catch (error) {\r\n\t\t\t// console.error(\"Error modifying SVG color:\", error);\r\n\t\t\treturn base64SVG; // Return the original if there's an error\r\n\t\t}\r\n\t};\r\n\t\tconst handleIconClick = async (id: number) => {\r\n\t\t\tsetIcons((prevIcons) =>\r\n\t\t\t\tprevIcons.map((icon) => ({\r\n\t\t\t\t\t...icon,\r\n\t\t\t\t\tselected: icon.id === id,\r\n\t\t\t\t}))\r\n\t\t\t);\r\n\t\t\r\n\t\t\tconst selectedIcon = icons.find((icon) => icon.id === id);\r\n\t\t\tif (selectedIcon) {\r\n\t\t\t\tconst svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\t\t\t\tif (svgElement) {\r\n\t\t\t\t\tconst base64Icon = svgToBase64(svgElement);\r\n\t\t\t\t\tconst appliedIconColorBase64Icon=modifySVGColor(base64Icon, checklistLauncherProperties?.iconColor);\r\n\t\t\t\t\tsetIcon(base64Icon);\r\n\t\t\r\n\r\n\t\t\t\t\tsetChecklistLauncherProperties((prevState:any) => ({\r\n\t\t\t\t\t\t...prevState, // Copy previous state\r\n\t\t\t\t\t\ticon: base64Icon, // Update icon property\r\n\t\t\t\t\t}));\r\n\t\t\t\t\tsetHasChanges(true);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\t\t\r\n\t\tconst [setPositionLeft, setSetPositionLeft] = useState(false);\r\n  const [launlft, setLaunlft] = useState(launlftun); \r\n  const [launrgt, setLaunrgt] = useState(launrgtse); \r\n\r\n  const handleLauncherLeft = () => {\r\n\tsetChecklistLauncherProperties((prev:any) => ({\r\n\t  ...prev,\r\n\t  launcherposition: {\r\n\t\t...prev.launcherposition,\r\n\t\tleft: true, \r\n\t\tright: false, \r\n\t  },\r\n\t}));\r\n\tsetHasChanges(true);\r\n\tsetLaunlft(launlftse); \r\n\tsetLaunrgt(launrgtun); \r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst position = checklistLauncherProperties.launcherposition;\r\n\t\tif (position.left) {\r\n\t\t  setLaunlft(launlftse); \r\n\t\t  setLaunrgt(launrgtun); \r\n\t\t} else if (position.right) {\r\n\t\t  setLaunlft(launlftun); \r\n\t\t  setLaunrgt(launrgtse); \r\n\t\t}\r\n\t  }, [checklistLauncherProperties.launcherposition]); \r\n  \r\n\r\n  const handleLauncherRight = () => {\r\n\tsetChecklistLauncherProperties((prev:any) => ({\r\n\t\t...prev,\r\n\t\tlauncherposition: {\r\n\t\t  ...prev.launcherposition, \r\n\t\t  left: false, \r\n\t\t  right: true, \r\n\t\t},\r\n\t  }));\r\n\tsetHasChanges(true);\r\n\tsetLaunlft(launlftun);\r\n    setLaunrgt(launrgtse); \r\n  };\r\n\t  \r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate('back', { defaultValue: 'back' })}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Launcher', { defaultValue: 'Launcher' })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate('close', { defaultValue: 'close' })}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\r\n\t\t\t\t\t\t<div className=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{height: \"auto\", flexDirection:\"column\" ,padding:\"0\"}}>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Type', { defaultValue: 'Type' })}</div>\r\n\t\t\t\t\t\t\t<div style={{ display:\"inline-block\",gap:\"5px\",padding:\"0 8px 8px 8px\",textAlign:\"left\"}\r\n\t\t\t\t\t\t\t}>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Icon')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Icon', { defaultValue: 'Icon' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Text' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Text')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Text', { defaultValue: 'Text' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tclassName={`qadpt-type-option ${checklistLauncherProperties.type === 'Icon+Txt' ? 'selected' : ''}`}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handleTypeChange('Icon+Txt')}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate('Icon+Text', { defaultValue: 'Icon+Text' })}\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Text\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Text', { defaultValue: 'Text' })}</div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate('Step Title', { defaultValue: 'Step Title' })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"calc(100% - 13px)\",padding: \"0 8px 8px 8px\", margin:\"0\"}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.text}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"text\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(textError)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={textError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.textColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"textColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Icon\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box\" sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon', { defaultValue: 'Icon' })}</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\", width: \"-webkit-fill-available\", flexWrap: \"wrap\", padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{icons.map(icon => (\r\n\t\t\t\t\t\t\t\t\t\t\t<Tooltip arrow key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.iconColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"iconColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#fff'}}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.type === \"Icon+Txt\" && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Text', { defaultValue: 'Text' })}</div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder={translate('Step Title', { defaultValue: 'Step Title' })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"calc(100% - 13px)\",padding: \"0 8px 8px 8px\", margin:\"0\"}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.text}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"text\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(textError)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thelperText={textError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box\" sx={{ flexDirection: \"column\", height: \"auto !important\" }}>\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" >{translate('Icon', { defaultValue: 'Icon' })}</div>\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\", width: \"-webkit-fill-available\", flexWrap: \"wrap\", padding: \"0 8px 8px 8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{icons.map(icon => (\r\n\t\t\t\t\t\t\t\t\t\t\t<Tooltip arrow key={icon.id} title={translate('Select Icon', { defaultValue: 'Select Icon' })}>\r\n\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleIconClick(icon.id)}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{icon.component}\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Upload New Icon */}\r\n\t\t\t\t\t\t\t\t\t{/* <input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\t\taccept=\".ico\"\r\n\t\t\t\t\t\t\t\t\t\tid=\"icon-upload\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleFileUpload}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Icon Color', { defaultValue: 'Icon Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.iconColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"iconColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#fff'}}\r\n\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</Typography>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.textColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"textColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Launcher Color', { defaultValue: 'Launcher Color' })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.launcherColor}\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"launcherColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'#5F9EA0'}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\" sx={{height:\"auto !important\",flexDirection:\"column !important\",padding:\"0 !important\"}}>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Position', { defaultValue: 'Position' })}</div>\r\n\t\t\t\t\t\t\t<div style={{    padding: \"0 8px 8px 8px\",display: \"flex\",gap: \"4px\",cursor:\"pointer\"}}>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: launlft }} onClick={handleLauncherLeft} style={{ zoom: \"0.95\" }} />\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html:  launrgt}} onClick={handleLauncherRight} style={{ zoom: \"0.95\" }} />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div style={{display : \"flex\", alignItems : \"center\",justifyContent : \"space-between\",paddingRight : \"8px\"}}>\r\n\t\t\t\t\t\t\t\t<span className=\"qadpt-control-label\" style={{whiteSpace: \"normal\",\r\n    wordBreak: \"break-word\"}}>{translate('X Axis Offset', { defaultValue: 'X Axis Offset' })}</span>\r\n\t\t\t\t\t\t\t<span className=\"qadpt-chkoffset\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.launcherposition?.xaxisOffset || \"10\"}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"xaxisOffset\", e.target.value)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div style={{display : \"flex\", alignItems : \"center\",justifyContent : \"space-between\",paddingRight : \"8px\"}}>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" style={{whiteSpace: \"normal\",\r\n    wordBreak: \"break-word\"}}>{translate('Y Axis Offset', { defaultValue: 'Y Axis Offset' })}</div>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-chkoffset\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties.launcherposition?.yaxisOffset || \"10\"}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"yaxisOffset\", e.target.value)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Notification Badge', { defaultValue: 'Notification Badge' })}</div>\r\n\r\n\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\tchecked={checklistLauncherProperties.notificationBadge}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationBadge\", e.target.checked)}\r\n\r\n\t\t\t\t\t\t\t\t\tname=\"showByDefault\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t{checklistLauncherProperties.notificationBadge && (\r\n\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Badge Color', { defaultValue: 'Badge Color' })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.notificationBadgeColor}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationBadgeColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'red'}}\r\n\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Text Color', { defaultValue: 'Text Color' })}</div>\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={checklistLauncherProperties?.notificationTextColor}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"notificationTextColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\r\n\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate('Apply', { defaultValue: 'Apply' })}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default LauncherSettings;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAgBC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC9D,OAASC,GAAG,CAAEC,UAAU,CAAEC,SAAS,CAAQC,UAAU,CAAEC,MAAM,CAA2IC,OAAO,KAAQ,eAAe,CACtO,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAA6E,yBAAyB,CAE3H,OACCC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CACPC,OAAO,CAMPC,SAAS,CACTC,SAAS,CACTC,SAAS,CACTC,SAAS,KACH,0BAA0B,CACjC,MAAO,CAAAC,2BAA2B,KAAM,6CAA6C,CAQrF,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAG/C,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAA2B,KAAAC,qBAAA,CAAAC,sBAAA,IAA1B,CAAEC,YAAkB,CAAC,CAAAH,IAAA,CAC9C,KAAM,CAAEI,CAAC,CAAEC,SAAU,CAAC,CAAGb,cAAc,CAAC,CAAC,CAGzC,KAAM,CAELc,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,UAAU,CACVC,aAAa,CACbC,aAAa,CACbC,gBAAgB,CAChBC,SAAS,CACTC,YAAY,CACZC,uBAAuB,CACvBC,sBAAsB,CACtBC,uBAAuB,CACvBC,mBACD,CAAC,CAAGtC,cAAc,CAAEuC,KAAU,EAAKA,KAAK,CAAC,CAEzC,KAAM,CAAAC,cAAc,CAAIC,SAAiB,EAAK,CAC7C,MAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE,CACpD,CAAC,CAED,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAGrD,QAAQ,CAAQ,IAAM,CACjD,MAAO,CACL,CAAEsD,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAACvC,OAAO,CAAC,CAAE8C,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAEhD,OAAQ,CAAE,CAACiD,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACnK,CAAER,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAACtC,OAAO,CAAC,CAAE6C,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAE/C,OAAQ,CAAE,CAACgD,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACnK,CAAER,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAACrC,OAAO,CAAC,CAAE4C,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAE9C,OAAQ,CAAE,CAAC+C,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACnK,CAAER,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAACpC,OAAO,CAAC,CAAE2C,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAE7C,OAAQ,CAAE,CAAC8C,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACnK,CAAER,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAACnC,OAAO,CAAC,CAAE0C,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAE5C,OAAQ,CAAE,CAAC6C,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACnK,CAAER,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAEN,cAAc,CAAClC,OAAO,CAAC,CAAEyC,SAAS,cAAEjC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAE3C,OAAQ,CAAE,CAAC4C,KAAK,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,KAAM,CAAC,CACpK,CACA,CAAC,CAAC,CACJ,KAAM,CACLC,2BAA2B,CAAEC,8BAA8B,CAAC,CAAGhE,QAAQ,CAAM,IAAM,KAAAiE,qBAAA,CACnF,KAAM,CAAAC,kCAAkC,CAAG,EAAAD,qBAAA,CAAApB,sBAAsB,CAAC,CAAC,CAAC,UAAAoB,qBAAA,iBAAzBA,qBAAA,CAA2BE,QAAQ,GAAI,CACjFC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,EAAE,CACRC,IAAI,CAAE,aAAa,CACnB5B,SAAS,CAAE,MAAM,CACjB6B,SAAS,CAAE,MAAM,CACjB/B,aAAa,CAAE,qBAAqB,CACpCgC,gBAAgB,CAAE,CACjBC,IAAI,CAAE,KAAK,CACXC,KAAK,CAAE,IAAI,CACXC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IACd,CAAC,CACDC,iBAAiB,CAAE,KAAK,CACxBC,sBAAsB,CAAE,KAAK,CAC7BC,qBAAqB,CAAE,MAExB,CAAC,CACD,MAAO,CAAAb,kCAAkC,CAC1C,CAAC,CAAC,CAEF;AACA,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGjF,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACkF,UAAU,CAAEC,aAAa,CAAC,CAAGnF,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACoF,YAAY,CAAEC,eAAe,CAAC,CAAGrF,QAAQ,CAAC,IAAM,KAAAsF,sBAAA,CACtD;AACA,MAAO,EAAAA,sBAAA,CAAAzC,sBAAsB,CAAC,CAAC,CAAC,UAAAyC,sBAAA,iBAAzBA,sBAAA,CAA2BnB,QAAQ,GAAIJ,2BAA2B,CAC1E,CAAC,CAAC,CAEF;AACA9D,SAAS,CAAC,IAAM,KAAAsF,sBAAA,CACf,IAAAA,sBAAA,CAAI1C,sBAAsB,CAAC,CAAC,CAAC,UAAA0C,sBAAA,WAAzBA,sBAAA,CAA2BpB,QAAQ,CAAE,CACxC,KAAM,CAAAqB,WAAW,CAAG3C,sBAAsB,CAAC,CAAC,CAAC,CAACsB,QAAQ,CACtDH,8BAA8B,CAACwB,WAAW,CAAC,CAC3CH,eAAe,CAACG,WAAW,CAAC,CAC5BL,aAAa,CAAC,KAAK,CAAC,CACpBF,aAAa,CAAC,IAAI,CAAC,CACnB;AACAQ,YAAY,CAAC,EAAE,CAAC,CACjB,CAAC,IAAM,CACN;AACA,KAAM,CAAAC,WAAW,CAAGtC,KAAK,CAAC,CAAC,CAAC,CAC5B,GAAIsC,WAAW,CAAE,KAAAC,qBAAA,CAChB,KAAM,CAAAC,UAAU,EAAAD,qBAAA,CAAGD,WAAW,CAAClC,SAAS,CAACqC,KAAK,CAACpC,uBAAuB,UAAAkC,qBAAA,iBAAnDA,qBAAA,CAAqDjC,MAAM,CAC9E,GAAIkC,UAAU,CAAE,CACf,KAAM,CAAAE,UAAU,CAAG7C,cAAc,CAAC2C,UAAU,CAAC,CAC7C,KAAM,CAAAG,iBAAiB,CAAG,CACzB,GAAGhC,2BAA2B,CAC9BM,IAAI,CAAEyB,UACP,CAAC,CACDT,eAAe,CAACU,iBAAiB,CAAC,CACnC,CACD,CACD,CACD,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA,KAAM,CAACC,SAAS,CAAEP,YAAY,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAC9C;AACA,KAAM,CAAAiG,sBAAsB,CAAG,QAAAA,CAACC,OAAgB,CAAiC,IAA/B,CAAAC,SAAkB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC3EnB,aAAa,CAAC,CAACiB,OAAO,EAAIC,SAAS,CAAC,CACrC,CAAC,CAED;AACAlG,SAAS,CAAC,IAAM,CACf;AACA,KAAM,CAAAsG,aAAa,CAAGC,IAAI,CAACC,SAAS,CAAC1C,2BAA2B,CAAC,GAAKyC,IAAI,CAACC,SAAS,CAACrB,YAAY,CAAC,CAClGD,aAAa,CAACoB,aAAa,CAAC,CAE5B;AACA,KAAM,CAAAG,mBAAmB,CAAG,CAAC,CAACV,SAAS,CAEvCC,sBAAsB,CAACM,aAAa,CAAEG,mBAAmB,CAAC,CAC3D,CAAC,CAAE,CAAC3C,2BAA2B,CAAEqB,YAAY,CAAEY,SAAS,CAAC,CAAC,CAC1D,KAAM,CAAAW,sBAAsB,CAAIC,CAAM,EAAKrE,aAAa,CAACqE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAExE7G,SAAS,CAAC,IAAM,CACf,GAAI8D,2BAA2B,CAACM,IAAI,CAAE,CACpChB,QAAQ,CAAE0D,SAAS,EACpBA,SAAS,CAACC,GAAG,CAAE3C,IAAI,GAAM,CACvB,GAAGA,IAAI,CACPP,QAAQ,CAAEO,IAAI,CAACd,MAAM,GAAKQ,2BAA2B,CAACM,IAAM;AAC9D,CAAC,CAAC,CACD,CAAC,CACH,CACD,CAAC,CAAE,CAACN,2BAA2B,CAACM,IAAI,CAAC,CAAC,CAGtC,KAAM,CAAA4C,WAAW,CAAGA,CAAA,GAAM,CACzBrE,uBAAuB,CAAC,KAAK,CAAC,CAC/B,CAAC,CACD,KAAM,CAAAsE,iBAAiB,CAAGA,CAAA,GAAM,CAC/B7E,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CACD,KAAM,CAAA8E,gBAAgB,CAAIL,KAAa,EAAK,CAC3C,KAAM,CAAAM,QAAQ,CAAG,EAAE,CAAG,CAACN,KAAK,CAAG,CAAC,EAAI,CAAC,CACrCO,gBAAgB,CAAC,MAAM,CAAED,QAAQ,CAAC,CACnC,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAEhC,CAAC,CAED,KAAM,CAAAD,gBAAgB,CAAGA,CAACE,GAAQ,CAAET,KAAU,GAAK,CAClD;AACA,GAAIS,GAAG,GAAK,MAAM,CAAE,CACnB,GAAI,CAAAC,YAAY,CAAG,EAAE,CACrB,GAAIV,KAAK,CAACT,MAAM,CAAG,CAAC,CAAE,CACrBmB,YAAY,CAAG,mBAAmB,CACnC,CAAC,IAAM,IAAIV,KAAK,CAACT,MAAM,CAAG,EAAE,CAAE,CAC7BmB,YAAY,CAAG,oBAAoB,CACpC,CACA/B,YAAY,CAAC+B,YAAY,CAAC,CAC3B,CAGAxD,8BAA8B,CAAEyD,SAAc,EAAK,CAClD,GAAI,CAAAC,QAAQ,CACZ;AACA,GAAIH,GAAG,GAAK,aAAa,EAAIA,GAAG,GAAK,aAAa,CAAE,CACnDG,QAAQ,CAAG,CACV,GAAGD,SAAS,CACZjD,gBAAgB,CAAE,CACjB,GAAGiD,SAAS,CAACjD,gBAAgB,CAC7B,CAAC+C,GAAG,EAAGT,KACR,CACD,CAAC,CACF,CAAC,IAAM,CACN;AACAY,QAAQ,CAAG,CACV,GAAGD,SAAS,CACZ,CAACF,GAAG,EAAGT,KACR,CAAC,CACF,CACA;AACA3B,aAAa,CAAC,IAAI,CAAC,CACnB,MAAO,CAAAuC,QAAQ,CAChB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAIf,CAAM,EAAK,CACzCjE,YAAY,CAACiE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC7B,CAAC,CACD,KAAM,CAAAc,yBAAyB,CAAIhB,CAAM,EAAK,CAAEnE,gBAAgB,CAACmE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAElF,KAAM,CAAAe,kBAAkB,CAAGA,CAAA,GAAM,CAChC/E,uBAAuB,CAACiB,2BAA2B,CAAC,CACpD;AACAsB,eAAe,CAAC,CAAE,GAAGtB,2BAA4B,CAAC,CAAC,CACnD;AACAoB,aAAa,CAAC,KAAK,CAAC,CACpB;AACAF,aAAa,CAAC,IAAI,CAAC,CACnBlC,mBAAmB,CAAC,IAAI,CAAC,CACzBkE,WAAW,CAAC,CAAC,CACd,CAAC,CACD,KAAM,CAAC7C,IAAI,CAAE0D,OAAO,CAAC,CAAG9H,QAAQ,CAAC,MAAM,CAAC,CACxC,KAAM,CAACsE,IAAI,CAAEyD,OAAO,CAAC,CAAG/H,QAAQ,CAAC,aAAa,CAAC,CAC/C,KAAM,CAACuE,SAAS,CAAEyD,YAAY,CAAC,CAAGhI,QAAQ,CAAC,SAAS,CAAC,CAErD,KAAM,CAAAiI,gBAAgB,CAAIC,OAAY,EAAK,CAC1CJ,OAAO,CAACI,OAAO,CAAC,CAChBb,gBAAgB,CAAC,MAAM,CAAEa,OAAO,CAAC,CAEjC;AACA;AAEA;AACA;AACA;AACA;AACA;AACD,CAAC,CAED,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGpI,QAAQ,CAAgB,IAAI,CAAC,CAEvD,KAAM,CAACqE,IAAI,CAAEgE,OAAO,CAAC,CAAGrI,QAAQ,CAAM,CAAC,CAGtC;AACA,KAAM,CAAAsI,WAAW,CAAIpF,SAAiB,EAAa,CAClD,MAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE,CACtD,CAAC,CAEHjD,SAAS,CAAC,IAAM,CACf,KAAM,CAAAyF,WAAW,CAAGtC,KAAK,CAAC,CAAC,CAAC,CAC5B,GAAIsC,WAAW,EAAI,CAAC3B,2BAA2B,CAACM,IAAI,CAAE,KAAAkE,sBAAA,CACrD,KAAM,CAAA3C,UAAU,EAAA2C,sBAAA,CAAG7C,WAAW,CAAClC,SAAS,CAACqC,KAAK,CAACpC,uBAAuB,UAAA8E,sBAAA,iBAAnDA,sBAAA,CAAqD7E,MAAM,CAC9E,GAAIkC,UAAU,CAAE,CACf,KAAM,CAAAE,UAAU,CAAG7C,cAAc,CAAC2C,UAAU,CAAC,CAC7C,KAAM,CAAA4C,0BAA0B,CAAGC,cAAc,CAAC3C,UAAU,CAAE/B,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAErB,SAAS,CAAC,CACrG2F,OAAO,CAACvC,UAAU,CAAC,CAEnB;AACA,KAAM,CAAA4C,iBAAiB,CAAG,CACzB,GAAG3E,2BAA2B,CAC9BM,IAAI,CAAEyB,UACP,CAAC,CAED;AACA9B,8BAA8B,CAAC0E,iBAAiB,CAAC,CAEjD;AACA;AACArD,eAAe,CAACqD,iBAAiB,CAAC,CAElC;AACA5F,uBAAuB,CAAC4F,iBAAiB,CAAC,CAC3C,CACD,CACD,CAAC,CAAE,EAAE,CAAC,CAEL,KAAM,CAAAD,cAAc,CAAGA,CAACE,SAAc,CAAEC,KAAU,GAAK,CACtD,GAAI,CAACD,SAAS,CAAE,CACf,MAAO,EAAE,CACV,CAEA,GAAI,CACH;AACA,GAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,CAAE,CACtD,MAAO,CAAAF,SAAS,CAAE;AACnB,CAEA,KAAM,CAAAG,UAAU,CAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAC,SAAS,CAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC,CACjD,KAAM,CAAAK,cAAc,CAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC,CAE/D,GAAI,CAAAM,WAAW,CAAGN,UAAU,CAE5B,GAAIG,SAAS,EAAI,CAACC,cAAc,CAAE,CACjC;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,CAAE,WAAWT,KAAK,GAAG,CAAC,CAC1E,CAAC,IAAM,IAAIM,cAAc,CAAE,CAC1B;AACAE,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,CAAE,SAAST,KAAK,GAAG,CAAC,CAC9E,CAAC,IAAM,CACN;AACAQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,CAAE,eAAeT,KAAK,GAAG,CAAC,CAClFQ,WAAW,CAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,CAAE,cAAcT,KAAK,GAAG,CAAC,CACjF,CAEA,KAAM,CAAAU,cAAc,CAAG,6BAA6BnG,IAAI,CAACiG,WAAW,CAAC,EAAE,CACvE,MAAO,CAAAE,cAAc,CACtB,CAAE,MAAOnB,KAAK,CAAE,CACf;AACA,MAAO,CAAAQ,SAAS,CAAE;AACnB,CACD,CAAC,CACA,KAAM,CAAAY,eAAe,CAAG,KAAO,CAAAjG,EAAU,EAAK,CAC7CD,QAAQ,CAAE0D,SAAS,EAClBA,SAAS,CAACC,GAAG,CAAE3C,IAAI,GAAM,CACxB,GAAGA,IAAI,CACPP,QAAQ,CAAEO,IAAI,CAACf,EAAE,GAAKA,EACvB,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAkG,YAAY,CAAGpG,KAAK,CAACqG,IAAI,CAAEpF,IAAI,EAAKA,IAAI,CAACf,EAAE,GAAKA,EAAE,CAAC,CACzD,GAAIkG,YAAY,CAAE,KAAAE,qBAAA,CACjB,KAAM,CAAA9D,UAAU,EAAA8D,qBAAA,CAAGF,YAAY,CAAChG,SAAS,CAACqC,KAAK,CAACpC,uBAAuB,UAAAiG,qBAAA,iBAApDA,qBAAA,CAAsDhG,MAAM,CAC/E,GAAIkC,UAAU,CAAE,CACf,KAAM,CAAAE,UAAU,CAAGwC,WAAW,CAAC1C,UAAU,CAAC,CAC1C,KAAM,CAAA4C,0BAA0B,CAACC,cAAc,CAAC3C,UAAU,CAAE/B,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAErB,SAAS,CAAC,CACnG2F,OAAO,CAACvC,UAAU,CAAC,CAGnB9B,8BAA8B,CAAEyD,SAAa,GAAM,CAClD,GAAGA,SAAS,CAAE;AACdpD,IAAI,CAAEyB,UAAY;AACnB,CAAC,CAAC,CAAC,CACHX,aAAa,CAAC,IAAI,CAAC,CACpB,CACD,CACD,CAAC,CAED,KAAM,CAACwE,eAAe,CAAEC,kBAAkB,CAAC,CAAG5J,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC6J,OAAO,CAAEC,UAAU,CAAC,CAAG9J,QAAQ,CAACgB,SAAS,CAAC,CACjD,KAAM,CAAC+I,OAAO,CAAEC,UAAU,CAAC,CAAGhK,QAAQ,CAACiB,SAAS,CAAC,CAEjD,KAAM,CAAAgJ,kBAAkB,CAAGA,CAAA,GAAM,CAClCjG,8BAA8B,CAAEkG,IAAQ,GAAM,CAC5C,GAAGA,IAAI,CACP1F,gBAAgB,CAAE,CACnB,GAAG0F,IAAI,CAAC1F,gBAAgB,CACxBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,KACN,CACF,CAAC,CAAC,CAAC,CACHS,aAAa,CAAC,IAAI,CAAC,CACnB2E,UAAU,CAAC5I,SAAS,CAAC,CACrB8I,UAAU,CAAC7I,SAAS,CAAC,CACrB,CAAC,CACDlB,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkK,QAAQ,CAAGpG,2BAA2B,CAACS,gBAAgB,CAC7D,GAAI2F,QAAQ,CAAC1F,IAAI,CAAE,CACjBqF,UAAU,CAAC5I,SAAS,CAAC,CACrB8I,UAAU,CAAC7I,SAAS,CAAC,CACvB,CAAC,IAAM,IAAIgJ,QAAQ,CAACzF,KAAK,CAAE,CACzBoF,UAAU,CAAC9I,SAAS,CAAC,CACrBgJ,UAAU,CAAC/I,SAAS,CAAC,CACvB,CACC,CAAC,CAAE,CAAC8C,2BAA2B,CAACS,gBAAgB,CAAC,CAAC,CAGnD,KAAM,CAAA4F,mBAAmB,CAAGA,CAAA,GAAM,CACnCpG,8BAA8B,CAAEkG,IAAQ,GAAM,CAC7C,GAAGA,IAAI,CACP1F,gBAAgB,CAAE,CAChB,GAAG0F,IAAI,CAAC1F,gBAAgB,CACxBC,IAAI,CAAE,KAAK,CACXC,KAAK,CAAE,IACT,CACC,CAAC,CAAC,CAAC,CACLS,aAAa,CAAC,IAAI,CAAC,CACnB2E,UAAU,CAAC9I,SAAS,CAAC,CAClBgJ,UAAU,CAAC/I,SAAS,CAAC,CACvB,CAAC,CAIF,mBACCM,IAAA,QACC+B,EAAE,CAAC,mBAAmB,CACtB+G,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAE7B7I,KAAA,QAAK4I,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7B7I,KAAA,QAAK4I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACnC/I,IAAA,CAAClB,UAAU,EACV,aAAY6B,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAE,CACxDC,OAAO,CAAEvD,WAAY,CAAAqD,QAAA,cAErB/I,IAAA,CAACH,2BAA2B,GAAE,CAAC,CACpB,CAAC,cACbG,IAAA,QAAK8I,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEpI,SAAS,CAAC,UAAU,CAAE,CAAEqI,YAAY,CAAE,UAAW,CAAC,CAAC,CAAM,CAAC,cACxFhJ,IAAA,CAAClB,UAAU,EACVoK,IAAI,CAAC,OAAO,CACZ,aAAYvI,SAAS,CAAC,OAAO,CAAE,CAAEqI,YAAY,CAAE,OAAQ,CAAC,CAAE,CAC1DC,OAAO,CAAEvD,WAAY,CAAAqD,QAAA,cAErB/I,IAAA,CAACf,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,cACNe,IAAA,QAAK8I,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9B7I,KAAA,QAAK4I,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAE9B7I,KAAA,QAAK4I,SAAS,CAAC,mBAAmB,CACtB1G,KAAK,CAAE,CAAC+G,MAAM,CAAE,MAAM,CAAEC,aAAa,CAAC,QAAQ,CAAEC,OAAO,CAAC,GAAG,CAAE,CAAAN,QAAA,eACxE/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cACxF9I,KAAA,QAAKkC,KAAK,CAAE,CAAEE,OAAO,CAAC,cAAc,CAACgH,GAAG,CAAC,KAAK,CAACD,OAAO,CAAC,eAAe,CAACE,SAAS,CAAC,MAAM,CACtF,CAAAR,QAAA,eACA/I,IAAA,WACC8I,SAAS,CAAE,qBAAqBtG,2BAA2B,CAACK,IAAI,GAAK,MAAM,CAAG,UAAU,CAAG,EAAE,EAAG,CAChGoG,OAAO,CAAEA,CAAA,GAAMvC,gBAAgB,CAAC,MAAM,CAAE,CAAAqC,QAAA,CAEvCpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CACrC,CAAC,cACThJ,IAAA,WACC8I,SAAS,CAAE,qBAAqBtG,2BAA2B,CAACK,IAAI,GAAK,MAAM,CAAG,UAAU,CAAG,EAAE,EAAG,CAChGoG,OAAO,CAAEA,CAAA,GAAMvC,gBAAgB,CAAC,MAAM,CAAE,CAAAqC,QAAA,CAEvCpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CACrC,CAAC,cACThJ,IAAA,WACC8I,SAAS,CAAE,qBAAqBtG,2BAA2B,CAACK,IAAI,GAAK,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACpGoG,OAAO,CAAEA,CAAA,GAAMvC,gBAAgB,CAAC,UAAU,CAAE,CAAAqC,QAAA,CAE3CpI,SAAS,CAAC,WAAW,CAAE,CAAEqI,YAAY,CAAE,WAAY,CAAC,CAAC,CAC/C,CAAC,EACL,CAAC,EAGF,CAAC,CAELxG,2BAA2B,CAACK,IAAI,GAAK,MAAM,eAC3C3C,KAAA,CAAAE,SAAA,EAAA2I,QAAA,eAEE7I,KAAA,CAACvB,GAAG,EACKoD,EAAE,CAAC,mBAAmB,CACtB+G,SAAS,CAAC,mBAAmB,CAC7BU,EAAE,CAAE,CAAEJ,aAAa,CAAE,QAAQ,CAAED,MAAM,CAAE,iBAAkB,CAAE,CAAAJ,QAAA,eAEpE/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAGpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cAEhFhJ,IAAA,CAACnB,SAAS,EACR4K,OAAO,CAAC,UAAU,CAClBP,IAAI,CAAC,OAAO,CACtBQ,WAAW,CAAE/I,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAE,CAC3DF,SAAS,CAAC,qBAAqB,CAC/B1G,KAAK,CAAE,CAACuH,KAAK,CAAE,mBAAmB,CAACN,OAAO,CAAE,eAAe,CAAEO,MAAM,CAAC,GAAG,CAAE,CACzErE,KAAK,CAAE/C,2BAA2B,CAACO,IAAK,CACxC8G,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,MAAM,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC1DqB,KAAK,CAAEkD,OAAO,CAACrF,SAAS,CAAE,CAC1BsF,UAAU,CAAEtF,SAAU,CACtBuF,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBT,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEU,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEX,SAAS,CAAE,iBAAiB,CAAEY,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAChB,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAEdjJ,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,CAACpB,UAAU,EAACkK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAC,CAAa,CAAC,cACpHhJ,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAEQ,SAAU,CAC9C6G,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,WAAW,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC/DuD,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,EACF,CACH,CAGAtG,2BAA2B,CAACK,IAAI,GAAK,MAAM,eAC3C3C,KAAA,CAAAE,SAAA,EAAA2I,QAAA,eAEM7I,KAAA,CAACvB,GAAG,EAACoD,EAAE,CAAC,mBAAmB,CAAC+G,SAAS,CAAC,mBAAmB,CAACU,EAAE,CAAE,CAAEJ,aAAa,CAAE,QAAQ,CAAED,MAAM,CAAE,iBAAiB,CAACE,OAAO,CAAC,cAAe,CAAE,CAAAN,QAAA,eAChJ/I,IAAA,CAACpB,UAAU,EAACkK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CAAa,CAAC,cACrGhJ,IAAA,CAACrB,GAAG,EAAC6K,EAAE,CAAE,CAAElH,OAAO,CAAE,MAAM,CAAEgH,GAAG,CAAE,CAAC,CAAEc,UAAU,CAAE,QAAQ,CAAET,KAAK,CAAE,wBAAwB,CAAEU,QAAQ,CAAE,MAAM,CAAEhB,OAAO,CAAE,eAAgB,CAAE,CAAAN,QAAA,CACvIlH,KAAK,CAAC4D,GAAG,CAAC3C,IAAI,eACd9C,IAAA,CAAChB,OAAO,EAACsL,KAAK,MAAeC,KAAK,CAAE5J,SAAS,CAAC,aAAa,CAAE,CAAEqI,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAD,QAAA,cAC/F/I,IAAA,CAAClB,UAAU,EACVmK,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAClF,IAAI,CAACf,EAAE,CAAE,CACxCyH,EAAE,CAAE,CACHU,MAAM,CAAEpH,IAAI,CAACP,QAAQ,CAAG,+BAA+B,CAAG,MAAM,CAC5DiI,YAAY,CAAE,KAAK,CACnBnB,OAAO,CAAE,KAAK,CACdoB,UAAU,CAAC,SAChB,CAAE,CAAA1B,QAAA,CAEDjG,IAAI,CAACb,SAAS,CACJ,CAAC,EAXQa,IAAI,CAACf,EAYlB,CACT,CAAC,CACI,CAAC,EACF,CAAC,cAGL7B,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClC/I,IAAA,CAACpB,UAAU,EAACkK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAC,CAAa,CAAC,cACpHhJ,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAErB,SAAU,CAC9C0I,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,WAAW,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC7DuD,SAAS,CAAC,mBAAmB,CAC7B1G,KAAK,CAAE,CAACsI,eAAe,CAAC,MAAM,CAAE,CAElC,CAAC,EACE,CAAC,EAIF,CAEH,CAGAlI,2BAA2B,CAACK,IAAI,GAAK,UAAU,eAC/C3C,KAAA,CAAAE,SAAA,EAAA2I,QAAA,eAEC7I,KAAA,CAACvB,GAAG,EACMoD,EAAE,CAAC,mBAAmB,CACtB+G,SAAS,CAAC,mBAAmB,CAC7BU,EAAE,CAAE,CAAEJ,aAAa,CAAE,QAAQ,CAAED,MAAM,CAAE,iBAAkB,CAAE,CAAAJ,QAAA,eAEpE/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAGpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cAEhFhJ,IAAA,CAACnB,SAAS,EACR4K,OAAO,CAAC,UAAU,CAClBP,IAAI,CAAC,OAAO,CACtBQ,WAAW,CAAE/I,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAE,CAC3DF,SAAS,CAAC,qBAAqB,CAC/B1G,KAAK,CAAE,CAACuH,KAAK,CAAE,mBAAmB,CAACN,OAAO,CAAE,eAAe,CAAEO,MAAM,CAAC,GAAG,CAAE,CACzErE,KAAK,CAAE/C,2BAA2B,CAACO,IAAK,CACxC8G,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,MAAM,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC1DqB,KAAK,CAAEkD,OAAO,CAACrF,SAAS,CAAE,CAC1BsF,UAAU,CAAEtF,SAAU,CACtBuF,UAAU,CAAE,CACXC,YAAY,CAAE,EAAE,CAChBT,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEU,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAC,CAACA,MAAM,CAAC,MAAM,CAAC,CAC5B,SAAS,CAAE,CAAEX,SAAS,CAAE,iBAAiB,CAAEY,WAAW,CAAC,iBAAiB,CAAC,CACzE,qBAAqB,CAAC,CAAChB,MAAM,CAAC,iBAAiB,CAChD,CACD,CAAE,CACF,CAAC,EACA,CAAC,cAEdjJ,KAAA,CAACvB,GAAG,EAACoD,EAAE,CAAC,mBAAmB,CAAC+G,SAAS,CAAC,mBAAmB,CAACU,EAAE,CAAE,CAAEJ,aAAa,CAAE,QAAQ,CAAED,MAAM,CAAE,iBAAkB,CAAE,CAAAJ,QAAA,eACpH/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAGpI,SAAS,CAAC,MAAM,CAAE,CAAEqI,YAAY,CAAE,MAAO,CAAC,CAAC,CAAM,CAAC,cACxFhJ,IAAA,CAACrB,GAAG,EAAC6K,EAAE,CAAE,CAAElH,OAAO,CAAE,MAAM,CAAEgH,GAAG,CAAE,CAAC,CAAEc,UAAU,CAAE,QAAQ,CAAET,KAAK,CAAE,wBAAwB,CAAEU,QAAQ,CAAE,MAAM,CAAEhB,OAAO,CAAE,eAAgB,CAAE,CAAAN,QAAA,CACvIlH,KAAK,CAAC4D,GAAG,CAAC3C,IAAI,eACd9C,IAAA,CAAChB,OAAO,EAACsL,KAAK,MAAeC,KAAK,CAAE5J,SAAS,CAAC,aAAa,CAAE,CAAEqI,YAAY,CAAE,aAAc,CAAC,CAAE,CAAAD,QAAA,cAC/F/I,IAAA,CAAClB,UAAU,EACVmK,OAAO,CAAEA,CAAA,GAAMjB,eAAe,CAAClF,IAAI,CAACf,EAAE,CAAE,CACxCyH,EAAE,CAAE,CACHU,MAAM,CAAEpH,IAAI,CAACP,QAAQ,CAAG,+BAA+B,CAAG,MAAM,CAC5DiI,YAAY,CAAE,KAAK,CACnBnB,OAAO,CAAE,KAAK,CACdoB,UAAU,CAAC,SAChB,CAAE,CAAA1B,QAAA,CAEAjG,IAAI,CAACb,SAAS,CACJ,CAAC,EAXOa,IAAI,CAACf,EAYjB,CACT,CAAC,CAUE,CAAC,EAGD,CAAC,cAGN7B,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,CAACpB,UAAU,EAACkK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAC,CAAa,CAAC,cACpHhJ,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAErB,SAAU,CAC9C0I,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,WAAW,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC7DuD,SAAS,CAAC,mBAAmB,CAC7B1G,KAAK,CAAE,CAACsI,eAAe,CAAC,MAAM,CAAE,CAElC,CAAC,EACI,CAAC,cACNxK,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,CAACpB,UAAU,EAACkK,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAC,CAAa,CAAC,cACpHhJ,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAEQ,SAAU,CAC9C6G,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,WAAW,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC/DuD,SAAS,CAAC,mBAAmB,CAC7B,CAAC,EACE,CAAC,EACH,CACF,cAKD5I,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,gBAAgB,CAAE,CAAEqI,YAAY,CAAE,gBAAiB,CAAC,CAAC,CAAM,CAAC,cAC5GhJ,IAAA,QAAA+I,QAAA,cACA/I,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAEvB,aAAc,CAClD4I,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,eAAe,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAClEuD,SAAS,CAAC,mBAAmB,CAC7B1G,KAAK,CAAE,CAACsI,eAAe,CAAC,SAAS,CAAE,CAEnC,CAAC,CACG,CAAC,EACH,CAAC,cAINxK,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAACU,EAAE,CAAE,CAACL,MAAM,CAAC,iBAAiB,CAACC,aAAa,CAAC,mBAAmB,CAACC,OAAO,CAAC,cAAc,CAAE,CAAAN,QAAA,eAC1H/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,UAAU,CAAE,CAAEqI,YAAY,CAAE,UAAW,CAAC,CAAC,CAAM,CAAC,cAChG9I,KAAA,QAAKkC,KAAK,CAAE,CAAKiH,OAAO,CAAE,eAAe,CAAC/G,OAAO,CAAE,MAAM,CAACgH,GAAG,CAAE,KAAK,CAACqB,MAAM,CAAC,SAAS,CAAE,CAAA5B,QAAA,eAEtF/I,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAEmG,OAAQ,CAAE,CAACW,OAAO,CAAEP,kBAAmB,CAACtG,KAAK,CAAE,CAAEC,IAAI,CAAE,MAAO,CAAE,CAAE,CAAC,cAE5GrC,IAAA,SAAMkC,uBAAuB,CAAE,CAAEC,MAAM,CAAGqG,OAAO,CAAE,CAACS,OAAO,CAAEJ,mBAAoB,CAACzG,KAAK,CAAE,CAAEC,IAAI,CAAE,MAAO,CAAE,CAAE,CAAC,EACzG,CAAC,cACNnC,KAAA,QAAKkC,KAAK,CAAE,CAACE,OAAO,CAAG,MAAM,CAAE8H,UAAU,CAAG,QAAQ,CAACQ,cAAc,CAAG,eAAe,CAACC,YAAY,CAAG,KAAK,CAAE,CAAA9B,QAAA,eAC3G/I,IAAA,SAAM8I,SAAS,CAAC,qBAAqB,CAAC1G,KAAK,CAAE,CAAC0I,UAAU,CAAE,QAAQ,CACtEC,SAAS,CAAE,YAAY,CAAE,CAAAhC,QAAA,CAAEpI,SAAS,CAAC,eAAe,CAAE,CAAEqI,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAO,CAAC,cAC7FhJ,IAAA,SAAM8I,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cACjC/I,IAAA,CAACnB,SAAS,EACT4K,OAAO,CAAC,UAAU,CAClBlE,KAAK,CAAE,EAAAhF,qBAAA,CAAAiC,2BAA2B,CAACS,gBAAgB,UAAA1C,qBAAA,iBAA5CA,qBAAA,CAA8C6C,WAAW,GAAI,IAAK,CAEzE8F,IAAI,CAAC,OAAO,CACZJ,SAAS,CAAC,qBAAqB,CAC/Be,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,aAAa,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjEyE,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBT,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEU,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAEhC,CACD,CAAE,CAED,CAAC,CACI,CAAC,EACJ,CAAC,cACNhK,KAAA,QAAKkC,KAAK,CAAE,CAACE,OAAO,CAAG,MAAM,CAAE8H,UAAU,CAAG,QAAQ,CAACQ,cAAc,CAAG,eAAe,CAACC,YAAY,CAAG,KAAK,CAAE,CAAA9B,QAAA,eAC1G/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAC1G,KAAK,CAAE,CAAC0I,UAAU,CAAE,QAAQ,CACrEC,SAAS,CAAE,YAAY,CAAE,CAAAhC,QAAA,CAAEpI,SAAS,CAAC,eAAe,CAAE,CAAEqI,YAAY,CAAE,eAAgB,CAAC,CAAC,CAAM,CAAC,cAC5FhJ,IAAA,QAAK8I,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAChC/I,IAAA,CAACnB,SAAS,EACT4K,OAAO,CAAC,UAAU,CAClBlE,KAAK,CAAE,EAAA/E,sBAAA,CAAAgC,2BAA2B,CAACS,gBAAgB,UAAAzC,sBAAA,iBAA5CA,sBAAA,CAA8C6C,WAAW,GAAI,IAAK,CAEzE6F,IAAI,CAAC,OAAO,CACZJ,SAAS,CAAC,qBAAqB,CAC/Be,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,aAAa,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CACjEyE,UAAU,CAAE,CACXC,YAAY,CAAE,IAAI,CAClBT,EAAE,CAAE,CAEH,0CAA0C,CAAE,CAAEU,MAAM,CAAE,MAAO,CAAC,CAC9D,gDAAgD,CAAE,CAAEA,MAAM,CAAE,MAAO,CAAC,CACpE,YAAY,CAAE,CAAEA,MAAM,CAAE,MAAO,CAEhC,CACD,CAAE,CAED,CAAC,CACG,CAAC,EACH,CAAC,EACC,CAAC,cAERhK,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,oBAAoB,CAAE,CAAEqI,YAAY,CAAE,oBAAqB,CAAC,CAAC,CAAM,CAAC,cAG3HhJ,IAAA,QAAA+I,QAAA,cACO7I,KAAA,UAAO4I,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC/B/I,IAAA,UACC6C,IAAI,CAAC,UAAU,CACfmI,OAAO,CAAExI,2BAA2B,CAACc,iBAAkB,CACvDuG,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,mBAAmB,CAAET,CAAC,CAACC,MAAM,CAAC0F,OAAO,CAAE,CAEzEC,IAAI,CAAC,eAAe,CACpB,CAAC,cACFjL,IAAA,SAAM8I,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC1B,CAAC,CACH,CAAC,EACF,CAAC,CAELtG,2BAA2B,CAACc,iBAAiB,eAC7CpD,KAAA,CAAAE,SAAA,EAAA2I,QAAA,eACC7I,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,aAAa,CAAE,CAAEqI,YAAY,CAAE,aAAc,CAAC,CAAC,CAAM,CAAC,cACtGhJ,IAAA,QAAA+I,QAAA,cACA/I,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAEe,sBAAuB,CAC3DsG,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,wBAAwB,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC3EuD,SAAS,CAAC,mBAAmB,CAC7B1G,KAAK,CAAE,CAACsI,eAAe,CAAC,KAAK,CAAE,CAE/B,CAAC,CACG,CAAC,EACH,CAAC,cACNxK,KAAA,CAACvB,GAAG,EAACmK,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC/I,IAAA,QAAK8I,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEpI,SAAS,CAAC,YAAY,CAAE,CAAEqI,YAAY,CAAE,YAAa,CAAC,CAAC,CAAM,CAAC,cACpGhJ,IAAA,QAAA+I,QAAA,cACA/I,IAAA,UACC6C,IAAI,CAAC,OAAO,CACZ0C,KAAK,CAAE/C,2BAA2B,SAA3BA,2BAA2B,iBAA3BA,2BAA2B,CAAEgB,qBAAsB,CAC1DqG,QAAQ,CAAGxE,CAAC,EAAKS,gBAAgB,CAAC,uBAAuB,CAAET,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAC3EuD,SAAS,CAAC,mBAAmB,CAC5B,CAAC,CACG,CAAC,EACH,CAAC,EACL,CACF,EAGG,CAAC,CAKF,CAAC,cACN9I,IAAA,QAAK8I,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cAClC/I,IAAA,CAACjB,MAAM,EACN0K,OAAO,CAAC,WAAW,CACnBR,OAAO,CAAE3C,kBAAmB,CAC5BwC,SAAS,CAAE,aAAarF,UAAU,CAAG,UAAU,CAAG,EAAE,EAAG,CACvDyH,QAAQ,CAAEzH,UAAW,CAAAsF,QAAA,CAEpBpI,SAAS,CAAC,OAAO,CAAE,CAAEqI,YAAY,CAAE,OAAQ,CAAC,CAAC,CACvC,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,CAER,CAAC,CAED,cAAe,CAAA3I,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}