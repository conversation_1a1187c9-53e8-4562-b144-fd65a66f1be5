[{"E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "10", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "11", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "13", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "14", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "15", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "16", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "17", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "18", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "19", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "21", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "22", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "23", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "24", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "25", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "26", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "27", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "28", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "29", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "30", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "31", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "32", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "33", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "34", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "35", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "36", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "37", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "38", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "39", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "40", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "41", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "42", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "43", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "44", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "45", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "46", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "47", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "48", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "49", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "50", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "51", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "52", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "53", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "54", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "55", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "56", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "57", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "58", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "59", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "60", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "61", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "62", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "63", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "64", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "65", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "66", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "67", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "68", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "69", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "70", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "71", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "72", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "73", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "74", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "75", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "76", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "77", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\Tooltip.tsx": "78", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Alertpopup.tsx": "79", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\designFields\\TooltipCanvasSettings.tsx": "80", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\TooltipBody.tsx": "81", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\Buttons.tsx": "82", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\ImageSection.tsx": "83", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\RTE\\RTESection.tsx": "84", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\ButtonSetting.tsx": "85", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "86", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "87", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "88", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "89", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "90", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "91", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Button\\index.tsx": "92", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "93", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "94", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "95", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "96", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\EnableAI.tsx": "97", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "98", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AIOptionsPopup.tsx": "99", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "100", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\ExtensionLoader.tsx": "101", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\hooks\\useExtensionInitialization.ts": "102", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\ExtensionPopupLoader.tsx": "103", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "104", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "105", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "106", "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "107"}, {"size": 604, "mtime": 1742548498942, "results": "108", "hashOfConfig": "109"}, {"size": 440, "mtime": 1742548498948, "results": "110", "hashOfConfig": "109"}, {"size": 2855, "mtime": 1752753524877, "results": "111", "hashOfConfig": "109"}, {"size": 3050, "mtime": 1752752349272, "results": "112", "hashOfConfig": "109"}, {"size": 247815, "mtime": 1752753653273, "results": "113", "hashOfConfig": "109"}, {"size": 6057, "mtime": 1752131905973, "results": "114", "hashOfConfig": "109"}, {"size": 636, "mtime": 1742548498924, "results": "115", "hashOfConfig": "109"}, {"size": 3112, "mtime": 1752752348843, "results": "116", "hashOfConfig": "109"}, {"size": 396652, "mtime": 1752752386371, "results": "117", "hashOfConfig": "109"}, {"size": 6144, "mtime": 1749441357216, "results": "118", "hashOfConfig": "109"}, {"size": 5077, "mtime": 1752752348273, "results": "119", "hashOfConfig": "109"}, {"size": 40511, "mtime": 1752752348157, "results": "120", "hashOfConfig": "109"}, {"size": 13098, "mtime": 1752752452655, "results": "121", "hashOfConfig": "109"}, {"size": 13085, "mtime": 1752752348720, "results": "122", "hashOfConfig": "109"}, {"size": 3731, "mtime": 1752752347385, "results": "123", "hashOfConfig": "109"}, {"size": 193, "mtime": 1742548498848, "results": "124", "hashOfConfig": "109"}, {"size": 296570, "mtime": 1752656924407, "results": "125", "hashOfConfig": "109"}, {"size": 1954, "mtime": 1752131906004, "results": "126", "hashOfConfig": "109"}, {"size": 1898, "mtime": 1744608017898, "results": "127", "hashOfConfig": "109"}, {"size": 8101, "mtime": 1752656924594, "results": "128", "hashOfConfig": "109"}, {"size": 23258, "mtime": 1752752347406, "results": "129", "hashOfConfig": "109"}, {"size": 33372, "mtime": 1752752347776, "results": "130", "hashOfConfig": "109"}, {"size": 13556, "mtime": 1752656924407, "results": "131", "hashOfConfig": "109"}, {"size": 49705, "mtime": 1752131905957, "results": "132", "hashOfConfig": "109"}, {"size": 26704, "mtime": 1752752342713, "results": "133", "hashOfConfig": "109"}, {"size": 4880, "mtime": 1750138931460, "results": "134", "hashOfConfig": "109"}, {"size": 9238, "mtime": 1747226854300, "results": "135", "hashOfConfig": "109"}, {"size": 743, "mtime": 1742548498847, "results": "136", "hashOfConfig": "109"}, {"size": 1248, "mtime": 1742548498954, "results": "137", "hashOfConfig": "109"}, {"size": 3285, "mtime": 1752752348335, "results": "138", "hashOfConfig": "109"}, {"size": 2997, "mtime": 1752752348001, "results": "139", "hashOfConfig": "109"}, {"size": 19401, "mtime": 1752752348793, "results": "140", "hashOfConfig": "109"}, {"size": 2608, "mtime": 1747226854268, "results": "141", "hashOfConfig": "109"}, {"size": 19040, "mtime": 1752752347555, "results": "142", "hashOfConfig": "109"}, {"size": 24904, "mtime": 1752752348430, "results": "143", "hashOfConfig": "109"}, {"size": 28620, "mtime": 1752752348437, "results": "144", "hashOfConfig": "109"}, {"size": 6625, "mtime": 1752752347943, "results": "145", "hashOfConfig": "109"}, {"size": 7772, "mtime": 1752752348441, "results": "146", "hashOfConfig": "109"}, {"size": 20321, "mtime": 1752752348099, "results": "147", "hashOfConfig": "109"}, {"size": 2848, "mtime": 1747226854191, "results": "148", "hashOfConfig": "109"}, {"size": 15966, "mtime": 1752752348378, "results": "149", "hashOfConfig": "109"}, {"size": 6245, "mtime": 1747226854224, "results": "150", "hashOfConfig": "109"}, {"size": 3236, "mtime": 1747226854156, "results": "151", "hashOfConfig": "109"}, {"size": 8476, "mtime": 1752752347448, "results": "152", "hashOfConfig": "109"}, {"size": 11208, "mtime": 1752752347450, "results": "153", "hashOfConfig": "109"}, {"size": 16370, "mtime": 1752752347428, "results": "154", "hashOfConfig": "109"}, {"size": 60407, "mtime": 1752656924422, "results": "155", "hashOfConfig": "109"}, {"size": 15571, "mtime": 1752752348881, "results": "156", "hashOfConfig": "109"}, {"size": 5258, "mtime": 1752752348470, "results": "157", "hashOfConfig": "109"}, {"size": 883, "mtime": 1742548498921, "results": "158", "hashOfConfig": "109"}, {"size": 2931, "mtime": 1748943304919, "results": "159", "hashOfConfig": "109"}, {"size": 2669, "mtime": 1747226854141, "results": "160", "hashOfConfig": "109"}, {"size": 11332, "mtime": 1752752386324, "results": "161", "hashOfConfig": "109"}, {"size": 7599, "mtime": 1752752349170, "results": "162", "hashOfConfig": "109"}, {"size": 24200, "mtime": 1752131906004, "results": "163", "hashOfConfig": "109"}, {"size": 30050, "mtime": 1752752342775, "results": "164", "hashOfConfig": "109"}, {"size": 16126, "mtime": 1750672451808, "results": "165", "hashOfConfig": "109"}, {"size": 32671, "mtime": 1752752342775, "results": "166", "hashOfConfig": "109"}, {"size": 2717, "mtime": 1752752346752, "results": "167", "hashOfConfig": "109"}, {"size": 17188, "mtime": 1752752342831, "results": "168", "hashOfConfig": "109"}, {"size": 27631, "mtime": 1752752343044, "results": "169", "hashOfConfig": "109"}, {"size": 10829, "mtime": 1752752343166, "results": "170", "hashOfConfig": "109"}, {"size": 14592, "mtime": 1752752342775, "results": "171", "hashOfConfig": "109"}, {"size": 26698, "mtime": 1752752343197, "results": "172", "hashOfConfig": "109"}, {"size": 30624, "mtime": 1752752343849, "results": "173", "hashOfConfig": "109"}, {"size": 9064, "mtime": 1752656924453, "results": "174", "hashOfConfig": "109"}, {"size": 15285, "mtime": 1752752343598, "results": "175", "hashOfConfig": "109"}, {"size": 27206, "mtime": 1752752345432, "results": "176", "hashOfConfig": "109"}, {"size": 13889, "mtime": 1752752344491, "results": "177", "hashOfConfig": "109"}, {"size": 1962, "mtime": 1747226854058, "results": "178", "hashOfConfig": "109"}, {"size": 2401, "mtime": 1752752345197, "results": "179", "hashOfConfig": "109"}, {"size": 702, "mtime": 1752752346057, "results": "180", "hashOfConfig": "109"}, {"size": 15217, "mtime": 1752752345807, "results": "181", "hashOfConfig": "109"}, {"size": 29082, "mtime": 1752752344080, "results": "182", "hashOfConfig": "109"}, {"size": 5504, "mtime": 1752752344932, "results": "183", "hashOfConfig": "109"}, {"size": 32764, "mtime": 1752752344229, "results": "184", "hashOfConfig": "109"}, {"size": 37265, "mtime": 1752752344198, "results": "185", "hashOfConfig": "109"}, {"size": 31629, "mtime": 1744611214590, "results": "186", "hashOfConfig": "109"}, {"size": 2595, "mtime": 1742548498849, "results": "187", "hashOfConfig": "109"}, {"size": 19707, "mtime": 1744281526647, "results": "188", "hashOfConfig": "109"}, {"size": 24277, "mtime": 1744353361911, "results": "189", "hashOfConfig": "109"}, {"size": 13397, "mtime": 1744608017848, "results": "190", "hashOfConfig": "109"}, {"size": 22935, "mtime": 1744611214764, "results": "191", "hashOfConfig": "109"}, {"size": 6667, "mtime": 1744353215096, "results": "192", "hashOfConfig": "109"}, {"size": 12487, "mtime": 1744611083679, "results": "193", "hashOfConfig": "109"}, {"size": 491, "mtime": 1750676827665, "results": "194", "hashOfConfig": "109"}, {"size": 2196, "mtime": 1752752346260, "results": "195", "hashOfConfig": "109"}, {"size": 1297, "mtime": 1748943304957, "results": "196", "hashOfConfig": "109"}, {"size": 29616, "mtime": 1752752342697, "results": "197", "hashOfConfig": "109"}, {"size": 1639, "mtime": 1752752342697, "results": "198", "hashOfConfig": "109"}, {"size": 7943, "mtime": 1746080575211, "results": "199", "hashOfConfig": "109"}, {"size": 588, "mtime": 1747226853926, "results": "200", "hashOfConfig": "109"}, {"size": 3927, "mtime": 1747114646684, "results": "201", "hashOfConfig": "109"}, {"size": 3012, "mtime": 1752752346532, "results": "202", "hashOfConfig": "109"}, {"size": 26975, "mtime": 1748436448761, "results": "203", "hashOfConfig": "109"}, {"size": 14238, "mtime": 1749702715024, "results": "204", "hashOfConfig": "109"}, {"size": 517, "mtime": 1747226853913, "results": "205", "hashOfConfig": "109"}, {"size": 850, "mtime": 1748436448749, "results": "206", "hashOfConfig": "109"}, {"size": 6368, "mtime": 1747226922889, "results": "207", "hashOfConfig": "109"}, {"size": 2448, "mtime": 1748943304882, "results": "208", "hashOfConfig": "109"}, {"size": 1332, "mtime": 1752744999138, "results": "209", "hashOfConfig": "109"}, {"size": 2104, "mtime": 1752747877126, "results": "210", "hashOfConfig": "109"}, {"size": 1891, "mtime": 1752753343530, "results": "211", "hashOfConfig": "109"}, {"size": 7289, "mtime": 1752752349227, "results": "212", "hashOfConfig": "109"}, {"size": 5493, "mtime": 1752752349193, "results": "213", "hashOfConfig": "109"}, {"size": 876, "mtime": 1752752349202, "results": "214", "hashOfConfig": "109"}, {"size": 5767, "mtime": 1752752346119, "results": "215", "hashOfConfig": "109"}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l<PERSON><PERSON><PERSON>", {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 220, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 48, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 42, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 56, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["537", "538"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["539"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["760", "761", "762", "763", "764", "765"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["766", "767"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["788"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["867"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["868", "869", "870"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1027", "1028", "1029", "1030", "1031", "1032"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1033"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1034", "1035", "1036"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1037"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1038", "1039"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1050", "1051", "1052", "1053"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1054", "1055", "1056"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1057", "1058", "1059"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1060", "1061", "1062", "1063", "1064", "1065"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1086", "1087"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1115", "1116", "1117"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152", "1153"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1154", "1155", "1156", "1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1252", "1253"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338", "1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1417", "1418", "1419"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", ["1685"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1686", "1687", "1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803", "1804", "1805"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866", "1867"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\Tooltip.tsx", ["1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880", "1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912", "1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921", "1922", "1923"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Alertpopup.tsx", ["1924", "1925", "1926"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1927", "1928", "1929", "1930", "1931", "1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\TooltipBody.tsx", ["1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951", "1952", "1953", "1954", "1955", "1956", "1957", "1958", "1959", "1960", "1961", "1962", "1963", "1964", "1965", "1966", "1967", "1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979", "1980", "1981", "1982"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\Buttons.tsx", ["1983", "1984", "1985", "1986", "1987", "1988", "1989", "1990", "1991", "1992", "1993", "1994", "1995", "1996", "1997", "1998"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\ImageSection.tsx", ["1999", "2000", "2001", "2002", "2003", "2004", "2005", "2006", "2007", "2008", "2009", "2010"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\RTE\\RTESection.tsx", ["2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023", "2024", "2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\tooltips\\components\\ButtonSetting.tsx", ["2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["2068", "2069"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["2070", "2071", "2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080", "2081", "2082", "2083"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["2084", "2085", "2086"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Button\\index.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["2087"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["2088", "2089", "2090", "2091"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["2092", "2093", "2094", "2095"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\EnableAI.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AIOptionsPopup.tsx", ["2096", "2097", "2098"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["2099", "2100", "2101", "2102", "2103", "2104", "2105", "2106", "2107", "2108", "2109", "2110", "2111", "2112", "2113", "2114"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\ExtensionLoader.tsx", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\hooks\\useExtensionInitialization.ts", ["2115"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\ExtensionPopupLoader.tsx", ["2116"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["2117"], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\Code\\Qadpt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["2118", "2119"], [], {"ruleId": "2120", "severity": 1, "message": "2121", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2124", "line": 9, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2125", "line": 1, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 1, "column": 58, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 65}, {"ruleId": "2120", "severity": 1, "message": "2127", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2128", "line": 6, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2129", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2130", "line": 13, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 40}, {"ruleId": "2120", "severity": 1, "message": "2131", "line": 18, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2132", "line": 23, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2133", "line": 24, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2134", "line": 25, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2135", "line": 26, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2136", "line": 27, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2137", "line": 28, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2138", "line": 29, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2139", "line": 30, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2140", "line": 31, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 31, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2141", "line": 32, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 32, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2142", "line": 33, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2143", "line": 34, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2144", "line": 35, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 35, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2145", "line": 36, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2146", "line": 38, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2147", "line": 39, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 39, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2148", "line": 40, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2149", "line": 41, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 45, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2151", "line": 51, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 61, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2153", "line": 62, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 62, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2154", "line": 69, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 69, "endColumn": 6}, {"ruleId": "2120", "severity": 1, "message": "2155", "line": 70, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 70, "endColumn": 6}, {"ruleId": "2120", "severity": 1, "message": "2156", "line": 77, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 77, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2157", "line": 79, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 79, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2158", "line": 80, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2159", "line": 80, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2160", "line": 83, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2161", "line": 84, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2162", "line": 85, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2163", "line": 89, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 89, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2164", "line": 91, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2165", "line": 96, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 96, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2166", "line": 103, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2167", "line": 106, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 106, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2168", "line": 107, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 107, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2169", "line": 112, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 112, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2170", "line": 112, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 112, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2171", "line": 115, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 115, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2172", "line": 122, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 122, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2173", "line": 135, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 135, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2174", "line": 198, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 198, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2175", "line": 215, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 215, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2176", "line": 223, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 223, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2177", "line": 379, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 379, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2178", "line": 414, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 414, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2179", "line": 416, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 416, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2180", "line": 418, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 418, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2181", "line": 433, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 433, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2182", "line": 434, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 434, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2183", "line": 436, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 436, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2184", "line": 439, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 439, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2185", "line": 443, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 443, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2186", "line": 444, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 444, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2187", "line": 455, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 455, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2188", "line": 456, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 456, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2189", "line": 457, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 457, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2190", "line": 459, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 459, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2191", "line": 459, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 459, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2192", "line": 464, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 464, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2193", "line": 464, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 464, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2194", "line": 466, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 466, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2195", "line": 466, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 466, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2196", "line": 469, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 469, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2197", "line": 469, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 469, "endColumn": 40}, {"ruleId": "2120", "severity": 1, "message": "2198", "line": 470, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 470, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2199", "line": 475, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 475, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2200", "line": 475, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 475, "endColumn": 50}, {"ruleId": "2120", "severity": 1, "message": "2201", "line": 482, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 482, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2202", "line": 482, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 482, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2203", "line": 484, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 484, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2204", "line": 484, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 484, "endColumn": 41}, {"ruleId": "2120", "severity": 1, "message": "2205", "line": 486, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 486, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2206", "line": 486, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 486, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2207", "line": 500, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 500, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2208", "line": 501, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 501, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2209", "line": 501, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 501, "endColumn": 58}, {"ruleId": "2120", "severity": 1, "message": "2210", "line": 504, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 504, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2211", "line": 504, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 504, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2212", "line": 505, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 505, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2213", "line": 505, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 505, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2214", "line": 506, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 506, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2215", "line": 506, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 506, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2216", "line": 515, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 515, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2217", "line": 516, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 516, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2218", "line": 522, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 522, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2219", "line": 526, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 526, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2220", "line": 526, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 526, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2221", "line": 529, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 529, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2222", "line": 529, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 529, "endColumn": 32}, {"ruleId": "2223", "severity": 1, "message": "2224", "line": 568, "column": 5, "nodeType": "2225", "endLine": 568, "endColumn": 27, "suggestions": "2226"}, {"ruleId": "2120", "severity": 1, "message": "2227", "line": 578, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 578, "endColumn": 6}, {"ruleId": "2120", "severity": 1, "message": "2228", "line": 579, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 579, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 580, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 580, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2230", "line": 582, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 582, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2231", "line": 583, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 583, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2232", "line": 588, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 588, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2233", "line": 589, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 589, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2234", "line": 624, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 624, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2235", "line": 625, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 625, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2236", "line": 626, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 626, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2237", "line": 634, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 634, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 636, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 636, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2239", "line": 637, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 637, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2240", "line": 638, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 638, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2241", "line": 639, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 639, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2242", "line": 644, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 644, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2243", "line": 646, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 646, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2244", "line": 648, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 648, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2245", "line": 658, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 658, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2246", "line": 659, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 659, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2247", "line": 662, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 662, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2248", "line": 666, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 666, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2249", "line": 668, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 668, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2250", "line": 669, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 669, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2251", "line": 671, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 671, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2252", "line": 678, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 678, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2253", "line": 679, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 679, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2254", "line": 684, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 684, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2255", "line": 685, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 685, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2256", "line": 686, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 686, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2257", "line": 696, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 696, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2258", "line": 700, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 700, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2259", "line": 704, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 704, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2260", "line": 706, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 706, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2261", "line": 708, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 708, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2262", "line": 709, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 709, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2263", "line": 714, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 714, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2264", "line": 715, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 715, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2265", "line": 726, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 726, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2266", "line": 732, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 732, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2267", "line": 733, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 733, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2268", "line": 737, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 737, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2269", "line": 749, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 749, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2270", "line": 774, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 774, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2271", "line": 785, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 785, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2272", "line": 790, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 790, "endColumn": 42}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 795, "column": 22, "nodeType": "2275", "messageId": "2276", "endLine": 795, "endColumn": 24}, {"ruleId": "2223", "severity": 1, "message": "2277", "line": 881, "column": 5, "nodeType": "2225", "endLine": 881, "endColumn": 46, "suggestions": "2278"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 881, "column": 6, "nodeType": "2280", "endLine": 881, "endColumn": 29}, {"ruleId": "2223", "severity": 1, "message": "2281", "line": 899, "column": 5, "nodeType": "2225", "endLine": 899, "endColumn": 18, "suggestions": "2282"}, {"ruleId": "2120", "severity": 1, "message": "2283", "line": 901, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 901, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2284", "line": 902, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 902, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2285", "line": 923, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 923, "endColumn": 24}, {"ruleId": "2223", "severity": 1, "message": "2286", "line": 947, "column": 8, "nodeType": "2287", "endLine": 949, "endColumn": 3}, {"ruleId": "2223", "severity": 1, "message": "2288", "line": 984, "column": 5, "nodeType": "2225", "endLine": 992, "endColumn": 3, "suggestions": "2289"}, {"ruleId": "2223", "severity": 1, "message": "2290", "line": 1020, "column": 5, "nodeType": "2225", "endLine": 1043, "endColumn": 3, "suggestions": "2291"}, {"ruleId": "2223", "severity": 1, "message": "2292", "line": 1161, "column": 5, "nodeType": "2225", "endLine": 1161, "endColumn": 39, "suggestions": "2293"}, {"ruleId": "2120", "severity": 1, "message": "2294", "line": 1278, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 1278, "endColumn": 24}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1443, "column": 25, "nodeType": "2275", "messageId": "2276", "endLine": 1443, "endColumn": 27}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1450, "column": 25, "nodeType": "2275", "messageId": "2276", "endLine": 1450, "endColumn": 27}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1450, "column": 53, "nodeType": "2275", "messageId": "2276", "endLine": 1450, "endColumn": 55}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1453, "column": 26, "nodeType": "2275", "messageId": "2276", "endLine": 1453, "endColumn": 28}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1453, "column": 58, "nodeType": "2275", "messageId": "2276", "endLine": 1453, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2296", "line": 1584, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1584, "endColumn": 33}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 1661, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 1661, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2297", "line": 1808, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1808, "endColumn": 30}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 2070, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 2070, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 2242, "column": 25, "nodeType": "2275", "messageId": "2276", "endLine": 2242, "endColumn": 27}, {"ruleId": "2223", "severity": 1, "message": "2298", "line": 2272, "column": 5, "nodeType": "2225", "endLine": 2272, "endColumn": 18, "suggestions": "2299"}, {"ruleId": "2120", "severity": 1, "message": "2300", "line": 2329, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 2329, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2301", "line": 2336, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 2336, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2302", "line": 2339, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 2339, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2303", "line": 2339, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 2339, "endColumn": 48}, {"ruleId": "2120", "severity": 1, "message": "2304", "line": 2731, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 2731, "endColumn": 27}, {"ruleId": "2223", "severity": 1, "message": "2305", "line": 2766, "column": 5, "nodeType": "2225", "endLine": 2766, "endColumn": 38, "suggestions": "2306"}, {"ruleId": "2120", "severity": 1, "message": "2307", "line": 2783, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 2783, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2308", "line": 2817, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 2817, "endColumn": 18}, {"ruleId": "2223", "severity": 1, "message": "2309", "line": 3201, "column": 4, "nodeType": "2225", "endLine": 3201, "endColumn": 18, "suggestions": "2310"}, {"ruleId": "2120", "severity": 1, "message": "2311", "line": 3538, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3538, "endColumn": 33}, {"ruleId": "2223", "severity": 1, "message": "2312", "line": 3612, "column": 16, "nodeType": "2280", "endLine": 3612, "endColumn": 37}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 3615, "column": 49, "nodeType": "2275", "messageId": "2276", "endLine": 3615, "endColumn": 51}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 3619, "column": 50, "nodeType": "2275", "messageId": "2276", "endLine": 3619, "endColumn": 52}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 3625, "column": 51, "nodeType": "2275", "messageId": "2276", "endLine": 3625, "endColumn": 53}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 3632, "column": 51, "nodeType": "2275", "messageId": "2276", "endLine": 3632, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2313", "line": 3853, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3853, "endColumn": 23}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 3861, "column": 30, "nodeType": "2275", "messageId": "2276", "endLine": 3861, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2314", "line": 3874, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 3874, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2315", "line": 3888, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 3888, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2316", "line": 3888, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 3888, "endColumn": 52}, {"ruleId": "2120", "severity": 1, "message": "2317", "line": 4002, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4002, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2318", "line": 4004, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 4004, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2319", "line": 4008, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4008, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2320", "line": 4027, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 4027, "endColumn": 26}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 4048, "column": 66, "nodeType": "2275", "messageId": "2276", "endLine": 4048, "endColumn": 68}, {"ruleId": "2223", "severity": 1, "message": "2321", "line": 4055, "column": 5, "nodeType": "2225", "endLine": 4062, "endColumn": 3, "suggestions": "2322"}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 4397, "column": 17, "nodeType": "2275", "messageId": "2276", "endLine": 4397, "endColumn": 19}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 4651, "column": 21, "nodeType": "2275", "messageId": "2276", "endLine": 4651, "endColumn": 23}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 4659, "column": 21, "nodeType": "2275", "messageId": "2276", "endLine": 4659, "endColumn": 23}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 4672, "column": 15, "nodeType": "2275", "messageId": "2276", "endLine": 4672, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2323", "line": 4969, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 4969, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2324", "line": 4980, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 4980, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2325", "line": 4981, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 4981, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "2326", "line": 4987, "column": 5, "nodeType": "2225", "endLine": 4987, "endColumn": 62, "suggestions": "2327"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 4987, "column": 6, "nodeType": "2328", "endLine": 4987, "endColumn": 48}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 5010, "column": 25, "nodeType": "2275", "messageId": "2276", "endLine": 5010, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2329", "line": 5014, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5014, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2330", "line": 5037, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 5037, "endColumn": 23}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 5112, "column": 25, "nodeType": "2275", "messageId": "2276", "endLine": 5112, "endColumn": 27}, {"ruleId": "2223", "severity": 1, "message": "2331", "line": 5145, "column": 5, "nodeType": "2225", "endLine": 5145, "endColumn": 22, "suggestions": "2332"}, {"ruleId": "2120", "severity": 1, "message": "2333", "line": 5147, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 5147, "endColumn": 18}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 5149, "column": 40, "nodeType": "2275", "messageId": "2276", "endLine": 5149, "endColumn": 42}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 5214, "column": 69, "nodeType": "2275", "messageId": "2276", "endLine": 5214, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2334", "line": 5264, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 5264, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2335", "line": 5265, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 5265, "endColumn": 22}, {"ruleId": "2223", "severity": 1, "message": "2336", "line": 5295, "column": 5, "nodeType": "2225", "endLine": 5295, "endColumn": 38, "suggestions": "2337"}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 5298, "column": 40, "nodeType": "2275", "messageId": "2276", "endLine": 5298, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2334", "line": 5304, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5304, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2335", "line": 5305, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5305, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "2338", "line": 5311, "column": 5, "nodeType": "2225", "endLine": 5311, "endColumn": 106, "suggestions": "2339"}, {"ruleId": "2223", "severity": 1, "message": "2340", "line": 5424, "column": 5, "nodeType": "2225", "endLine": 5424, "endColumn": 17, "suggestions": "2341"}, {"ruleId": "2342", "severity": 1, "message": "2343", "line": 6026, "column": 80, "nodeType": "2344", "messageId": "2345", "endLine": 6026, "endColumn": 81, "suggestions": "2346"}, {"ruleId": "2120", "severity": 1, "message": "2347", "line": 6256, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 6256, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2348", "line": 2, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2349", "line": 7, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2350", "line": 7, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2351", "line": 93, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 93, "endColumn": 25}, {"ruleId": "2223", "severity": 1, "message": "2352", "line": 98, "column": 6, "nodeType": "2225", "endLine": 98, "endColumn": 8, "suggestions": "2353"}, {"ruleId": "2120", "severity": 1, "message": "2354", "line": 120, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 120, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2355", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2356", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 3, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2358", "line": 8, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2359", "line": 9, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2360", "line": 13, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 46}, {"ruleId": "2361", "severity": 1, "message": "2362", "line": 2377, "column": 5, "nodeType": "2363", "messageId": "2276", "endLine": 2377, "endColumn": 17}, {"ruleId": "2361", "severity": 1, "message": "2364", "line": 2378, "column": 5, "nodeType": "2363", "messageId": "2276", "endLine": 2378, "endColumn": 20}, {"ruleId": "2361", "severity": 1, "message": "2365", "line": 2709, "column": 5, "nodeType": "2363", "messageId": "2276", "endLine": 2709, "endColumn": 24}, {"ruleId": "2361", "severity": 1, "message": "2366", "line": 2886, "column": 5, "nodeType": "2363", "messageId": "2276", "endLine": 2886, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2367", "line": 3590, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 3590, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2367", "line": 3789, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 3789, "endColumn": 28}, {"ruleId": "2361", "severity": 1, "message": "2368", "line": 5354, "column": 5, "nodeType": "2363", "messageId": "2276", "endLine": 5354, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2369", "line": 5417, "column": 14, "nodeType": "2122", "messageId": "2123", "endLine": 5417, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2370", "line": 6501, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 6501, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2370", "line": 6527, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 6527, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2370", "line": 6533, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 6533, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2370", "line": 6548, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 6548, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2369", "line": 7325, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 7325, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2369", "line": 7569, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 7569, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2369", "line": 7740, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 7740, "endColumn": 16}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 8405, "column": 66, "nodeType": "2275", "messageId": "2276", "endLine": 8405, "endColumn": 68}, {"ruleId": "2120", "severity": 1, "message": "2371", "line": 1, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2372", "line": 2, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 56}, {"ruleId": "2120", "severity": 1, "message": "2373", "line": 18, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2374", "line": 19, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 20, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2376", "line": 21, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2377", "line": 24, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2378", "line": 25, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2379", "line": 26, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2380", "line": 31, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 31, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2381", "line": 38, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 55}, {"ruleId": "2120", "severity": 1, "message": "2382", "line": 38, "column": 63, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 70}, {"ruleId": "2120", "severity": 1, "message": "2383", "line": 46, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2384", "line": 48, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2385", "line": 90, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 90, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2386", "line": 91, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2387", "line": 97, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 97, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2388", "line": 98, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 98, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2389", "line": 102, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 102, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 106, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 106, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2391", "line": 110, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 110, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2392", "line": 111, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 111, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2393", "line": 112, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 112, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2394", "line": 113, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 113, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2395", "line": 114, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 114, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 117, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 117, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2397", "line": 118, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 118, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2398", "line": 160, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 160, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2399", "line": 170, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 170, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2400", "line": 178, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 178, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2401", "line": 179, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 179, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2402", "line": 180, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 180, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2403", "line": 181, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 181, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2404", "line": 182, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 182, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2405", "line": 203, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 203, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2406", "line": 207, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 207, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2407", "line": 453, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 453, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "2408", "line": 545, "column": 5, "nodeType": "2225", "endLine": 545, "endColumn": 60, "suggestions": "2409"}, {"ruleId": "2120", "severity": 1, "message": "2410", "line": 559, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 559, "endColumn": 22}, {"ruleId": "2223", "severity": 1, "message": "2411", "line": 579, "column": 5, "nodeType": "2225", "endLine": 579, "endColumn": 60, "suggestions": "2412"}, {"ruleId": "2223", "severity": 1, "message": "2413", "line": 596, "column": 4, "nodeType": "2225", "endLine": 596, "endColumn": 6, "suggestions": "2414"}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2160", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2161", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2158", "line": 5, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2159", "line": 5, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 55}, {"ruleId": "2120", "severity": 1, "message": "2416", "line": 6, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2170", "line": 11, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2417", "line": 16, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2418", "line": 20, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2419", "line": 23, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2420", "line": 24, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2421", "line": 25, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2203", "line": 34, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2204", "line": 34, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2135", "line": 6, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2422", "line": 9, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2423", "line": 12, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2424", "line": 25, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2425", "line": 48, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2426", "line": 49, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2242", "line": 50, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 51, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2428", "line": 52, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2259", "line": 53, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2429", "line": 54, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2430", "line": 55, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 55, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2260", "line": 56, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2431", "line": 57, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 57, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2432", "line": 58, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2433", "line": 59, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2434", "line": 60, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2435", "line": 61, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2392", "line": 62, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 62, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2395", "line": 63, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 63, "endColumn": 23}, {"ruleId": "2223", "severity": 1, "message": "2436", "line": 75, "column": 5, "nodeType": "2225", "endLine": 75, "endColumn": 7, "suggestions": "2437"}, {"ruleId": "2223", "severity": 1, "message": "2438", "line": 93, "column": 5, "nodeType": "2225", "endLine": 93, "endColumn": 28, "suggestions": "2439"}, {"ruleId": "2223", "severity": 1, "message": "2440", "line": 104, "column": 5, "nodeType": "2225", "endLine": 104, "endColumn": 48, "suggestions": "2441"}, {"ruleId": "2120", "severity": 1, "message": "2442", "line": 183, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 183, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2444", "line": 1, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2445", "line": 3, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2446", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2447", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2448", "line": 13, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2449", "line": 15, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2450", "line": 78, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2451", "line": 80, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2452", "line": 81, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 81, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2453", "line": 82, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2454", "line": 83, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2237", "line": 87, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 87, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2455", "line": 88, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2456", "line": 90, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 90, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2239", "line": 91, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2240", "line": 92, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 93, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 93, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 103, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 19}, {"ruleId": "2223", "severity": 1, "message": "2457", "line": 208, "column": 7, "nodeType": "2225", "endLine": 208, "endColumn": 9, "suggestions": "2458"}, {"ruleId": "2223", "severity": 1, "message": "2459", "line": 243, "column": 7, "nodeType": "2225", "endLine": 243, "endColumn": 29, "suggestions": "2460"}, {"ruleId": "2223", "severity": 1, "message": "2461", "line": 248, "column": 7, "nodeType": "2225", "endLine": 248, "endColumn": 18, "suggestions": "2462"}, {"ruleId": "2223", "severity": 1, "message": "2463", "line": 291, "column": 7, "nodeType": "2225", "endLine": 291, "endColumn": 72, "suggestions": "2464"}, {"ruleId": "2120", "severity": 1, "message": "2465", "line": 330, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 330, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2466", "line": 333, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 333, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2467", "line": 462, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 462, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2155", "line": 3, "column": 65, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 69}, {"ruleId": "2120", "severity": 1, "message": "2468", "line": 6, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2469", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2470", "line": 20, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2471", "line": 82, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2472", "line": 83, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2473", "line": 83, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2474", "line": 84, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2475", "line": 84, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2476", "line": 88, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2477", "line": 88, "column": 45, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 62}, {"ruleId": "2120", "severity": 1, "message": "2478", "line": 91, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2479", "line": 92, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2210", "line": 93, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 93, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2211", "line": 94, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 94, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2212", "line": 95, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 95, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2213", "line": 96, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 96, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2214", "line": 97, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 97, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2215", "line": 98, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 98, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2480", "line": 99, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 99, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 100, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2482", "line": 101, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 101, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 102, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 102, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2465", "line": 103, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2483", "line": 105, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2484", "line": 106, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 106, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2485", "line": 113, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 113, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2486", "line": 114, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 114, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2487", "line": 116, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 116, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2488", "line": 117, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 117, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2489", "line": 118, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 118, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2490", "line": 119, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 119, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2491", "line": 120, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 120, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2492", "line": 121, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 121, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2493", "line": 130, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 130, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2494", "line": 135, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 135, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2386", "line": 137, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 137, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2393", "line": 138, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 138, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2495", "line": 139, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 139, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2496", "line": 142, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 142, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2391", "line": 143, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 143, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2497", "line": 144, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 144, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "2498", "line": 168, "column": 5, "nodeType": "2225", "endLine": 168, "endColumn": 45, "suggestions": "2499"}, {"ruleId": "2120", "severity": 1, "message": "2500", "line": 219, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 219, "endColumn": 29}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 239, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 239, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "2501", "line": 313, "column": 7, "nodeType": "2225", "endLine": 313, "endColumn": 42, "suggestions": "2502"}, {"ruleId": "2120", "severity": 1, "message": "2503", "line": 337, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 337, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2504", "line": 338, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 338, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2505", "line": 486, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 486, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2506", "line": 489, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 489, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2507", "line": 498, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 498, "endColumn": 31}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 812, "column": 26, "nodeType": "2275", "messageId": "2276", "endLine": 812, "endColumn": 28}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 848, "column": 26, "nodeType": "2275", "messageId": "2276", "endLine": 848, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2508", "line": 1032, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1032, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2509", "line": 1036, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1036, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2510", "line": 1040, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1040, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2511", "line": 1044, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1044, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2512", "line": 1048, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1048, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2513", "line": 1052, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1052, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2514", "line": 1056, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1056, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2515", "line": 1060, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1060, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2516", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2517", "line": 6, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2518", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2519", "line": 7, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2520", "line": 8, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2521", "line": 9, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2239", "line": 13, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2240", "line": 14, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 15, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2522", "line": 17, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2523", "line": 18, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2524", "line": 18, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2525", "line": 19, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2526", "line": 96, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 96, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2527", "line": 97, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 97, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2528", "line": 100, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2529", "line": 101, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 101, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2530", "line": 109, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 109, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2531", "line": 129, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 129, "endColumn": 16}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 176, "column": 45, "nodeType": "2275", "messageId": "2276", "endLine": 176, "endColumn": 47}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 176, "column": 104, "nodeType": "2275", "messageId": "2276", "endLine": 176, "endColumn": 106}, {"ruleId": "2120", "severity": 1, "message": "2532", "line": 111, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 111, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2492", "line": 152, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 152, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2251", "line": 153, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 153, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2533", "line": 159, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 159, "endColumn": 23}, {"ruleId": "2534", "severity": 1, "message": "2535", "line": 225, "column": 25, "nodeType": "2122", "messageId": "2536", "endLine": 225, "endColumn": 34, "suggestions": "2537"}, {"ruleId": "2223", "severity": 1, "message": "2538", "line": 231, "column": 5, "nodeType": "2225", "endLine": 231, "endColumn": 12, "suggestions": "2539"}, {"ruleId": "2223", "severity": 1, "message": "2540", "line": 237, "column": 5, "nodeType": "2225", "endLine": 237, "endColumn": 21, "suggestions": "2541"}, {"ruleId": "2223", "severity": 1, "message": "2542", "line": 472, "column": 5, "nodeType": "2225", "endLine": 472, "endColumn": 70, "suggestions": "2543"}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 547, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 547, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 548, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 548, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 549, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 549, "endColumn": 26}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 550, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 550, "endColumn": 26}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 554, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 554, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 555, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 555, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 556, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 556, "endColumn": 26}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 557, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 557, "endColumn": 26}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 561, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 561, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 562, "column": 24, "nodeType": "2275", "messageId": "2276", "endLine": 562, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "2544", "line": 582, "column": 5, "nodeType": "2225", "endLine": 582, "endColumn": 64, "suggestions": "2545"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 582, "column": 6, "nodeType": "2328", "endLine": 582, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2546", "line": 591, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 591, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2547", "line": 605, "column": 5, "nodeType": "2225", "endLine": 605, "endColumn": 47, "suggestions": "2548"}, {"ruleId": "2120", "severity": 1, "message": "2546", "line": 614, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 614, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2547", "line": 627, "column": 5, "nodeType": "2225", "endLine": 627, "endColumn": 47, "suggestions": "2549"}, {"ruleId": "2223", "severity": 1, "message": "2550", "line": 1021, "column": 17, "nodeType": "2122", "endLine": 1021, "endColumn": 32}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 1227, "column": 43, "nodeType": "2275", "messageId": "2276", "endLine": 1227, "endColumn": 45}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 1232, "column": 78, "nodeType": "2275", "messageId": "2276", "endLine": 1232, "endColumn": 80}, {"ruleId": "2120", "severity": 1, "message": "2380", "line": 2, "column": 60, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 73}, {"ruleId": "2120", "severity": 1, "message": "2516", "line": 3, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2551", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2552", "line": 9, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2553", "line": 133, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 133, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2554", "line": 134, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 134, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2555", "line": 135, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 135, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2556", "line": 135, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 135, "endColumn": 52}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 137, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 137, "endColumn": 19}, {"ruleId": "2223", "severity": 1, "message": "2557", "line": 163, "column": 8, "nodeType": "2225", "endLine": 163, "endColumn": 10, "suggestions": "2558"}, {"ruleId": "2120", "severity": 1, "message": "2559", "line": 299, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 299, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2560", "line": 342, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 342, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2561", "line": 343, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 343, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2562", "line": 344, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 344, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2563", "line": 346, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 346, "endColumn": 20}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 465, "column": 22, "nodeType": "2275", "messageId": "2276", "endLine": 465, "endColumn": 24}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 465, "column": 53, "nodeType": "2275", "messageId": "2276", "endLine": 465, "endColumn": 55}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 465, "column": 89, "nodeType": "2275", "messageId": "2276", "endLine": 465, "endColumn": 91}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 465, "column": 125, "nodeType": "2275", "messageId": "2276", "endLine": 465, "endColumn": 127}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 467, "column": 29, "nodeType": "2275", "messageId": "2276", "endLine": 467, "endColumn": 31}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 467, "column": 56, "nodeType": "2275", "messageId": "2276", "endLine": 467, "endColumn": 58}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 467, "column": 88, "nodeType": "2275", "messageId": "2276", "endLine": 467, "endColumn": 90}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 467, "column": 120, "nodeType": "2275", "messageId": "2276", "endLine": 467, "endColumn": 122}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 469, "column": 29, "nodeType": "2275", "messageId": "2276", "endLine": 469, "endColumn": 31}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 469, "column": 64, "nodeType": "2275", "messageId": "2276", "endLine": 469, "endColumn": 66}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 1, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2350", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2446", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2168", "line": 7, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2564", "line": 43, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 34}, {"ruleId": "2223", "severity": 1, "message": "2565", "line": 63, "column": 21, "nodeType": "2566", "endLine": 63, "endColumn": 111}, {"ruleId": "2120", "severity": 1, "message": "2567", "line": 1, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2157", "line": 1, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2568", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2569", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 40}, {"ruleId": "2120", "severity": 1, "message": "2570", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2570", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 29, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2572", "line": 60, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 67, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 67, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2574", "line": 69, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 69, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2575", "line": 91, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 22}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 100, "column": 34, "nodeType": "2275", "messageId": "2276", "endLine": 100, "endColumn": 36}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 148, "column": 44, "nodeType": "2275", "messageId": "2276", "endLine": 148, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2576", "line": 167, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 167, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2577", "line": 291, "column": 5, "nodeType": "2225", "endLine": 291, "endColumn": 50, "suggestions": "2578"}, {"ruleId": "2223", "severity": 1, "message": "2577", "line": 307, "column": 5, "nodeType": "2225", "endLine": 307, "endColumn": 18, "suggestions": "2579"}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2581", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2582", "line": 5, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2583", "line": 10, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2585", "line": 59, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2586", "line": 61, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2588", "line": 6, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 22}, {"ruleId": "2223", "severity": 1, "message": "2589", "line": 291, "column": 8, "nodeType": "2225", "endLine": 291, "endColumn": 24, "suggestions": "2590"}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2591", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2592", "line": 2, "column": 41, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 58}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 72, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 88}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 90, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 96}, {"ruleId": "2120", "severity": 1, "message": "2594", "line": 8, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 6, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 9, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 10, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 11, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 12, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2598", "line": 17, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2497", "line": 33, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2599", "line": 35, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 35, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 36, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 37, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 37, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2602", "line": 38, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2391", "line": 40, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 46, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2603", "line": 53, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2604", "line": 54, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2605", "line": 55, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 55, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2606", "line": 84, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2607", "line": 88, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2608", "line": 93, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 93, "endColumn": 33}, {"ruleId": "2223", "severity": 1, "message": "2609", "line": 193, "column": 5, "nodeType": "2225", "endLine": 193, "endColumn": 30, "suggestions": "2610"}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2611", "line": 4, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 65}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 4, "column": 67, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 75}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2612", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2425", "line": 29, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2613", "line": 30, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 33, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2614", "line": 41, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2615", "line": 42, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2616", "line": 43, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2617", "line": 44, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2618", "line": 48, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2619", "line": 48, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2620", "line": 49, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2621", "line": 50, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2622", "line": 53, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2623", "line": 54, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2624", "line": 54, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 32}, {"ruleId": "2223", "severity": 1, "message": "2625", "line": 62, "column": 5, "nodeType": "2225", "endLine": 62, "endColumn": 7, "suggestions": "2626"}, {"ruleId": "2120", "severity": 1, "message": "2627", "line": 81, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 81, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2628", "line": 85, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2629", "line": 112, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 112, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2630", "line": 120, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 120, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2631", "line": 124, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 124, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2632", "line": 138, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 138, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2633", "line": 141, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 141, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 3, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 5}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 4, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 9, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 56, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 65}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 2, "column": 67, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 75}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 2, "column": 77, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 83}, {"ruleId": "2120", "severity": 1, "message": "2611", "line": 13, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2635", "line": 44, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 48}, {"ruleId": "2120", "severity": 1, "message": "2559", "line": 56, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 39}, {"ruleId": "2120", "severity": 1, "message": "2636", "line": 65, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 41}, {"ruleId": "2120", "severity": 1, "message": "2637", "line": 71, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 71, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 3, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 3, "column": 56, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 63}, {"ruleId": "2120", "severity": 1, "message": "2392", "line": 11, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2478", "line": 12, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2479", "line": 13, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2210", "line": 14, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2212", "line": 16, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2213", "line": 17, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2214", "line": 18, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2215", "line": 19, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2480", "line": 20, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 21, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2482", "line": 22, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 23, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2639", "line": 35, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 35, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2450", "line": 37, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 37, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2640", "line": 39, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 39, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2641", "line": 43, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2642", "line": 47, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2643", "line": 47, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2644", "line": 48, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2645", "line": 48, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2646", "line": 49, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2647", "line": 49, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2648", "line": 50, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2649", "line": 50, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 52}, {"ruleId": "2120", "severity": 1, "message": "2650", "line": 51, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2651", "line": 51, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2652", "line": 3, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2653", "line": 4, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2654", "line": 5, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2655", "line": 6, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2656", "line": 9, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2373", "line": 17, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2374", "line": 18, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 19, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2376", "line": 20, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2377", "line": 23, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2378", "line": 24, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2379", "line": 25, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 26, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2517", "line": 43, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2657", "line": 44, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2658", "line": 48, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2659", "line": 49, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2660", "line": 51, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2661", "line": 52, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2662", "line": 53, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2663", "line": 54, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2392", "line": 56, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2425", "line": 57, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 57, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2613", "line": 58, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2386", "line": 60, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2387", "line": 66, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 66, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2388", "line": 67, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 67, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2389", "line": 73, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 73, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2664", "line": 75, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 75, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2239", "line": 78, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2240", "line": 79, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 79, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 80, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2665", "line": 81, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 81, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2666", "line": 82, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2667", "line": 83, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2402", "line": 85, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2400", "line": 87, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 87, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2404", "line": 89, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 89, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2668", "line": 90, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 90, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2397", "line": 92, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 99, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 99, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2480", "line": 99, "column": 22, "nodeType": "2122", "messageId": "2123", "endLine": 99, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 100, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2482", "line": 100, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2669", "line": 101, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 101, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2670", "line": 102, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 102, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2671", "line": 103, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2672", "line": 103, "column": 14, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2398", "line": 104, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 104, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2673", "line": 104, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 104, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2563", "line": 105, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2674", "line": 105, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2605", "line": 116, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 116, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2675", "line": 116, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 116, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2676", "line": 123, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 123, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2677", "line": 123, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 123, "endColumn": 44}, {"ruleId": "2223", "severity": 1, "message": "2678", "line": 146, "column": 5, "nodeType": "2225", "endLine": 146, "endColumn": 60, "suggestions": "2679"}, {"ruleId": "2120", "severity": 1, "message": "2680", "line": 149, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 149, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2681", "line": 161, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 161, "endColumn": 24}, {"ruleId": "2223", "severity": 1, "message": "2682", "line": 165, "column": 5, "nodeType": "2225", "endLine": 165, "endColumn": 60, "suggestions": "2683"}, {"ruleId": "2120", "severity": 1, "message": "2684", "line": 167, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 167, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2405", "line": 201, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 201, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2406", "line": 205, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 205, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2685", "line": 11, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2516", "line": 14, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2686", "line": 67, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 67, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2687", "line": 180, "column": 86, "nodeType": "2122", "messageId": "2123", "endLine": 180, "endColumn": 101}, {"ruleId": "2120", "severity": 1, "message": "2688", "line": 184, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 184, "endColumn": 24}, {"ruleId": "2223", "severity": 1, "message": "2689", "line": 563, "column": 5, "nodeType": "2225", "endLine": 563, "endColumn": 40, "suggestions": "2690"}, {"ruleId": "2223", "severity": 1, "message": "2691", "line": 586, "column": 6, "nodeType": "2225", "endLine": 586, "endColumn": 42, "suggestions": "2692"}, {"ruleId": "2223", "severity": 1, "message": "2693", "line": 600, "column": 6, "nodeType": "2225", "endLine": 600, "endColumn": 50, "suggestions": "2694"}, {"ruleId": "2223", "severity": 1, "message": "2695", "line": 877, "column": 5, "nodeType": "2225", "endLine": 877, "endColumn": 160, "suggestions": "2696"}, {"ruleId": "2223", "severity": 1, "message": "2697", "line": 945, "column": 5, "nodeType": "2225", "endLine": 945, "endColumn": 110, "suggestions": "2698"}, {"ruleId": "2223", "severity": 1, "message": "2699", "line": 975, "column": 5, "nodeType": "2225", "endLine": 975, "endColumn": 34, "suggestions": "2700"}, {"ruleId": "2223", "severity": 1, "message": "2701", "line": 993, "column": 5, "nodeType": "2225", "endLine": 993, "endColumn": 34, "suggestions": "2702"}, {"ruleId": "2223", "severity": 1, "message": "2701", "line": 1007, "column": 5, "nodeType": "2225", "endLine": 1007, "endColumn": 34, "suggestions": "2703"}, {"ruleId": "2223", "severity": 1, "message": "2701", "line": 1010, "column": 5, "nodeType": "2225", "endLine": 1010, "endColumn": 40, "suggestions": "2704"}, {"ruleId": "2120", "severity": 1, "message": "2705", "line": 1220, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1220, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2706", "line": 1223, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 1223, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 1, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2713", "line": 21, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2248", "line": 22, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2714", "line": 24, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2244", "line": 25, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2715", "line": 26, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2716", "line": 27, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2504", "line": 83, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 92, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 102}, {"ruleId": "2120", "severity": 1, "message": "2717", "line": 74, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 74, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 9, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2480", "line": 9, "column": 22, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 10, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2482", "line": 10, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2670", "line": 12, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2718", "line": 12, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2671", "line": 13, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2672", "line": 13, "column": 14, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2563", "line": 15, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2605", "line": 16, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2719", "line": 28, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2720", "line": 1, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2721", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2425", "line": 14, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2431", "line": 15, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2426", "line": 16, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2395", "line": 17, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2435", "line": 20, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2722", "line": 27, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 5, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2723", "line": 6, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2724", "line": 6, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 7, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2434", "line": 26, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2395", "line": 29, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2725", "line": 32, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 32, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2726", "line": 145, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 145, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2727", "line": 146, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 146, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2720", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2656", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 2, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 39, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2728", "line": 2, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 58}, {"ruleId": "2120", "severity": 1, "message": "2380", "line": 2, "column": 60, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 73}, {"ruleId": "2120", "severity": 1, "message": "2729", "line": 2, "column": 74, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 84}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2730", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2425", "line": 98, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 98, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2431", "line": 99, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 99, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2426", "line": 100, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2242", "line": 101, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 101, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 102, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 102, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2428", "line": 103, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2259", "line": 104, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 104, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2260", "line": 105, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2731", "line": 110, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 110, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 113, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 113, "endColumn": 29}, {"ruleId": "2223", "severity": 1, "message": "2732", "line": 172, "column": 12, "nodeType": "2225", "endLine": 172, "endColumn": 35, "suggestions": "2733"}, {"ruleId": "2120", "severity": 1, "message": "2734", "line": 1, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 5, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 5}, {"ruleId": "2120", "severity": 1, "message": "2735", "line": 6, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2728", "line": 10, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2685", "line": 12, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 13, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2736", "line": 17, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2737", "line": 19, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 33, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 33, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 44}, {"ruleId": "2738", "severity": 1, "message": "2739", "line": 95, "column": 2, "nodeType": "2740", "messageId": "2741", "endLine": 111, "endColumn": 4}, {"ruleId": "2120", "severity": 1, "message": "2742", "line": 132, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 132, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2743", "line": 135, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 135, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 136, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 136, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 137, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 137, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2744", "line": 139, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 139, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2478", "line": 140, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 140, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2745", "line": 141, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 141, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2746", "line": 144, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 144, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2747", "line": 145, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 145, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2748", "line": 146, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 146, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2749", "line": 147, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 147, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2750", "line": 148, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 148, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2751", "line": 149, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 149, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2487", "line": 150, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 150, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2486", "line": 151, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 151, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2485", "line": 152, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 152, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2752", "line": 155, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 155, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2688", "line": 158, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 158, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2753", "line": 159, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 159, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2754", "line": 160, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 160, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 161, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 161, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 162, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 162, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2755", "line": 165, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 165, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2756", "line": 166, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 166, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2533", "line": 168, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 168, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2572", "line": 173, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 173, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2251", "line": 174, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 174, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2757", "line": 185, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 185, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2758", "line": 185, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 185, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2759", "line": 187, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 187, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2760", "line": 187, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 187, "endColumn": 44}, {"ruleId": "2761", "severity": 1, "message": "2762", "line": 349, "column": 5, "nodeType": "2763", "messageId": "2764", "endLine": 349, "endColumn": 52}, {"ruleId": "2120", "severity": 1, "message": "2765", "line": 504, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 504, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2766", "line": 511, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 511, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2767", "line": 668, "column": 5, "nodeType": "2225", "endLine": 668, "endColumn": 100, "suggestions": "2768"}, {"ruleId": "2223", "severity": 1, "message": "2769", "line": 775, "column": 5, "nodeType": "2225", "endLine": 775, "endColumn": 22, "suggestions": "2770"}, {"ruleId": "2120", "severity": 1, "message": "2771", "line": 876, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 876, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2516", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2239", "line": 11, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2240", "line": 12, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2238", "line": 13, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2522", "line": 21, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2523", "line": 22, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2524", "line": 22, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 44}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 82, "column": 22, "nodeType": "2275", "messageId": "2276", "endLine": 82, "endColumn": 24}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 82, "column": 53, "nodeType": "2275", "messageId": "2276", "endLine": 82, "endColumn": 55}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 85, "column": 36, "nodeType": "2275", "messageId": "2276", "endLine": 85, "endColumn": 38}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 85, "column": 63, "nodeType": "2275", "messageId": "2276", "endLine": 85, "endColumn": 65}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 88, "column": 36, "nodeType": "2275", "messageId": "2276", "endLine": 88, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2526", "line": 95, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 95, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2527", "line": 96, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 96, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2528", "line": 99, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 99, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2529", "line": 100, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2530", "line": 108, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 108, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2531", "line": 128, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 128, "endColumn": 16}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 268, "column": 45, "nodeType": "2275", "messageId": "2276", "endLine": 268, "endColumn": 47}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 268, "column": 104, "nodeType": "2275", "messageId": "2276", "endLine": 268, "endColumn": 106}, {"ruleId": "2120", "severity": 1, "message": "2772", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2735", "line": 4, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 8, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2773", "line": 15, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2774", "line": 15, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2775", "line": 15, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2776", "line": 15, "column": 62, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2777", "line": 15, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 96}, {"ruleId": "2120", "severity": 1, "message": "2778", "line": 17, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2779", "line": 17, "column": 35, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2381", "line": 17, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 55}, {"ruleId": "2120", "severity": 1, "message": "2780", "line": 22, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2781", "line": 58, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2782", "line": 66, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 66, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2745", "line": 72, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 72, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2744", "line": 73, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 73, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2783", "line": 74, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 74, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 75, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 75, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2784", "line": 76, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 76, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2752", "line": 77, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 77, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2785", "line": 78, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2688", "line": 79, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 79, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2748", "line": 80, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2786", "line": 81, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 81, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2787", "line": 82, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 83, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2385", "line": 84, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2788", "line": 87, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 87, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2789", "line": 89, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 89, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2391", "line": 91, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 91, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2396", "line": 93, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 93, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2790", "line": 166, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 166, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2791", "line": 169, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 169, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2792", "line": 179, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 179, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2793", "line": 332, "column": 5, "nodeType": "2225", "endLine": 332, "endColumn": 18, "suggestions": "2794"}, {"ruleId": "2120", "severity": 1, "message": "2754", "line": 599, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 599, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 599, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 599, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 599, "column": 62, "nodeType": "2122", "messageId": "2123", "endLine": 599, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 599, "column": 69, "nodeType": "2122", "messageId": "2123", "endLine": 599, "endColumn": 85}, {"ruleId": "2223", "severity": 1, "message": "2795", "line": 927, "column": 3, "nodeType": "2225", "endLine": 927, "endColumn": 19, "suggestions": "2796"}, {"ruleId": "2120", "severity": 1, "message": "2797", "line": 999, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 999, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2798", "line": 1008, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 1008, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2729", "line": 2, "column": 50, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 32, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2611", "line": 4, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 65}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 4, "column": 67, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 75}, {"ruleId": "2120", "severity": 1, "message": "2799", "line": 8, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2800", "line": 31, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 31, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2494", "line": 33, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2801", "line": 43, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2802", "line": 85, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2632", "line": 95, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 95, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2803", "line": 5, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2804", "line": 6, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2805", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2806", "line": 27, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2807", "line": 34, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2585", "line": 56, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2586", "line": 58, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2808", "line": 80, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2809", "line": 82, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2810", "line": 82, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2811", "line": 133, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 133, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2812", "line": 290, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 290, "endColumn": 28}, {"ruleId": "2223", "severity": 1, "message": "2813", "line": 344, "column": 5, "nodeType": "2225", "endLine": 344, "endColumn": 22, "suggestions": "2814"}, {"ruleId": "2120", "severity": 1, "message": "2772", "line": 1, "column": 58, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 1, "column": 75, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 82}, {"ruleId": "2120", "severity": 1, "message": "2656", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2780", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2372", "line": 5, "column": 41, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2778", "line": 6, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2381", "line": 6, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2815", "line": 6, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2816", "line": 6, "column": 31, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2447", "line": 6, "column": 37, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2779", "line": 6, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 61}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2817", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2818", "line": 39, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 39, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2819", "line": 40, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2820", "line": 42, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2454", "line": 43, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2821", "line": 44, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2453", "line": 45, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2452", "line": 46, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2822", "line": 47, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2450", "line": 49, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2823", "line": 50, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2824", "line": 51, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2742", "line": 54, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2825", "line": 58, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2471", "line": 58, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2826", "line": 60, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2827", "line": 60, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2828", "line": 76, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 76, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2829", "line": 84, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 84, "endColumn": 29}, {"ruleId": "2223", "severity": 1, "message": "2830", "line": 117, "column": 6, "nodeType": "2225", "endLine": 117, "endColumn": 35, "suggestions": "2831"}, {"ruleId": "2120", "severity": 1, "message": "2377", "line": 2, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2832", "line": 20, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2491", "line": 42, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 43, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2393", "line": 44, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2833", "line": 46, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2731", "line": 48, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2494", "line": 49, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2476", "line": 60, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2477", "line": 60, "column": 45, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 62}, {"ruleId": "2120", "severity": 1, "message": "2834", "line": 100, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 100, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2835", "line": 103, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "2836", "line": 128, "column": 5, "nodeType": "2225", "endLine": 128, "endColumn": 155, "suggestions": "2837"}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 79}, {"ruleId": "2120", "severity": 1, "message": "2170", "line": 15, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2838", "line": 18, "column": 48, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 76}, {"ruleId": "2120", "severity": 1, "message": "2839", "line": 18, "column": 78, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 85}, {"ruleId": "2120", "severity": 1, "message": "2470", "line": 20, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2840", "line": 52, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2841", "line": 54, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2842", "line": 59, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2789", "line": 59, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2718", "line": 60, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2205", "line": 61, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2206", "line": 61, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2684", "line": 85, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 92, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 25}, {"ruleId": "2223", "severity": 1, "message": "2844", "line": 183, "column": 5, "nodeType": "2225", "endLine": 183, "endColumn": 52, "suggestions": "2845"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 183, "column": 6, "nodeType": "2328", "endLine": 183, "endColumn": 51}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 1, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2737", "line": 8, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2846", "line": 9, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2847", "line": 80, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 58}, {"ruleId": "2223", "severity": 1, "message": "2848", "line": 86, "column": 8, "nodeType": "2287", "endLine": 90, "endColumn": 12}, {"ruleId": "2223", "severity": 1, "message": "2849", "line": 86, "column": 8, "nodeType": "2287", "endLine": 90, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2850", "line": 92, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2851", "line": 92, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 92, "endColumn": 42}, {"ruleId": "2852", "severity": 1, "message": "2853", "line": 113, "column": 113, "nodeType": "2854", "messageId": "2855", "endLine": 113, "endColumn": 397}, {"ruleId": "2223", "severity": 1, "message": "2856", "line": 154, "column": 5, "nodeType": "2225", "endLine": 154, "endColumn": 38, "suggestions": "2857"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 154, "column": 6, "nodeType": "2275", "endLine": 154, "endColumn": 37}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 154, "column": 33, "nodeType": "2275", "messageId": "2276", "endLine": 154, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2858", "line": 156, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 156, "endColumn": 24}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 174, "column": 56, "nodeType": "2275", "messageId": "2276", "endLine": 174, "endColumn": 58}, {"ruleId": "2120", "severity": 1, "message": "2859", "line": 181, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 181, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2860", "line": 182, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 182, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2861", "line": 305, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 305, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2858", "line": 770, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 770, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2825", "line": 805, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 805, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2471", "line": 805, "column": 19, "nodeType": "2122", "messageId": "2123", "endLine": 805, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 806, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 806, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2731", "line": 806, "column": 22, "nodeType": "2122", "messageId": "2123", "endLine": 806, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2302", "line": 807, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 807, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2303", "line": 807, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 807, "endColumn": 48}, {"ruleId": "2120", "severity": 1, "message": "2862", "line": 808, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 808, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2863", "line": 808, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 808, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2217", "line": 809, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 809, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 2, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2864", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2846", "line": 8, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2865", "line": 8, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 59}, {"ruleId": "2120", "severity": 1, "message": "2866", "line": 10, "column": 34, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 57}, {"ruleId": "2120", "severity": 1, "message": "2867", "line": 10, "column": 59, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 79}, {"ruleId": "2120", "severity": 1, "message": "2858", "line": 59, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2868", "line": 124, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 124, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2581", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2582", "line": 5, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2869", "line": 9, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2870", "line": 9, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2583", "line": 9, "column": 32, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 41}, {"ruleId": "2120", "severity": 1, "message": "2871", "line": 9, "column": 43, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 51}, {"ruleId": "2120", "severity": 1, "message": "2872", "line": 9, "column": 53, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 63}, {"ruleId": "2120", "severity": 1, "message": "2873", "line": 9, "column": 65, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 77}, {"ruleId": "2120", "severity": 1, "message": "2874", "line": 9, "column": 79, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 90}, {"ruleId": "2120", "severity": 1, "message": "2875", "line": 9, "column": 92, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 102}, {"ruleId": "2120", "severity": 1, "message": "2876", "line": 9, "column": 104, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 116}, {"ruleId": "2120", "severity": 1, "message": "2877", "line": 9, "column": 118, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 129}, {"ruleId": "2120", "severity": 1, "message": "2878", "line": 9, "column": 131, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2879", "line": 15, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 16, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2880", "line": 17, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2745", "line": 18, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2881", "line": 19, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 20, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2744", "line": 23, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2882", "line": 24, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2783", "line": 25, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2883", "line": 26, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2787", "line": 27, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2884", "line": 28, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2885", "line": 29, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2886", "line": 30, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 34, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2887", "line": 62, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 62, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2607", "line": 82, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2888", "line": 83, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 5, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 6, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 6, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 6, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 6, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 6, "column": 105, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 111}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 6, "column": 113, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 121}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 6, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 6, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 6, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2591", "line": 6, "column": 168, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 180}, {"ruleId": "2120", "severity": 1, "message": "2592", "line": 6, "column": 182, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 199}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 8, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 8, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 8, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 9, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 17, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 18, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 19, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 20, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 21, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 7}, {"ruleId": "2120", "severity": 1, "message": "2893", "line": 28, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2894", "line": 29, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2895", "line": 30, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2896", "line": 31, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 31, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2897", "line": 32, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 32, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2898", "line": 33, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2899", "line": 34, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2900", "line": 42, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2901", "line": 43, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2902", "line": 45, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2903", "line": 47, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2859", "line": 49, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2887", "line": 96, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 96, "endColumn": 19}, {"ruleId": "2223", "severity": 1, "message": "2904", "line": 127, "column": 5, "nodeType": "2225", "endLine": 127, "endColumn": 7, "suggestions": "2905"}, {"ruleId": "2120", "severity": 1, "message": "2906", "line": 147, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 147, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2907", "line": 164, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 164, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2908", "line": 167, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 167, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 172, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 172, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2909", "line": 213, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 213, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2910", "line": 216, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 216, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2911", "line": 229, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 229, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2912", "line": 230, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 230, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2913", "line": 230, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 230, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2914", "line": 231, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 231, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2915", "line": 231, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 231, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2205", "line": 247, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 247, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2206", "line": 247, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 247, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2916", "line": 249, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 249, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2917", "line": 263, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 263, "endColumn": 36}, {"ruleId": "2223", "severity": 1, "message": "2918", "line": 283, "column": 4, "nodeType": "2225", "endLine": 283, "endColumn": 6, "suggestions": "2919"}, {"ruleId": "2120", "severity": 1, "message": "2917", "line": 336, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 336, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2920", "line": 349, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 349, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2921", "line": 349, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 349, "endColumn": 45}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 2, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 2, "column": 105, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 111}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 2, "column": 113, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 121}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 168, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 175}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 8, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 9, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 10, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 11, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 12, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2922", "line": 13, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2923", "line": 14, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2924", "line": 15, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2925", "line": 22, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2926", "line": 29, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2866", "line": 30, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2900", "line": 33, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2901", "line": 34, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2902", "line": 36, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2927", "line": 37, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 37, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2928", "line": 38, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2929", "line": 40, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2930", "line": 41, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2931", "line": 42, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2932", "line": 43, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2933", "line": 44, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2934", "line": 45, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2935", "line": 46, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2936", "line": 47, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2937", "line": 48, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2938", "line": 49, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 56, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2939", "line": 58, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2887", "line": 73, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 73, "endColumn": 19}, {"ruleId": "2223", "severity": 1, "message": "2940", "line": 85, "column": 5, "nodeType": "2225", "endLine": 85, "endColumn": 45, "suggestions": "2941"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 85, "column": 6, "nodeType": "2328", "endLine": 85, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2942", "line": 104, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 104, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2943", "line": 104, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 104, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2944", "line": 105, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2945", "line": 105, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 105, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2946", "line": 106, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 106, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2389", "line": 107, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 107, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2947", "line": 107, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 107, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2948", "line": 108, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 108, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2907", "line": 113, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 113, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 117, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 117, "endColumn": 25}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 132, "column": 11, "nodeType": "2275", "messageId": "2276", "endLine": 132, "endColumn": 13}, {"ruleId": "2949", "severity": 1, "message": "2950", "line": 41, "column": 11, "nodeType": "2951", "endLine": 51, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 2, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 2, "column": 105, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 111}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 2, "column": 113, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 121}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 7, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 8, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 9, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 10, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2900", "line": 17, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2902", "line": 20, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2952", "line": 22, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2953", "line": 23, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2954", "line": 24, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2955", "line": 25, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 29, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2887", "line": 34, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2956", "line": 87, "column": 21, "nodeType": "2122", "messageId": "2123", "endLine": 87, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2957", "line": 88, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 40}, {"ruleId": "2120", "severity": 1, "message": "2906", "line": 103, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 103, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2907", "line": 137, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 137, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2958", "line": 140, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 140, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2126", "line": 5, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2959", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2850", "line": 83, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 83, "endColumn": 24}, {"ruleId": "2223", "severity": 1, "message": "2960", "line": 96, "column": 6, "nodeType": "2225", "endLine": 96, "endColumn": 8, "suggestions": "2961"}, {"ruleId": "2223", "severity": 1, "message": "2836", "line": 119, "column": 6, "nodeType": "2225", "endLine": 119, "endColumn": 32, "suggestions": "2962"}, {"ruleId": "2223", "severity": 1, "message": "2856", "line": 123, "column": 6, "nodeType": "2225", "endLine": 123, "endColumn": 40, "suggestions": "2963"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 123, "column": 7, "nodeType": "2275", "endLine": 123, "endColumn": 39}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 123, "column": 35, "nodeType": "2275", "messageId": "2276", "endLine": 123, "endColumn": 37}, {"ruleId": "2223", "severity": 1, "message": "2940", "line": 146, "column": 6, "nodeType": "2225", "endLine": 146, "endColumn": 33, "suggestions": "2964"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 146, "column": 7, "nodeType": "2280", "endLine": 146, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2965", "line": 154, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 154, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2720", "line": 1, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 1, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2966", "line": 1, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 59}, {"ruleId": "2120", "severity": 1, "message": "2734", "line": 1, "column": 61, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 2, "column": 56, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 62}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 2, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 2, "column": 105, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 111}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 2, "column": 113, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 121}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2446", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 7, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 8, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 9, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 10, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 11, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2922", "line": 12, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2967", "line": 18, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2378", "line": 2, "column": 177, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 193}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 7, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 8, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 9, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 10, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 11, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2894", "line": 27, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 27, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2895", "line": 28, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2896", "line": 29, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2897", "line": 30, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2900", "line": 36, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2901", "line": 37, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 37, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2902", "line": 39, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 39, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2927", "line": 40, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2928", "line": 41, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2968", "line": 42, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2929", "line": 43, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2931", "line": 45, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2932", "line": 46, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2933", "line": 47, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2935", "line": 49, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2936", "line": 50, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2937", "line": 51, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2938", "line": 52, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 56, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2969", "line": 150, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 150, "endColumn": 40}, {"ruleId": "2120", "severity": 1, "message": "2970", "line": 151, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 151, "endColumn": 41}, {"ruleId": "2120", "severity": 1, "message": "2971", "line": 152, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 152, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2205", "line": 154, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 154, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2972", "line": 183, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 183, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2907", "line": 218, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 218, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2908", "line": 221, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 221, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 226, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 226, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2313", "line": 243, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 243, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2942", "line": 247, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 247, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2948", "line": 252, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 252, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "2973", "line": 261, "column": 6, "nodeType": "2225", "endLine": 261, "endColumn": 8, "suggestions": "2974"}, {"ruleId": "2120", "severity": 1, "message": "2975", "line": 296, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 296, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2707", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2734", "line": 1, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 55}, {"ruleId": "2120", "severity": 1, "message": "2976", "line": 1, "column": 69, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 80}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 2, "column": 38, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 2, "column": 93, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 103}, {"ruleId": "2120", "severity": 1, "message": "2708", "line": 2, "column": 123, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 140}, {"ruleId": "2120", "severity": 1, "message": "2375", "line": 2, "column": 142, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 158}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 160, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 166}, {"ruleId": "2120", "severity": 1, "message": "2735", "line": 2, "column": 195, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 212}, {"ruleId": "2120", "severity": 1, "message": "2709", "line": 4, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 49}, {"ruleId": "2120", "severity": 1, "message": "2710", "line": 4, "column": 51, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2711", "line": 4, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 91}, {"ruleId": "2120", "severity": 1, "message": "2712", "line": 5, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2893", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2894", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2895", "line": 9, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2896", "line": 10, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2897", "line": 11, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2898", "line": 12, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2889", "line": 14, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2890", "line": 15, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2891", "line": 16, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 16, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2892", "line": 17, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 18, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2817", "line": 33, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 33, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2977", "line": 41, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2900", "line": 42, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2901", "line": 43, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2902", "line": 45, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2927", "line": 46, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2928", "line": 47, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2968", "line": 48, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2929", "line": 49, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 49, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2931", "line": 51, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2932", "line": 52, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "2933", "line": 53, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2935", "line": 55, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 55, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2936", "line": 56, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2937", "line": 57, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 57, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2938", "line": 58, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2978", "line": 60, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2573", "line": 63, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 63, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2969", "line": 86, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 86, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2970", "line": 87, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 87, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2971", "line": 88, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 88, "endColumn": 46}, {"ruleId": "2223", "severity": 1, "message": "2979", "line": 109, "column": 5, "nodeType": "2225", "endLine": 109, "endColumn": 52, "suggestions": "2980"}, {"ruleId": "2120", "severity": 1, "message": "2907", "line": 114, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 114, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2908", "line": 117, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 117, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 122, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 122, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2981", "line": 132, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 132, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2313", "line": 172, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 172, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2982", "line": 180, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 180, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "2973", "line": 185, "column": 4, "nodeType": "2225", "endLine": 185, "endColumn": 6, "suggestions": "2983"}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 190, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 190, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 191, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 191, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 191, "column": 39, "nodeType": "2275", "messageId": "2276", "endLine": 191, "endColumn": 41}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 210, "column": 19, "nodeType": "2275", "messageId": "2276", "endLine": 210, "endColumn": 21}, {"ruleId": "2273", "severity": 1, "message": "2295", "line": 223, "column": 20, "nodeType": "2275", "messageId": "2276", "endLine": 223, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2984", "line": 276, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 276, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2205", "line": 304, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 304, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2972", "line": 331, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 331, "endColumn": 23}, {"ruleId": "2223", "severity": 1, "message": "2985", "line": 368, "column": 4, "nodeType": "2225", "endLine": 368, "endColumn": 6, "suggestions": "2986"}, {"ruleId": "2120", "severity": 1, "message": "2734", "line": 1, "column": 28, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 5, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 5}, {"ruleId": "2120", "severity": 1, "message": "2735", "line": 6, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2728", "line": 10, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2685", "line": 12, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 13, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2736", "line": 17, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2737", "line": 19, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2987", "line": 20, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2988", "line": 34, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2989", "line": 34, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2990", "line": 34, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2991", "line": 34, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 34, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 45, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 45, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 45, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2210", "line": 69, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 69, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2992", "line": 70, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 70, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2993", "line": 71, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 71, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2742", "line": 154, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 154, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2743", "line": 157, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 157, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 158, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 158, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2229", "line": 159, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 159, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2744", "line": 161, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 161, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2478", "line": 162, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 162, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "2745", "line": 163, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 163, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2746", "line": 166, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 166, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2747", "line": 167, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 167, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2748", "line": 168, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 168, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2749", "line": 169, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 169, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2750", "line": 170, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 170, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2751", "line": 171, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 171, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2487", "line": 172, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 172, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2486", "line": 173, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 173, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2485", "line": 174, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 174, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2752", "line": 177, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 177, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2688", "line": 180, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 180, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2754", "line": 182, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 182, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 183, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 183, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 184, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 184, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2755", "line": 187, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 187, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2756", "line": 188, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 188, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2533", "line": 190, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 190, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2572", "line": 195, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 195, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2251", "line": 196, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 196, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2994", "line": 204, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 204, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2757", "line": 207, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 207, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2758", "line": 207, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 207, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2759", "line": 227, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 227, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2760", "line": 227, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 227, "endColumn": 44}, {"ruleId": "2761", "severity": 1, "message": "2762", "line": 390, "column": 5, "nodeType": "2763", "messageId": "2764", "endLine": 390, "endColumn": 52}, {"ruleId": "2120", "severity": 1, "message": "2765", "line": 502, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 502, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2766", "line": 509, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 509, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2992", "line": 663, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 663, "endColumn": 18}, {"ruleId": "2223", "severity": 1, "message": "2995", "line": 746, "column": 5, "nodeType": "2225", "endLine": 746, "endColumn": 100, "suggestions": "2996"}, {"ruleId": "2223", "severity": 1, "message": "2997", "line": 839, "column": 5, "nodeType": "2225", "endLine": 839, "endColumn": 22, "suggestions": "2998"}, {"ruleId": "2120", "severity": 1, "message": "2771", "line": 941, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 941, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2729", "line": 2, "column": 50, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 64, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 71}, {"ruleId": "2120", "severity": 1, "message": "2593", "line": 2, "column": 73, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 79}, {"ruleId": "2120", "severity": 1, "message": "2170", "line": 15, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2838", "line": 18, "column": 48, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 76}, {"ruleId": "2120", "severity": 1, "message": "2839", "line": 18, "column": 78, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 85}, {"ruleId": "2120", "severity": 1, "message": "2470", "line": 20, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2840", "line": 50, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 50, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2841", "line": 52, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 52, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2842", "line": 57, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 57, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2789", "line": 57, "column": 18, "nodeType": "2122", "messageId": "2123", "endLine": 57, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2718", "line": 58, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 58, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2684", "line": 78, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2843", "line": 85, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 85, "endColumn": 25}, {"ruleId": "2223", "severity": 1, "message": "2844", "line": 167, "column": 5, "nodeType": "2225", "endLine": 167, "endColumn": 52, "suggestions": "2999"}, {"ruleId": "2223", "severity": 1, "message": "2279", "line": 167, "column": 6, "nodeType": "2328", "endLine": 167, "endColumn": 51}, {"ruleId": "2120", "severity": 1, "message": "2772", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 26}, {"ruleId": "2120", "severity": 1, "message": "2735", "line": 4, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 8, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 9, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 9, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2773", "line": 15, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2774", "line": 15, "column": 30, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2775", "line": 15, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2776", "line": 15, "column": 62, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 78}, {"ruleId": "2120", "severity": 1, "message": "2777", "line": 15, "column": 80, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 96}, {"ruleId": "2120", "severity": 1, "message": "2778", "line": 17, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2779", "line": 17, "column": 35, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2381", "line": 17, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 55}, {"ruleId": "2120", "severity": 1, "message": "2780", "line": 22, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2782", "line": 54, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 54, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2745", "line": 59, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 59, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "2744", "line": 60, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 60, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2783", "line": 61, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 61, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2481", "line": 62, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 62, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2784", "line": 63, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 63, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2752", "line": 64, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 64, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2785", "line": 65, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2688", "line": 66, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 66, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2748", "line": 67, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 67, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2786", "line": 68, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 68, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2787", "line": 69, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 69, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 70, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 70, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2385", "line": 71, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 71, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2788", "line": 74, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 74, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2789", "line": 76, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 76, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2391", "line": 78, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2790", "line": 121, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 121, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2791", "line": 124, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 124, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2792", "line": 134, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 134, "endColumn": 21}, {"ruleId": "2223", "severity": 1, "message": "2793", "line": 191, "column": 5, "nodeType": "2225", "endLine": 191, "endColumn": 18, "suggestions": "3000"}, {"ruleId": "2120", "severity": 1, "message": "2754", "line": 424, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 424, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2601", "line": 424, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 424, "endColumn": 60}, {"ruleId": "2120", "severity": 1, "message": "2427", "line": 424, "column": 62, "nodeType": "2122", "messageId": "2123", "endLine": 424, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2390", "line": 424, "column": 69, "nodeType": "2122", "messageId": "2123", "endLine": 424, "endColumn": 85}, {"ruleId": "2223", "severity": 1, "message": "2795", "line": 681, "column": 3, "nodeType": "2225", "endLine": 681, "endColumn": 5, "suggestions": "3001"}, {"ruleId": "2120", "severity": 1, "message": "2797", "line": 753, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 753, "endColumn": 33}, {"ruleId": "2120", "severity": 1, "message": "2798", "line": 762, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 762, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 32, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 44, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2611", "line": 4, "column": 46, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 65}, {"ruleId": "2120", "severity": 1, "message": "2150", "line": 4, "column": 67, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 75}, {"ruleId": "2120", "severity": 1, "message": "2799", "line": 8, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2800", "line": 28, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 28, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 29, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2494", "line": 30, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "3002", "line": 35, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 35, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "3003", "line": 35, "column": 29, "nodeType": "2122", "messageId": "2123", "endLine": 35, "endColumn": 50}, {"ruleId": "2120", "severity": 1, "message": "3004", "line": 36, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "3005", "line": 36, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 44}, {"ruleId": "2120", "severity": 1, "message": "2801", "line": 38, "column": 6, "nodeType": "2122", "messageId": "2123", "endLine": 38, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2802", "line": 72, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 72, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2632", "line": 82, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 82, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "3006", "line": 99, "column": 5, "nodeType": "2225", "endLine": 99, "endColumn": 31, "suggestions": "3007"}, {"ruleId": "2120", "severity": 1, "message": "2803", "line": 5, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2804", "line": 6, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2805", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2806", "line": 25, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 25, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2807", "line": 32, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 32, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2808", "line": 63, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 63, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2809", "line": 65, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2810", "line": 65, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 38}, {"ruleId": "2120", "severity": 1, "message": "3008", "line": 80, "column": 25, "nodeType": "2122", "messageId": "2123", "endLine": 80, "endColumn": 42}, {"ruleId": "2120", "severity": 1, "message": "2811", "line": 118, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 118, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2812", "line": 257, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 257, "endColumn": 28}, {"ruleId": "2223", "severity": 1, "message": "2813", "line": 311, "column": 5, "nodeType": "2225", "endLine": 311, "endColumn": 22, "suggestions": "3009"}, {"ruleId": "2120", "severity": 1, "message": "2734", "line": 1, "column": 50, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 56}, {"ruleId": "2120", "severity": 1, "message": "2772", "line": 1, "column": 58, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2656", "line": 2, "column": 15, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2415", "line": 2, "column": 33, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2780", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2372", "line": 5, "column": 41, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 53}, {"ruleId": "2120", "severity": 1, "message": "2778", "line": 6, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2381", "line": 6, "column": 16, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "2815", "line": 6, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2816", "line": 6, "column": 31, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2447", "line": 6, "column": 37, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 47}, {"ruleId": "2120", "severity": 1, "message": "2779", "line": 6, "column": 49, "nodeType": "2122", "messageId": "2123", "endLine": 6, "endColumn": 61}, {"ruleId": "2120", "severity": 1, "message": "2571", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2817", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2818", "line": 36, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 36, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2819", "line": 37, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 37, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2820", "line": 39, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 39, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2454", "line": 40, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2821", "line": 41, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2453", "line": 42, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2452", "line": 43, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 43, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2450", "line": 46, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "2823", "line": 47, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2824", "line": 48, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "2742", "line": 51, "column": 4, "nodeType": "2122", "messageId": "2123", "endLine": 51, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2825", "line": 53, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2471", "line": 53, "column": 20, "nodeType": "2122", "messageId": "2123", "endLine": 53, "endColumn": 31}, {"ruleId": "2120", "severity": 1, "message": "2826", "line": 55, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 55, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2827", "line": 55, "column": 26, "nodeType": "2122", "messageId": "2123", "endLine": 55, "endColumn": 43}, {"ruleId": "2120", "severity": 1, "message": "2828", "line": 68, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 68, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "2829", "line": 76, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 76, "endColumn": 29}, {"ruleId": "2223", "severity": 1, "message": "2830", "line": 109, "column": 6, "nodeType": "2225", "endLine": 109, "endColumn": 35, "suggestions": "3010"}, {"ruleId": "2120", "severity": 1, "message": "2377", "line": 2, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2832", "line": 20, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 35}, {"ruleId": "2120", "severity": 1, "message": "2491", "line": 40, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 40, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "2600", "line": 41, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2393", "line": 42, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 42, "endColumn": 28}, {"ruleId": "2120", "severity": 1, "message": "2833", "line": 44, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 44, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2731", "line": 46, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 46, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "2494", "line": 47, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 47, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "2800", "line": 48, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 48, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2476", "line": 56, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2477", "line": 56, "column": 45, "nodeType": "2122", "messageId": "2123", "endLine": 56, "endColumn": 62}, {"ruleId": "2120", "severity": 1, "message": "2834", "line": 69, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 69, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2835", "line": 72, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 72, "endColumn": 26}, {"ruleId": "2223", "severity": 1, "message": "3011", "line": 97, "column": 5, "nodeType": "2225", "endLine": 97, "endColumn": 28, "suggestions": "3012"}, {"ruleId": "2120", "severity": 1, "message": "2656", "line": 2, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 34}, {"ruleId": "2120", "severity": 1, "message": "2729", "line": 2, "column": 36, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 46}, {"ruleId": "2120", "severity": 1, "message": "2587", "line": 2, "column": 48, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 57}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 2, "column": 59, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 67}, {"ruleId": "2120", "severity": 1, "message": "2357", "line": 2, "column": 69, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 75}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 2, "column": 77, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 84}, {"ruleId": "2120", "severity": 1, "message": "3013", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "2817", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2803", "line": 7, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 7, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "2804", "line": 8, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 18}, {"ruleId": "3014", "severity": 1, "message": "3015", "line": 67, "column": 12, "nodeType": "2951", "endLine": 67, "endColumn": 104}, {"ruleId": "2120", "severity": 1, "message": "3016", "line": 1, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2568", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 25}, {"ruleId": "2120", "severity": 1, "message": "3017", "line": 4, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 30}, {"ruleId": "2120", "severity": 1, "message": "3018", "line": 4, "column": 32, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 45}, {"ruleId": "2120", "severity": 1, "message": "3019", "line": 10, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "3020", "line": 65, "column": 12, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "3021", "line": 65, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 65, "endColumn": 37}, {"ruleId": "2120", "severity": 1, "message": "3022", "line": 78, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "3023", "line": 78, "column": 24, "nodeType": "2122", "messageId": "2123", "endLine": 78, "endColumn": 39}, {"ruleId": "2223", "severity": 1, "message": "3024", "line": 120, "column": 6, "nodeType": "2225", "endLine": 120, "endColumn": 8, "suggestions": "3025"}, {"ruleId": "2120", "severity": 1, "message": "3026", "line": 157, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 157, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "3027", "line": 280, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 280, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "3028", "line": 296, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 296, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "3029", "line": 461, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 461, "endColumn": 17}, {"ruleId": "2120", "severity": 1, "message": "3030", "line": 462, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 462, "endColumn": 20}, {"ruleId": "2223", "severity": 1, "message": "3031", "line": 467, "column": 3, "nodeType": "2225", "endLine": 467, "endColumn": 5, "suggestions": "3032"}, {"ruleId": "2120", "severity": 1, "message": "3033", "line": 2, "column": 14, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "3034", "line": 14, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 14, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "3035", "line": 18, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 20}, {"ruleId": "2120", "severity": 1, "message": "3036", "line": 70, "column": 23, "nodeType": "2122", "messageId": "2123", "endLine": 70, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "3037", "line": 3, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 3, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "3038", "line": 4, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "3039", "line": 29, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "3040", "line": 30, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 30, "endColumn": 21}, {"ruleId": "2120", "severity": 1, "message": "3041", "line": 226, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 226, "endColumn": 29}, {"ruleId": "2120", "severity": 1, "message": "3042", "line": 296, "column": 7, "nodeType": "2122", "messageId": "2123", "endLine": 296, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2314", "line": 681, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 681, "endColumn": 19}, {"ruleId": "2120", "severity": 1, "message": "3043", "line": 686, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 686, "endColumn": 23}, {"ruleId": "2120", "severity": 1, "message": "2584", "line": 1, "column": 27, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 36}, {"ruleId": "2120", "severity": 1, "message": "2444", "line": 2, "column": 8, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "3044", "line": 12, "column": 11, "nodeType": "2122", "messageId": "2123", "endLine": 12, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2966", "line": 1, "column": 17, "nodeType": "2122", "messageId": "2123", "endLine": 1, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "2130", "line": 2, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 2, "endColumn": 22}, {"ruleId": "2120", "severity": 1, "message": "3045", "line": 4, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 4, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "2152", "line": 8, "column": 2, "nodeType": "2122", "messageId": "2123", "endLine": 8, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2638", "line": 10, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 10, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "2443", "line": 11, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 11, "endColumn": 8}, {"ruleId": "2120", "severity": 1, "message": "3046", "line": 13, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 13, "endColumn": 14}, {"ruleId": "2120", "severity": 1, "message": "2379", "line": 15, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 15, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2597", "line": 17, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 16}, {"ruleId": "2120", "severity": 1, "message": "2634", "line": 18, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 18, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2595", "line": 19, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 19, "endColumn": 11}, {"ruleId": "2120", "severity": 1, "message": "2596", "line": 20, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 20, "endColumn": 13}, {"ruleId": "2120", "severity": 1, "message": "2729", "line": 21, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 21, "endColumn": 15}, {"ruleId": "2120", "severity": 1, "message": "2580", "line": 22, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 22, "endColumn": 12}, {"ruleId": "2120", "severity": 1, "message": "3047", "line": 23, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 23, "endColumn": 10}, {"ruleId": "2120", "severity": 1, "message": "3048", "line": 24, "column": 5, "nodeType": "2122", "messageId": "2123", "endLine": 24, "endColumn": 9}, {"ruleId": "2120", "severity": 1, "message": "3049", "line": 29, "column": 13, "nodeType": "2122", "messageId": "2123", "endLine": 29, "endColumn": 32}, {"ruleId": "2120", "severity": 1, "message": "2496", "line": 17, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 17, "endColumn": 18}, {"ruleId": "2120", "severity": 1, "message": "3050", "line": 5, "column": 3, "nodeType": "2122", "messageId": "2123", "endLine": 5, "endColumn": 24}, {"ruleId": "2120", "severity": 1, "message": "3051", "line": 26, "column": 10, "nodeType": "2122", "messageId": "2123", "endLine": 26, "endColumn": 27}, {"ruleId": "2120", "severity": 1, "message": "3052", "line": 41, "column": 9, "nodeType": "2122", "messageId": "2123", "endLine": 41, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["3053"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["3054"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["3055"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1050) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["3056"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["3057"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["3058"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3059"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["3060"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["3061"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "'response' is assigned a value but never used.", "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["3062"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["3063"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["3064"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["3065"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["3066"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["3067"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["3068", "3069"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["3070"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["3071"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["3072"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["3073"], "'Typography' is defined but never used.", "'useAuth' is defined but never used.", "'ExtensionLoader' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["3074"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["3075"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["3076"], "'handleEnableAI' is assigned a value but never used.", "'Box' is defined but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["3077"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["3078"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["3079"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["3080"], "'overlayEnabled' is assigned a value but never used.", "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'setAnchorEl' is assigned a value but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["3081"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["3082"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["3083"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["3084"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["3085"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["3086"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["3087"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["3088"], ["3089"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["3090"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'currentGuideId' is assigned a value but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["3091"], ["3092"], "'Tooltip' is defined but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'useEffect' is defined but never used.", "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "React Hook useMemo has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["3093"], "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'Switch' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["3094"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["3095"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'Grid' is defined but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'Popover' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["3096"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["3097"], "'handlePositionClick' is assigned a value but never used.", "'Breadcrumbs' is defined but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["3098"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["3099"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["3100"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["3101"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["3102"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["3103"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["3104"], ["3105"], ["3106"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'guideStatus' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "'handleColorChange' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'IconButton' is defined but never used.", "'PopoverOrigin' is defined but never used.", "'setCurrentStep' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["3107"], "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["3108"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["3109"], "'DotsStepper' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3110"], "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["3111"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["3112"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'AddIcon' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'handleTooltipRTEValue' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'anchorPosition' is assigned a value but never used.", "'setAnchorPosition' is assigned a value but never used.", "'preserveCaretPosition' is assigned a value but never used.", "'restoreCaretPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'boxRef' and 'textvaluess'. Either include them or remove the dependency array. Mutable values like 'boxRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["3113"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["3114"], "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3115"], "'closeicon' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["3116"], "'setIcons' is assigned a value but never used.", "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'ChecklistPopup' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["3117"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["3118"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["3119"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["3120"], ["3121"], ["3122"], ["3123"], "'toggleItemCompletion' is assigned a value but never used.", "'useContext' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["3124"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["3125"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["3126"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["3127"], "'transform' is defined but never used.", "'a' is assigned a value but never used.", "'b' is assigned a value but never used.", "'c' is assigned a value but never used.", "'d' is assigned a value but never used.", "'inset' is assigned a value but never used.", "'margin' is assigned a value but never used.", "'storedTransform' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'currentStep', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setStoredTransform', 'setTooltip', 'setTooltipPosition', and 'setXpathToTooltipMetaData'. Either include them or remove the dependency array.", ["3128"], "React Hook useEffect has missing dependencies: 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["3129"], ["3130"], ["3131"], ["3132"], "'currentContainerId' is assigned a value but never used.", "'setCurrentContainerId' is assigned a value but never used.", "'currentButtonId' is assigned a value but never used.", "'setCurrentButtonId' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'buttonsContainer.buttons'. Either include it or remove the dependency array.", ["3133"], "'setSelectedButton' is assigned a value but never used.", ["3134"], ["3135"], "React Hook useEffect has missing dependencies: 'btnidss', 'buttonInfo', 'currentGuideId', and 'currentStep'. Either include them or remove the dependency array.", ["3136"], "'RemoveIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'AxiosResponse' is defined but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["3137"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["3138"], "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'future' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'ChromeMessage' is defined but never used.", "'AccountContext' is defined but never used.", "'Container' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'initializationSteps' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "'getOrgLanguageKey' is defined but never used.", "'orgId' is assigned a value but never used.", {"desc": "3139", "fix": "3140"}, {"desc": "3141", "fix": "3142"}, {"desc": "3143", "fix": "3144"}, {"desc": "3145", "fix": "3146"}, {"desc": "3147", "fix": "3148"}, {"desc": "3149", "fix": "3150"}, {"desc": "3151", "fix": "3152"}, {"desc": "3153", "fix": "3154"}, {"desc": "3155", "fix": "3156"}, {"desc": "3157", "fix": "3158"}, {"desc": "3159", "fix": "3160"}, {"desc": "3161", "fix": "3162"}, {"desc": "3163", "fix": "3164"}, {"desc": "3165", "fix": "3166"}, {"desc": "3167", "fix": "3168"}, {"messageId": "3169", "fix": "3170", "desc": "3171"}, {"messageId": "3172", "fix": "3173", "desc": "3174"}, {"desc": "3175", "fix": "3176"}, {"desc": "3177", "fix": "3178"}, {"desc": "3179", "fix": "3180"}, {"desc": "3181", "fix": "3182"}, {"desc": "3183", "fix": "3184"}, {"desc": "3185", "fix": "3186"}, {"desc": "3187", "fix": "3188"}, {"desc": "3189", "fix": "3190"}, {"desc": "3191", "fix": "3192"}, {"desc": "3193", "fix": "3194"}, {"desc": "3195", "fix": "3196"}, {"desc": "3197", "fix": "3198"}, {"desc": "3199", "fix": "3200"}, {"messageId": "3201", "data": "3202", "fix": "3203", "desc": "3204"}, {"desc": "3205", "fix": "3206"}, {"desc": "3207", "fix": "3208"}, {"desc": "3209", "fix": "3210"}, {"desc": "3211", "fix": "3212"}, {"desc": "3213", "fix": "3214"}, {"desc": "3215", "fix": "3216"}, {"desc": "3217", "fix": "3218"}, {"desc": "3219", "fix": "3220"}, {"desc": "3221", "fix": "3222"}, {"desc": "3223", "fix": "3224"}, {"desc": "3225", "fix": "3226"}, {"desc": "3227", "fix": "3228"}, {"desc": "3229", "fix": "3230"}, {"desc": "3231", "fix": "3232"}, {"desc": "3233", "fix": "3234"}, {"desc": "3235", "fix": "3236"}, {"desc": "3237", "fix": "3238"}, {"desc": "3239", "fix": "3240"}, {"desc": "3241", "fix": "3242"}, {"desc": "3243", "fix": "3244"}, {"desc": "3245", "fix": "3246"}, {"desc": "3245", "fix": "3247"}, {"desc": "3248", "fix": "3249"}, {"desc": "3250", "fix": "3251"}, {"desc": "3252", "fix": "3253"}, {"desc": "3254", "fix": "3255"}, {"desc": "3256", "fix": "3257"}, {"desc": "3258", "fix": "3259"}, {"desc": "3260", "fix": "3261"}, {"desc": "3262", "fix": "3263"}, {"desc": "3264", "fix": "3265"}, {"desc": "3266", "fix": "3267"}, {"desc": "3268", "fix": "3269"}, {"desc": "3270", "fix": "3271"}, {"desc": "3272", "fix": "3273"}, {"desc": "3274", "fix": "3275"}, {"desc": "3276", "fix": "3277"}, {"desc": "3278", "fix": "3279"}, {"desc": "3268", "fix": "3280"}, {"desc": "3274", "fix": "3281"}, {"desc": "3282", "fix": "3283"}, {"desc": "3284", "fix": "3285"}, {"desc": "3282", "fix": "3286"}, {"desc": "3287", "fix": "3288"}, {"desc": "3289", "fix": "3290"}, {"desc": "3291", "fix": "3292"}, {"desc": "3266", "fix": "3293"}, {"desc": "3256", "fix": "3294"}, {"desc": "3295", "fix": "3296"}, {"desc": "3297", "fix": "3298"}, {"desc": "3260", "fix": "3299"}, {"desc": "3262", "fix": "3300"}, {"desc": "3301", "fix": "3302"}, {"desc": "3303", "fix": "3304"}, {"desc": "3305", "fix": "3306"}, "Update the dependencies array to be: []", {"range": "3307", "text": "3308"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3309", "text": "3310"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3311", "text": "3312"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3313", "text": "3314"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3315", "text": "3316"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3317", "text": "3318"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, setElementSelected, toolTipGuideMetaData]", {"range": "3319", "text": "3320"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3321", "text": "3322"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3323", "text": "3324"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3325", "text": "3326"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3327", "text": "3328"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3329", "text": "3330"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3331", "text": "3332"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3333", "text": "3334"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3335", "text": "3336"}, "removeEscape", {"range": "3337", "text": "3338"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3339", "text": "3340"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3341", "text": "3342"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3343", "text": "3344"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3345", "text": "3346"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3347", "text": "3348"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3349", "text": "3350"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3351", "text": "3352"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3353", "text": "3354"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3355", "text": "3356"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3357", "text": "3358"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3359", "text": "3360"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3361", "text": "3362"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3363", "text": "3364"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3365", "text": "3366"}, "suggestString", {"type": "3367"}, {"range": "3368", "text": "3369"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3370", "text": "3371"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3372", "text": "3373"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3374", "text": "3375"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3376", "text": "3377"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3378", "text": "3379"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3380", "text": "3381"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3382", "text": "3383"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3384", "text": "3385"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3386", "text": "3387"}, "Update the dependencies array to be: [handlePaste, isRtlDirection]", {"range": "3388", "text": "3389"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3390", "text": "3391"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3392", "text": "3393"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3394", "text": "3395"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3396", "text": "3397"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3398", "text": "3399"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3400", "text": "3401"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3402", "text": "3403"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3404", "text": "3405"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3406", "text": "3407"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3408", "text": "3409"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3410", "text": "3411"}, {"range": "3412", "text": "3411"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3413", "text": "3414"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3415", "text": "3416"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3417", "text": "3418"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3419", "text": "3420"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3421", "text": "3422"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3423", "text": "3424"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3425", "text": "3426"}, "Update the dependencies array to be: [rteBoxValue, boxRef, textvaluess]", {"range": "3427", "text": "3428"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3429", "text": "3430"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3431", "text": "3432"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3433", "text": "3434"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3435", "text": "3436"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3437", "text": "3438"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3439", "text": "3440"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3441", "text": "3442"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3443", "text": "3444"}, {"range": "3445", "text": "3434"}, {"range": "3446", "text": "3440"}, "Update the dependencies array to be: [fetchData]", {"range": "3447", "text": "3448"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3449", "text": "3450"}, {"range": "3451", "text": "3448"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3452", "text": "3453"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, currentStep, setTooltipPosition, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, applyHotspotProperties, setStoredTransform]", {"range": "3454", "text": "3455"}, "Update the dependencies array to be: [elementSelected, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3456", "text": "3457"}, {"range": "3458", "text": "3432"}, {"range": "3459", "text": "3422"}, "Update the dependencies array to be: [handleFocus]", {"range": "3460", "text": "3461"}, "Update the dependencies array to be: [buttonsContainer.buttons, settingAnchorEl.buttonId]", {"range": "3462", "text": "3463"}, {"range": "3464", "text": "3426"}, {"range": "3465", "text": "3428"}, "Update the dependencies array to be: [btnidss, buttonInfo, currentGuideId, currentStep, settingAnchorEl.value]", {"range": "3466", "text": "3467"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3468", "text": "3469"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3470", "text": "3471"}, [18230, 18252], "[]", [26508, 26549], "[fetchGuideDetails, hotspot, hotspotClicked]", [27031, 27044], "[designPopup, setDesignPopup]", [29768, 29915], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30393, 30753], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [35361, 35395], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [73248, 73261], "[currentStep, elementSelected, handleClose, isShowIcon, setElementSelected, toolTipGuideMetaData]", [89276, 89309], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [103908, 103922], "[createWithAI, setIsUnSavedChanges, stepCreation]", [131943, 132095], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [165134, 165191], "[currentGuide?.GuideStep, currentStep]", [170448, 170465], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [176398, 176431], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [177141, 177242], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [181319, 181331], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [202305, 202306], "", [202305, 202305], "\\", [4291, 4293], "[loggedOut]", [15983, 16038], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17412, 17467], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [17912, 17914], "[selectedActions.value, targetURL]", [2251, 2253], "[isExtensionClosed, setIsExtensionClosed]", [2918, 2941], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3285, 3328], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [6062, 6064], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7127, 7149], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7310, 7321], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8872, 8937], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [5216, 5256], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10140, 10175], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [4728, 4730], "[OverlayValue]", [8410, 8455], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [8761, 8774], "[fetchAnnouncements, searchQuery]", [13627, 13643], "[handlePaste, isRtlDirection]", [6784, 6809], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [2532, 2534], "[setButtonProperty]", [4537, 4592], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [5674, 5729], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [22789, 22884], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26752, 26769], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [9769, 9782], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26434, 26450], "[handleFocus, isRtlDirection]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [3811, 3840], "[rteBoxV<PERSON>ue, boxRef, textvaluess]", [4246, 4396], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [5960, 5993], "[checkpointslistData]", [4786, 4788], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9542, 9544], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3117, 3157], "[checklistGuideMetaData]", [3447, 3449], "[checkpointslistData, completedStatus]", [4291, 4317], "[selectedItem, activeItem, createWithAI, interactionData]", [4421, 4455], [5411, 5438], [7586, 7588], "[fetchData]", [4189, 4236], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6437, 6439], [12409, 12411], "[checklistCheckpointListProperties, icons]", [25304, 25399], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, currentStep, setTooltipPosition, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, applyHotspotProperties, setStoredTransform]", [28592, 28609], "[elementSelected, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6911, 6958], [5768, 5781], [18827, 18829], "[handleFocus]", [3435, 3461], "[buttonsContainer.buttons, settingAnchorEl.buttonId]", [8833, 8850], [3512, 3541], [3122, 3145], "[btnidss, buttonInfo, currentGuideId, currentStep, settingAnchorEl.value]", [4604, 4606], "[accountId, openSnackbar]", [17324, 17326], "[setElementSelected]"]