{"ast": null, "code": "import React,{useEffect,useState}from'react';import'./ExtensionPopupLoader.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExtensionPopupLoader=_ref=>{let{message=\"QuickAdapt Extension Loading...\",duration=3000,onComplete,position='top-right'}=_ref;const[isVisible,setIsVisible]=useState(true);const[progress,setProgress]=useState(0);useEffect(()=>{// Animate progress bar\nconst progressInterval=setInterval(()=>{setProgress(prev=>{if(prev>=100){clearInterval(progressInterval);return 100;}return prev+100/(duration/50);// Update every 50ms\n});},50);// Auto hide after duration\nconst hideTimer=setTimeout(()=>{setIsVisible(false);if(onComplete){setTimeout(onComplete,300);// Wait for fade out animation\n}},duration);return()=>{clearInterval(progressInterval);clearTimeout(hideTimer);};},[duration,onComplete]);if(!isVisible)return null;return/*#__PURE__*/_jsx(\"div\",{className:`extension-popup-loader ${position} ${isVisible?'visible':'hidden'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"popup-loader-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"popup-loader-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"popup-loader-icon\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"icon-spinner\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner-dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"spinner-dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"spinner-dot\"})]})}),/*#__PURE__*/_jsx(\"button\",{className:\"popup-close-btn\",onClick:()=>setIsVisible(false),\"aria-label\":\"Close\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"popup-loader-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"popup-loader-title\",children:\"QuickAdapt\"}),/*#__PURE__*/_jsx(\"div\",{className:\"popup-loader-message\",children:message}),/*#__PURE__*/_jsx(\"div\",{className:\"popup-progress-container\",children:/*#__PURE__*/_jsx(\"div\",{className:\"popup-progress-bar\",style:{width:`${progress}%`}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"popup-loader-status\",children:[progress<30&&\"Initializing...\",progress>=30&&progress<70&&\"Loading components...\",progress>=70&&progress<100&&\"Almost ready...\",progress>=100&&\"Ready!\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"popup-loader-footer\",children:/*#__PURE__*/_jsx(\"div\",{className:\"extension-badge\",children:\"Extension\"})})]})});};export default ExtensionPopupLoader;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "ExtensionPopup<PERSON><PERSON>der", "_ref", "message", "duration", "onComplete", "position", "isVisible", "setIsVisible", "progress", "setProgress", "progressInterval", "setInterval", "prev", "clearInterval", "hide<PERSON><PERSON>r", "setTimeout", "clearTimeout", "className", "children", "onClick", "style", "width"], "sources": ["E:/Code/Qadpt/quickadapt/QuickAdaptExtension/src/components/common/ExtensionPopupLoader.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ExtensionPopupLoader.css';\n\ninterface ExtensionPopupLoaderProps {\n  message?: string;\n  duration?: number;\n  onComplete?: () => void;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';\n}\n\nconst ExtensionPopupLoader: React.FC<ExtensionPopupLoaderProps> = ({\n  message = \"QuickAdapt Extension Loading...\",\n  duration = 3000,\n  onComplete,\n  position = 'top-right'\n}) => {\n  const [isVisible, setIsVisible] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  useEffect(() => {\n    // Animate progress bar\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          return 100;\n        }\n        return prev + (100 / (duration / 50)); // Update every 50ms\n      });\n    }, 50);\n\n    // Auto hide after duration\n    const hideTimer = setTimeout(() => {\n      setIsVisible(false);\n      if (onComplete) {\n        setTimeout(onComplete, 300); // Wait for fade out animation\n      }\n    }, duration);\n\n    return () => {\n      clearInterval(progressInterval);\n      clearTimeout(hideTimer);\n    };\n  }, [duration, onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className={`extension-popup-loader ${position} ${isVisible ? 'visible' : 'hidden'}`}>\n      <div className=\"popup-loader-container\">\n        {/* Header with icon and close button */}\n        <div className=\"popup-loader-header\">\n          <div className=\"popup-loader-icon\">\n            <div className=\"icon-spinner\">\n              <div className=\"spinner-dot\"></div>\n              <div className=\"spinner-dot\"></div>\n              <div className=\"spinner-dot\"></div>\n            </div>\n          </div>\n          <button \n            className=\"popup-close-btn\"\n            onClick={() => setIsVisible(false)}\n            aria-label=\"Close\"\n          >\n            ×\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"popup-loader-content\">\n          <div className=\"popup-loader-title\">QuickAdapt</div>\n          <div className=\"popup-loader-message\">{message}</div>\n          \n          {/* Progress bar */}\n          <div className=\"popup-progress-container\">\n            <div \n              className=\"popup-progress-bar\"\n              style={{ width: `${progress}%` }}\n            ></div>\n          </div>\n          \n          {/* Status text */}\n          <div className=\"popup-loader-status\">\n            {progress < 30 && \"Initializing...\"}\n            {progress >= 30 && progress < 70 && \"Loading components...\"}\n            {progress >= 70 && progress < 100 && \"Almost ready...\"}\n            {progress >= 100 && \"Ready!\"}\n          </div>\n        </div>\n\n        {/* Extension logo/branding area */}\n        <div className=\"popup-loader-footer\">\n          <div className=\"extension-badge\">Extension</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExtensionPopupLoader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASpC,KAAM,CAAAC,oBAAyD,CAAGC,IAAA,EAK5D,IAL6D,CACjEC,OAAO,CAAG,iCAAiC,CAC3CC,QAAQ,CAAG,IAAI,CACfC,UAAU,CACVC,QAAQ,CAAG,WACb,CAAC,CAAAJ,IAAA,CACC,KAAM,CAACK,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACa,QAAQ,CAAEC,WAAW,CAAC,CAAGd,QAAQ,CAAC,CAAC,CAAC,CAE3CD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAgB,gBAAgB,CAAGC,WAAW,CAAC,IAAM,CACzCF,WAAW,CAACG,IAAI,EAAI,CAClB,GAAIA,IAAI,EAAI,GAAG,CAAE,CACfC,aAAa,CAACH,gBAAgB,CAAC,CAC/B,MAAO,IAAG,CACZ,CACA,MAAO,CAAAE,IAAI,CAAI,GAAG,EAAIT,QAAQ,CAAG,EAAE,CAAE,CAAE;AACzC,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAW,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjCR,YAAY,CAAC,KAAK,CAAC,CACnB,GAAIH,UAAU,CAAE,CACdW,UAAU,CAACX,UAAU,CAAE,GAAG,CAAC,CAAE;AAC/B,CACF,CAAC,CAAED,QAAQ,CAAC,CAEZ,MAAO,IAAM,CACXU,aAAa,CAACH,gBAAgB,CAAC,CAC/BM,YAAY,CAACF,SAAS,CAAC,CACzB,CAAC,CACH,CAAC,CAAE,CAACX,QAAQ,CAAEC,UAAU,CAAC,CAAC,CAE1B,GAAI,CAACE,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACET,IAAA,QAAKoB,SAAS,CAAE,0BAA0BZ,QAAQ,IAAIC,SAAS,CAAG,SAAS,CAAG,QAAQ,EAAG,CAAAY,QAAA,cACvFnB,KAAA,QAAKkB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErCnB,KAAA,QAAKkB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCrB,IAAA,QAAKoB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCnB,KAAA,QAAKkB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BrB,IAAA,QAAKoB,SAAS,CAAC,aAAa,CAAM,CAAC,cACnCpB,IAAA,QAAKoB,SAAS,CAAC,aAAa,CAAM,CAAC,cACnCpB,IAAA,QAAKoB,SAAS,CAAC,aAAa,CAAM,CAAC,EAChC,CAAC,CACH,CAAC,cACNpB,IAAA,WACEoB,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEA,CAAA,GAAMZ,YAAY,CAAC,KAAK,CAAE,CACnC,aAAW,OAAO,CAAAW,QAAA,CACnB,MAED,CAAQ,CAAC,EACN,CAAC,cAGNnB,KAAA,QAAKkB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCrB,IAAA,QAAKoB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,cACpDrB,IAAA,QAAKoB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEhB,OAAO,CAAM,CAAC,cAGrDL,IAAA,QAAKoB,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCrB,IAAA,QACEoB,SAAS,CAAC,oBAAoB,CAC9BG,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGb,QAAQ,GAAI,CAAE,CAC7B,CAAC,CACJ,CAAC,cAGNT,KAAA,QAAKkB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EACjCV,QAAQ,CAAG,EAAE,EAAI,iBAAiB,CAClCA,QAAQ,EAAI,EAAE,EAAIA,QAAQ,CAAG,EAAE,EAAI,uBAAuB,CAC1DA,QAAQ,EAAI,EAAE,EAAIA,QAAQ,CAAG,GAAG,EAAI,iBAAiB,CACrDA,QAAQ,EAAI,GAAG,EAAI,QAAQ,EACzB,CAAC,EACH,CAAC,cAGNX,IAAA,QAAKoB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCrB,IAAA,QAAKoB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,CAC7C,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}